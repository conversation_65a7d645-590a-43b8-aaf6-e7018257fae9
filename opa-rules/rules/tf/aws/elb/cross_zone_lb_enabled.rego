package rules.tf_aws_elb_cross_zone_lb_enabled

import data.fugue
import data.keywords.in


__rego__metadoc__ := {
    "id": "SOC2_R00017",
    "title": "Ensure Elastic Load Balancer cross zone lb is enabled",
    "description": "Checks whether Elastic Load Balancer resorce argument cross_zone_load_balancing is true",
    "custom": {"severity": "Medium"},
}

resource_type := "aws_elb"

default deny = false


deny {
    input.cross_zone_load_balancing == false
}
