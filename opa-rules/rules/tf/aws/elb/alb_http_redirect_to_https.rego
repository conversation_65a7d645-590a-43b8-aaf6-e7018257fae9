package rules.tf_aws_elb_alb_http_redirect_to_https

__rego__metadoc__ := {
	"custom": {
		"controls": {},
		"severity": "Low",
	},
	"description": "Checks whether HTTP to HTTPS redirection is configured on all HTTP listeners of Application Load Balancers. The rule is NON_COMPLIANT if one or more HTTP listeners of Application Load Balancer do not have HTTP to HTTPS redirection configured.",
	"id": "SOC2_R00013",
	"title": "ALB load balancer HTTP redirection to HTTPS should be configured",
}

resource_type = "aws_lb_listener"

default allow = false

allow {
	input.default_action[_].redirect[_].protocol == "HTTPS"
}
