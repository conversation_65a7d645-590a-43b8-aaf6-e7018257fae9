package rules.tf_aws_rds_instance_public_access

import data.fugue

__rego__metadoc__ := {
	"custom": {"severity": "High"},
	"description": "RDS instance 'Publicly Accessible' should not be enabled. Publicly accessible RDS instances allow any AWS user or anonymous user access to the data in the database. RDS instances should not be publicly accessible.",
	"id": "SOC2_R0057",
	"title": "Ensure RDS instance 'Publicly Accessible' should not be enabled",
}

resource_type := "aws_db_instance"

default allow = false

allow {
	input.publicly_accessible == false
}
