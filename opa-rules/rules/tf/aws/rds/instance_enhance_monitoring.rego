package rule.tf_aws_rds_instance_enhance_monitoring

__rego__metadoc__ := {
  "id": "SOC2_R0051",
  "title": "RDS DB instance enhanced monitoring should be enabled.",
  "description": "Enhanced Monitoring requires permission to act on your behalf to send OS metric information to CloudWatch Logs. You grant Enhanced Monitoring permissions using an AWS Identity and Access Management (IAM) role. You can either create this role when you enable Enhanced Monitoring or create it beforehand.",
  "custom": {
    "severity": "Low"
  }
}

resource_type := "aws_db_instance"

default allow = false

allow {
    input.monitoring_role_arn != ""
}