package rules.tf_aws_s3_bucket_object_lock_enabled

import data.fugue

__rego__metadoc__ := {
	"custom": {"severity": "High"},
	"description": "Object Lock is an Amazon S3 feature that blocks object version deletion during a user-defined retention period, to enforce retention policies as an additional layer of data protection and/or for strict regulatory compliance.",
	"id": "SOC2_R0064",
	"title": "S3 bucket object lock should be enabled",
}

resource_type := "aws_s3_bucket"

default allow = false

allow {

  is_boolean(input.object_lock_enabled)
  input.object_lock_enabled == true

}
