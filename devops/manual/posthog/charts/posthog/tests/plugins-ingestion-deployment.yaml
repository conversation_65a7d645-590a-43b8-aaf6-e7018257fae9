suite: PostHog plugins ingestion deployment definition
templates:
  - templates/plugins-ingestion-deployment.yaml
  - templates/secrets.yaml

tests:
  - it: should be empty if pluginsIngestion.enabled is set to false
    template: templates/plugins-ingestion-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsIngestion.enabled: false
    asserts:
      - hasDocuments:
          count: 0

  - it: should have the correct apiVersion
    template: templates/plugins-ingestion-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsIngestion.enabled: true
    asserts:
      - hasDocuments:
          count: 1
      - isAPIVersion:
          of: apps/v1

  - it: should be the correct kind
    template: templates/plugins-ingestion-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsIngestion.enabled: true
    asserts:
      - hasDocuments:
          count: 1
      - isKind:
          of: Deployment

  - it: should have a pod securityContext
    template: templates/plugins-ingestion-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsIngestion.enabled: true
      pluginsIngestion.podSecurityContext.enabled: true
      pluginsIngestion.podSecurityContext.runAsUser: 1001
      pluginsIngestion.podSecurityContext.fsGroup: 2000
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: spec.template.spec.securityContext.runAsUser
          value: 1001
      - equal:
          path: spec.template.spec.securityContext.fsGroup
          value: 2000

  - it: should have a container securityContext
    template: templates/plugins-ingestion-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsIngestion.enabled: true
      pluginsIngestion.securityContext.enabled: true
      pluginsIngestion.securityContext.runAsUser: 1001
      pluginsIngestion.securityContext.allowPrivilegeEscalation: false
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: spec.template.spec.containers[0].securityContext.runAsUser
          value: 1001
      - equal:
          path: spec.template.spec.containers[0].securityContext.allowPrivilegeEscalation
          value: false

  - it: should not have a pod securityContext
    template: templates/plugins-ingestion-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsIngestion.enabled: true
      pluginsIngestion.podSecurityContext.enabled: false
    asserts:
      - hasDocuments:
          count: 1
      - isEmpty:
          path: spec.template.spec.securityContext
          value: 1001

  - it: should not have a container securityContext
    template: templates/plugins-ingestion-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsIngestion.enabled: true
      pluginsIngestion.securityContext.enabled: false
    asserts:
      - hasDocuments:
          count: 1
      - isEmpty:
          path: spec.template.spec.containers[0].securityContext

  - it: sets PLUGIN_SERVER_MODE
    template: templates/plugins-ingestion-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsIngestion.enabled: true
    asserts:
      - contains:
          path: spec.template.spec.containers[0].env
          content:
            name: PLUGIN_SERVER_MODE
            value: ingestion

  - it: sets INGESTION_OVERFLOW_ENABLED to false by default
    template: templates/plugins-ingestion-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsIngestion.enabled: true
    asserts:
      - contains:
          path: spec.template.spec.containers[0].env
          content:
            name: INGESTION_OVERFLOW_ENABLED
            value: "false"

  - it: can set INGESTION_OVERFLOW_ENABLED to true
    template: templates/plugins-ingestion-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsIngestion:
        enabled: true
        env:
          - name: INGESTION_OVERFLOW_ENABLED
            value: "true"
    asserts:
      - contains:
          path: spec.template.spec.containers[0].env
          content:
            name: INGESTION_OVERFLOW_ENABLED
            value: "true"

  - it: sets SENTRY_DSN env var
    template: templates/plugins-ingestion-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsIngestion.enabled: true
      sentryDSN: main.endpoint
      pluginsIngestion.sentryDSN: pluginsIngestion.endpoint
    asserts:
      - contains:
          path: spec.template.spec.containers[0].env
          content:
            name: SENTRY_DSN
            value: pluginsIngestion.endpoint

  - it: sets SENTRY_DSN env var with default
    template: templates/plugins-ingestion-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsIngestion.enabled: true
      sentryDSN: main.endpoint
    asserts:
      - contains:
          path: spec.template.spec.containers[0].env
          content:
            name: SENTRY_DSN
            value: main.endpoint

  - it: allows setting imagePullSecrets
    template: templates/plugins-ingestion-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local
      pluginsIngestion.enabled: true
      image.pullSecrets: [secret]
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: spec.template.spec.imagePullSecrets
          value: [name: secret]
