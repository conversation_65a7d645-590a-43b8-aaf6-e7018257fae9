{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}, {"datasource": {"type": "loki", "uid": "P8E80F9AEF21F6940"}, "enable": true, "expr": "{namespace=\"posthog\", container=~\"grafana-annotation-job-.*\"}", "hide": false, "iconColor": "dark-blue", "name": "Deploy / Rollback", "tagKeys": "container", "target": {}, "titleFormat": ""}]}, "description": "", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": 2, "iteration": 1644853610973, "links": [], "liveNow": false, "panels": [{"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-red", "value": null}, {"color": "dark-orange", "value": 60}, {"color": "dark-green", "value": 3600}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 0}, "id": 18, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "time() - pg_postmaster_start_time_seconds{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Uptime", "type": "stat"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 0}, "id": 2, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {"titleSize": 30, "valueSize": 24}, "textMode": "auto"}, "pluginVersion": "8.3.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "sum(irate(pg_stat_database_xact_commit{instance=~\"$instance\"}[$interval])) + \nsum(irate(pg_stat_database_xact_rollback{instance=~\"$instance\"}[$interval]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "QPS", "type": "stat"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 9, "x": 6, "y": 0}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "irate(pg_stat_database_xact_commit{instance=\"$instance\", datname=\"posthog\"}[5m])", "interval": "", "legendFormat": "Commits", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "irate(pg_stat_database_xact_rollback{instance=\"$instance\", datname=\"posthog\"}[5m])", "hide": false, "interval": "", "legendFormat": "Rollbacks", "refId": "B"}], "title": "Transactions", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 9, "x": 15, "y": 0}, "id": 22, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "sum(\n  rate(pg_slow_queries{instance=\"$instance\"}[$__interval])\n)", "interval": "", "legendFormat": "Slow queries", "refId": "A"}], "title": "Slow queries", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 60}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 0, "y": 3}, "id": 16, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.3.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "sum(avg_over_time(pg_stat_activity_count{instance=\"$instance\"}[$__interval])) /\nsum(pg_settings_max_connections{instance=\"$instance\"})", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Connections", "type": "gauge"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "dark-red", "value": null}, {"color": "red", "value": 80}, {"color": "#EAB839", "value": 90}, {"color": "dark-green", "value": 95}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 3, "y": 3}, "id": 21, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.3.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "pg_stat_database_blks_hit{instance=\"$instance\", datname=\"posthog\"} / (pg_stat_database_blks_read{instance=\"$instance\", datname=\"posthog\"} + pg_stat_database_blks_hit{instance=\"$instance\", datname=\"posthog\"})", "interval": "", "legendFormat": "", "refId": "A"}], "title": "<PERSON><PERSON> hit", "type": "stat"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 7}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "sum(irate(pg_stat_database_tup_inserted{instance=\"$instance\"}[$interval]))", "interval": "", "legendFormat": "Inserted", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "sum(irate(pg_stat_database_tup_fetched{instance=\"$instance\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "Fetched", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "sum(irate(pg_stat_database_tup_deleted{instance=\"$instance\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "Deleted", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "sum(irate(pg_stat_database_tup_updated{instance=\"$instance\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "Updated", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "sum(irate(pg_stat_database_tup_returned{instance=\"$instance\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "Returned", "refId": "E"}], "title": "Rows", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 7}, "id": 20, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "sum by (mode) (\n  avg_over_time(pg_locks_count{instance=\"$instance\", datname=\"$database\"}[$__interval])\n)", "interval": "", "legendFormat": "{{mode}}", "refId": "A"}], "title": "Locks", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 15}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "sum(rate(pg_stat_database_deadlocks{instance=\"$instance\"}[$interval]))", "interval": "", "legendFormat": "Deadlocks", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "sum(rate(pg_stat_database_conflicts{instance=\"$instance\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "Conflicts", "refId": "B"}], "title": "Conflicts / Deadlocks", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Max connections"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 15}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "sum by (state) (pg_stat_activity_count{instance=\"$instance\"})", "interval": "", "legendFormat": "{{state}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "pg_settings_max_connections{instance=\"$instance\"}", "hide": false, "interval": "", "legendFormat": "Max connections", "refId": "B"}], "title": "Connection (by state)", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 2, "mappings": [], "max": 1, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 22}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "pg_stat_database_blks_hit{instance=\"$instance\", datname=\"posthog\"} / (pg_stat_database_blks_read{instance=\"$instance\", datname=\"posthog\"} + pg_stat_database_blks_hit{instance=\"$instance\", datname=\"posthog\"})", "interval": "", "legendFormat": "Hit rate", "refId": "A"}], "title": "<PERSON><PERSON>", "type": "timeseries"}], "schemaVersion": 34, "style": "dark", "tags": ["PostHog", "PostgreSQL"], "templating": {"list": [{"auto": false, "auto_count": 30, "auto_min": "10s", "current": {"selected": false, "text": "5m", "value": "5m"}, "hide": 0, "label": "Interval", "name": "interval", "options": [{"selected": false, "text": "1m", "value": "1m"}, {"selected": true, "text": "5m", "value": "5m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}, {"selected": false, "text": "7d", "value": "7d"}, {"selected": false, "text": "14d", "value": "14d"}, {"selected": false, "text": "30d", "value": "30d"}], "query": "1m,5m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "queryValue": "", "refresh": 2, "skipUrlSync": false, "type": "interval"}, {"current": {"selected": false, "text": "**********:9187", "value": "**********:9187"}, "definition": "label_values(pg_up{}, instance)", "hide": 0, "includeAll": false, "label": "Instance", "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(pg_up{}, instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "postgres", "value": "postgres"}, "definition": "label_values(datname)", "hide": 0, "includeAll": false, "label": "Database", "multi": false, "name": "database", "options": [], "query": {"query": "label_values(datname)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {}, "timezone": "utc", "title": "PostgreSQL", "uid": "postgresql", "version": 1, "weekStart": "monday"}