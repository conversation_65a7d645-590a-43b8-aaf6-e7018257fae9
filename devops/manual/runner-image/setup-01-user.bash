#!/usr/bin/env bash

set -o errexit -o xtrace

# Install python3 and virtualenv (taken care of by actions)
#sudo apt install -y python3 python3-pip
#sudo update-alternatives --install /usr/bin/python python /usr/bin/python3 10
#sudo pip3 install virtualenv

# Install a specific version of Google chrome, because unfortunately version
# 117 headless does not work with cypress.  Using 116 for now.
wget --no-verbose -O /tmp/chrome.deb https://chrome-test-version.s3.us-west-2.amazonaws.com/google-chrome-stable_116.0.5845.187-1_amd64.deb
sudo apt install -y /tmp/chrome.deb
rm /tmp/chrome.deb

# This is how you are supposed to install the latest chrome
#wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
#sudo bash -c 'echo "deb http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google.list'
#sudo apt-get update
#sudo apt-get install -y google-chrome-stable

# Already installed sentry-cli
#curl -sL https://sentry.io/get-cli/ | sudo bash

# Install asdf, node and yarn (taken care of by actions)
#git clone https://github.com/asdf-vm/asdf.git $HOME/.asdf --branch v0.12.0
#echo ". $HOME/.asdf/asdf.sh" >> $HOME/.bashrc
#. $HOME/.asdf/asdf.sh
#asdf plugin add nodejs
#asdf plugin add direnv
#asdf install nodejs 18.17.1
#asdf install nodejs 14.20.1
#echo "nodejs 18.17.1" > $HOME/.tool-versions
#npm install -g yarn

# Install Docker (already in actions runner)
#curl -fsSL https://get.docker.com/ -o get-docker.sh
#sh get-docker.sh
#sudo usermod -aG docker runner

# Install kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh"  | bash
sudo install -o root -g root -m 0755 kustomize /usr/local/bin/kustomize

# Install awsv2
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Up file watcher limit
sudo sh -c "echo fs.inotify.max_user_watches=524288 > /etc/sysctl.d/40-max-user-watches.conf"
sudo sysctl --system
