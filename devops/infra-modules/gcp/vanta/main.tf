# Set up GCP provider
terraform {
  required_providers {
    google = {
      source = "hashicorp/google"
    }
    local = {
      source  = "hashicorp/local"
      version = "~> 2.0"
    }
  }
}

provider "google" {}

locals {
  subject_uri          = "principal://iam.googleapis.com/projects/${data.google_project.vanta_scanner.number}/locations/global/workloadIdentityPools/${local.identity_pool_id}/subject/${local.subject_name}"
  identity_pool_id     = "vanta-a3fbdd59ca42c60"
  subject_name         = "vanta-scanner"
  aws_role_name        = "scanner"
  identity_provider_id = "vanta-aws"
}

# Project IDs populated from step 1
variable "projects" {
  type = list(string)
  default = [
    "intrepid-nova-444311-b5",
    "production-network-442916",
    "registries-442916",
    "tfstate-442916",
    "platform-442916",
  ]
}

variable "vanta_project_target_folder_id" {
  type        = string
  description = "the folder in which vanta project will be created"
}

variable "vanta_project_billing_account" {
  type        = string
  description = "the billing account to attatch vanta project to"
  default     = null
}

variable "org_id" {
  type        = string
  description = "the gcp org_id that the project will be attached to"
  default     = null
}

# Create the vanta-scanner project that will host the workload identity pool and enable the APIs
resource "google_project" "vanta_scanner" {
  name = "vanta-scanner"
  # Change the id below if it already exists
  project_id      = "plasmic-vanta-scanner"
  org_id          = var.vanta_project_target_folder_id != null ? null : var.org_id
  folder_id       = var.vanta_project_target_folder_id
  billing_account = var.vanta_project_billing_account
}

data "google_project" "vanta_scanner" {
  project_id = google_project.vanta_scanner.project_id
}

# Enable the required APIs using dynamic resource creation
variable "requiredAPIs" {
  type = list(string)
  default = [
    "bigquery.googleapis.com",
    "cloudasset.googleapis.com",
    "cloudresourcemanager.googleapis.com",
    "containeranalysis.googleapis.com",
    "essentialcontacts.googleapis.com",
    "firestore.googleapis.com",
    "iam.googleapis.com",
    "serviceusage.googleapis.com",
    "sqladmin.googleapis.com",
    "logging.googleapis.com",
    "monitoring.googleapis.com",
    "pubsub.googleapis.com",
    "storage-api.googleapis.com",
    "iamcredentials.googleapis.com",
    "sts.googleapis.com"
  ]
}

resource "google_project_service" "enabled_apis" {
  for_each = toset(var.requiredAPIs)
  project  = data.google_project.vanta_scanner.project_id
  service  = each.key
}

# Create custom roles
resource "google_organization_iam_custom_role" "vanta_project_scanner_role" {
  # Creates the VantaProjectScanner role
  org_id      = var.org_id
  role_id     = "VantaProjectScanner"
  title       = "Vanta Project Scanner"
  description = "Role for listing project resources with configuration metadata"
  permissions = [
    "resourcemanager.projects.get",
    "bigquery.datasets.get",
    "compute.instances.get",
    "compute.instances.getEffectiveFirewalls",
    "compute.subnetworks.get",
    "pubsub.topics.get",
    "storage.buckets.get",
    "cloudasset.assets.searchAllResources",
  ]
}

# These permissions on the organization are optional
# If omitted, we will not be able to fetch essential contacts at the organization level and inherited roles and their bindings
resource "google_organization_iam_custom_role" "vanta_org_scanner_role" {
  # Creates the VantaOrganizationScanner role
  org_id      = var.org_id
  role_id     = "VantaOrganizationScanner"
  title       = "Vanta Organization Scanner"
  description = "Role for listing inherited IAM policies"
  permissions = [
    "essentialcontacts.contacts.list",
    "iam.roles.list",
    "resourcemanager.organizations.getIamPolicy",
    "resourcemanager.folders.getIamPolicy",
  ]
}

# Wait for the project to be created
resource "time_sleep" "wait_for_vanta_scanner_project_creation" {
  depends_on      = [google_project.vanta_scanner]
  create_duration = "90s"
}
# Create the Workload Identity Pool
resource "google_iam_workload_identity_pool" "vanta_identity_pool" {
  depends_on                = [time_sleep.wait_for_vanta_scanner_project_creation]
  project                   = data.google_project.vanta_scanner.project_id
  workload_identity_pool_id = local.identity_pool_id
  display_name              = "Vanta"
}
# Wait for the pool to be created
resource "time_sleep" "wait_for_vanta_scanner_pool_creation" {
  depends_on      = [google_iam_workload_identity_pool.vanta_identity_pool]
  create_duration = "30s"
}
# Create the Workload Identity Provider
resource "google_iam_workload_identity_pool_provider" "vanta_identity_provider" {
  depends_on                         = [time_sleep.wait_for_vanta_scanner_pool_creation]
  project                            = data.google_project.vanta_scanner.project_id
  workload_identity_pool_id          = google_iam_workload_identity_pool.vanta_identity_pool.workload_identity_pool_id
  workload_identity_pool_provider_id = local.identity_provider_id
  display_name                       = "Vanta AWS"
  attribute_mapping = {
    "google.subject" = "\"${local.subject_name}\""
    "attribute.arn"  = "assertion.arn"
  }
  attribute_condition = "attribute.arn.extract('assumed-role/{role}/') == '${local.aws_role_name}'"
  aws {
    account_id = "************"
  }
}

# Grant VantaOrganizationScanner role to the scanner principal at the organization level
resource "google_organization_iam_binding" "vanta_org_binding" {
  depends_on = [google_iam_workload_identity_pool_provider.vanta_identity_provider]
  org_id     = var.org_id
  role       = "organizations/${var.org_id}/roles/VantaOrganizationScanner"
  members = [
    local.subject_uri
  ]
}

# Grant VantaProjectScanner role to the scanner principal for each project
resource "google_project_iam_binding" "vanta_project_bindings" {
  for_each   = toset(var.projects)
  depends_on = [google_iam_workload_identity_pool_provider.vanta_identity_provider]
  project    = each.key
  role       = "organizations/${var.org_id}/roles/VantaProjectScanner"
  members = [
    local.subject_uri
  ]
}

# Grant iam.securityReviewer role to the scanner principal in vanta_scanner project
resource "google_project_iam_member" "vanta_scanner_iam_security_reviewer" {
  depends_on = [google_iam_workload_identity_pool_provider.vanta_identity_provider]
  project    = data.google_project.vanta_scanner.project_id
  role       = "roles/iam.securityReviewer"
  member     = local.subject_uri
}

resource "google_project_iam_member" "vanta_scanner_iam_project_scanner" {
  depends_on = [google_iam_workload_identity_pool_provider.vanta_identity_provider]
  project    = data.google_project.vanta_scanner.project_id
  role       = "organizations/${var.org_id}/roles/VantaProjectScanner"
  member     = local.subject_uri
}

resource "google_project_iam_member" "iam_security_reviewer" {
  for_each   = toset(var.projects)
  depends_on = [google_iam_workload_identity_pool_provider.vanta_identity_provider]
  project    = each.key
  role       = "roles/iam.securityReviewer"
  member     = local.subject_uri
}

# Wait for the pool to be created
resource "time_sleep" "wait_for_changes_to_propagate" {
  depends_on      = [time_sleep.wait_for_vanta_scanner_pool_creation]
  create_duration = "60s"
}

output "vanta_gcp_project_number" {
  value = data.google_project.vanta_scanner.number
}
