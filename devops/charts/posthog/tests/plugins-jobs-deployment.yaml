suite: PostHog plugins jobs deployment definition
templates:
  - templates/plugins-jobs-deployment.yaml
  - templates/secrets.yaml

tests:
  - it: should be empty if pluginsJobs.enabled is set to false
    template: templates/plugins-jobs-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsJobs.enabled: false
    asserts:
      - hasDocuments:
          count: 0

  - it: should have the correct apiVersion
    template: templates/plugins-jobs-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsJobs.enabled: true
    asserts:
      - hasDocuments:
          count: 1
      - isAPIVersion:
          of: apps/v1

  - it: should be the correct kind
    template: templates/plugins-jobs-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsJobs.enabled: true
    asserts:
      - hasDocuments:
          count: 1
      - isKind:
          of: Deployment

  - it: should have a pod securityContext
    template: templates/plugins-jobs-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsJobs.enabled: true
      pluginsJobs.podSecurityContext.enabled: true
      pluginsJobs.podSecurityContext.runAsUser: 1001
      pluginsJobs.podSecurityContext.fsGroup: 2000
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: spec.template.spec.securityContext.runAsUser
          value: 1001
      - equal:
          path: spec.template.spec.securityContext.fsGroup
          value: 2000

  - it: should have a container securityContext
    template: templates/plugins-jobs-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsJobs.enabled: true
      pluginsJobs.securityContext.enabled: true
      pluginsJobs.securityContext.runAsUser: 1001
      pluginsJobs.securityContext.allowPrivilegeEscalation: false
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: spec.template.spec.containers[0].securityContext.runAsUser
          value: 1001
      - equal:
          path: spec.template.spec.containers[0].securityContext.allowPrivilegeEscalation
          value: false

  - it: should not have a pod securityContext
    template: templates/plugins-jobs-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsJobs.enabled: true
      pluginsJobs.podSecurityContext.enabled: false
    asserts:
      - hasDocuments:
          count: 1
      - isEmpty:
          path: spec.template.spec.securityContext
          value: 1001

  - it: should not have a container securityContext
    template: templates/plugins-jobs-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsJobs.enabled: true
      pluginsJobs.securityContext.enabled: false
    asserts:
      - hasDocuments:
          count: 1
      - isEmpty:
          path: spec.template.spec.containers[0].securityContext

  - it: sets PLUGIN_SERVER_MODE
    template: templates/plugins-jobs-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsJobs.enabled: true
    asserts:
      - contains:
          path: spec.template.spec.containers[0].env
          content:
            name: PLUGIN_SERVER_MODE
            value: jobs

  - it: sets SENTRY_DSN env var
    template: templates/plugins-jobs-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsJobs.enabled: true
      sentryDSN: main.endpoint
      pluginsJobs.sentryDSN: pluginsJobs.endpoint
    asserts:
      - contains:
          path: spec.template.spec.containers[0].env
          content:
            name: SENTRY_DSN
            value: pluginsJobs.endpoint

  - it: sets SENTRY_DSN env var with default
    template: templates/plugins-jobs-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local # TODO: remove once secrets.yaml will be fixed/removed
      pluginsJobs.enabled: true
      sentryDSN: main.endpoint
    asserts:
      - contains:
          path: spec.template.spec.containers[0].env
          content:
            name: SENTRY_DSN
            value: main.endpoint

  - it: allows setting imagePullSecrets
    template: templates/plugins-jobs-deployment.yaml # TODO: remove once secrets.yaml will be fixed/removed
    set:
      cloud: local
      pluginsJobs.enabled: true
      image.pullSecrets: [secret]
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: spec.template.spec.imagePullSecrets
          value: [name: secret]
