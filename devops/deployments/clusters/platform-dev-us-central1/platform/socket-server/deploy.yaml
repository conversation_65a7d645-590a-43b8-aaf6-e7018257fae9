apiVersion: apps/v1
kind: Deployment
metadata:
  name: socket
  namespace: platform
spec:
  selector:
    matchLabels:
      app: socket
  # Wait for 20 minutes for deployment to finish; most of the time is spent
  # pulling the image down from ecr
  progressDeadlineSeconds: 1200
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  replicas: 1
  template:
    metadata:
      labels:
        app: socket
      annotations:
        prometheus.io/scrape: "true"
    spec:
      securityContext:
        fsGroup: 1000
      containers:
        - name: app-server
          image: us-central1-docker.pkg.dev/registries-442916/plasmic-prod-us-central1/codegen:dev-85-7a2c47
          # Would love to figure out how to clean this up.
          command: ["bash", "-lc", "cp ~/pgpass ~/.pgpass && chmod 600 ~/.pgpass && MAX_HEAP_SIZE=800 ./internal/scripts/start_backend.sh socket"]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3004
          env:
            - name: SOCKET_PORT
              value: "3004"
            - name: NODE_ENV
              value: production
            - name: HOST
              value: https://studio.dev.plasmic.app
            - name: DATABASE_URI
              value: postgres://wab@postgres-wab-cluster/wab
            - name: SESSION_SECRET
              valueFrom:
                secretKeyRef:
                  name: prod-secrets
                  key: session-secret
            - name: SENTRY_DSN
              value: "https://<EMAIL>/4505399278567425"
            - name: S3_ENDPOINT
              value: https://storage.googleapis.com
          resources:
            requests:
              cpu: "100m"
              memory: "1Gi"
            limits:
              memory: "1Gi"
          readinessProbe:
            httpGet:
              path: /healthcheck
              port: 3004
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
          volumeMounts:
            # Names must match the volume names below
            - name: secrets-volume
              mountPath: /home/<USER>/pgpass
              subPath: pgpass
            - name: secrets-volume
              mountPath: /home/<USER>/.plasmic/secrets.json
              subPath: secrets.json
            - name: secrets-volume
              mountPath: /home/<USER>/.aws/credentials
              subPath: appserver-aws-credentials
      dnsConfig:
        # Reduce DNS queries traffic:
        # https://pracucci.com/kubernetes-dns-resolution-ndots-options-and-why-it-may-affect-application-performances.html
        options:
          - name: ndots
            value: "2"
      terminationGracePeriodSeconds: 30
      volumes:
        - name: secrets-volume
          secret:
            secretName: prod-secrets
            # chmod 600 - but this doesn't seem to be working?
            defaultMode: 384
