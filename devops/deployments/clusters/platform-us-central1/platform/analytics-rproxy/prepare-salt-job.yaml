apiVersion: batch/v1
kind: CronJob
metadata:
  name: prepare-salt-job
spec:
  schedule: "55 * * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: prepare-salt-container
            image: us-central1-docker.pkg.dev/registries-442916/plasmic-prod-us-central1/analytics-rproxy:latest
            command: ["node", "/app/dist/entry-prepare-salt.js"]
          restartPolicy: OnFailure
