locals {
  org_variables   = read_terragrunt_config(find_in_parent_folders("globals.hcl"))
  env_variables   = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  folder_id       = local.env_variables.locals.folder_id
  org_id          = local.org_variables.locals.org_id
  billing_account = local.org_variables.locals.billing_account
}

terraform {
  source = "**************:plasmicapp/plasmic-internal.git//devops/infra-modules/gcp/vanta?ref=master"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  org_id                         = local.org_id
  vanta_project_target_folder_id = local.folder_id
  vanta_project_billing_account  = local.billing_account
}
