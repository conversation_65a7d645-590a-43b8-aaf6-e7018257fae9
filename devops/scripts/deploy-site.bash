#!/usr/bin/env bash

set -o errexit -o nounset -o pipefail

dir="$(dirname "$0")"

deploy() {
  local tag=$1
  gcloud storage cp -r --quiet gs://studio-plasmic-app/versions/$tag/* gs://studio-plasmic-app/
  gcloud storage cp -r --quiet gs://studio-plasmic-app/versions/$tag/index.html gs://studio-plasmic-app/index.html --cache-control 'max-age=0, s-maxage=31536000'
  gcloud storage cp -r --quiet gs://studio-plasmic-app/versions/$tag/static/js/studio.js gs://studio-plasmic-app/static/js/studio.js --cache-control 'max-age=0, s-maxage=31536000'
  gcloud compute url-maps invalidate-cdn-cache platform-url-map --path "/index.html" --async --project platform-442916
  gcloud compute url-maps invalidate-cdn-cache platform-url-map --path "/static/js/studio.js" --async --project platform-442916
}

"$@"
