{"branches": [{"id": "jJhYw1pP4mnJVCFrhiNsk9", "name": "branch"}], "pkgVersions": [{"id": "92029f97-7e74-40be-9276-7b932cd19305", "data": {"root": "xHLk5sYdKoX_", "map": {"rtH3qI7L415j": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "1fLFumwuth8K": {"name": "Default Typography", "rs": {"__ref": "rtH3qI7L415j"}, "preview": null, "uuid": "sagEmhwjYOTv", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "BVAkgseP-CCN": {"values": {}, "mixins": [], "__type": "RuleSet"}, "SP2PWWxkt0m2": {"rs": {"__ref": "BVAkgseP-CCN"}, "__type": "ThemeLayoutSettings"}, "cvvQFii1k4BV": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "N-pVtSn_n0zF": {"name": "Default \"h1\"", "rs": {"__ref": "cvvQFii1k4BV"}, "preview": null, "uuid": "a6mQakstSSb4", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "TSbaacmeT3pO": {"selector": "h1", "style": {"__ref": "N-pVtSn_n0zF"}, "__type": "ThemeStyle"}, "7gC2lqy7T1BZ": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "lbegziOdqTpT": {"name": "Default \"h2\"", "rs": {"__ref": "7gC2lqy7T1BZ"}, "preview": null, "uuid": "Fqj9l7eVvVRQ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Rt6Kp90e90am": {"selector": "h2", "style": {"__ref": "lbegziOdqTpT"}, "__type": "ThemeStyle"}, "jUxQ_rCDUH-R": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "hveAsZ0hEnq6": {"name": "Default \"h3\"", "rs": {"__ref": "jUxQ_rCDUH-R"}, "preview": null, "uuid": "zE0GdkrnVwD7", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "n_wYcbdSBu5c": {"selector": "h3", "style": {"__ref": "hveAsZ0hEnq6"}, "__type": "ThemeStyle"}, "f-JVl75nBMC6": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "HtFfryIXI-43": {"name": "Default \"h4\"", "rs": {"__ref": "f-JVl75nBMC6"}, "preview": null, "uuid": "KUFwR3EAOnM0", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "iuFFhWvNtxoy": {"selector": "h4", "style": {"__ref": "HtFfryIXI-43"}, "__type": "ThemeStyle"}, "X86rGAeSOUMf": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "XJfgBHB02oRq": {"name": "Default \"h5\"", "rs": {"__ref": "X86rGAeSOUMf"}, "preview": null, "uuid": "DdIdJg54vX8n", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ts3_I66D1MPI": {"selector": "h5", "style": {"__ref": "XJfgBHB02oRq"}, "__type": "ThemeStyle"}, "oPuOJF1qFR8k": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "5R0CNHcUANBU": {"name": "Default \"h6\"", "rs": {"__ref": "oPuOJF1qFR8k"}, "preview": null, "uuid": "Jo3U8t7XTYPO", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Ci54heeaBkw4": {"selector": "h6", "style": {"__ref": "5R0CNHcUANBU"}, "__type": "ThemeStyle"}, "s4epEyOGiffY": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "bGRWpAemJxYO": {"name": "Default \"a\"", "rs": {"__ref": "s4epEyOGiffY"}, "preview": null, "uuid": "VEpAY9G4fAsp", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "2H6QtOHDSeuu": {"selector": "a", "style": {"__ref": "bGRWpAemJxYO"}, "__type": "ThemeStyle"}, "39P_5NDX3Yg4": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "1g9-hvXSp-H6": {"name": "Default \"a:hover\"", "rs": {"__ref": "39P_5NDX3Yg4"}, "preview": null, "uuid": "DrtH_8PmDu0U", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "1mgTigoScETJ": {"selector": "a:hover", "style": {"__ref": "1g9-hvXSp-H6"}, "__type": "ThemeStyle"}, "l-7somvmAiFg": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "ekZQ9vXL8RHe": {"name": "Default \"blockquote\"", "rs": {"__ref": "l-7somvmAiFg"}, "preview": null, "uuid": "nDc0dI9YwsuK", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "umpOYLMj3IBi": {"selector": "blockquote", "style": {"__ref": "ekZQ9vXL8RHe"}, "__type": "ThemeStyle"}, "EVeK8uNZ3fp3": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "NbfGIq7obiGd": {"name": "Default \"code\"", "rs": {"__ref": "EVeK8uNZ3fp3"}, "preview": null, "uuid": "okl61CsLJAw7", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Af8xBi5A8a8g": {"selector": "code", "style": {"__ref": "NbfGIq7obiGd"}, "__type": "ThemeStyle"}, "Og8vedb-uqkf": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "rxCOi8l7bi78": {"name": "Default \"pre\"", "rs": {"__ref": "Og8vedb-uqkf"}, "preview": null, "uuid": "BRhb2LGnCjLb", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "A7D7wzAv3qG-": {"selector": "pre", "style": {"__ref": "rxCOi8l7bi78"}, "__type": "ThemeStyle"}, "dbYyBrnzs3d3": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "9FqB8TLizCkB": {"name": "Default \"ol\"", "rs": {"__ref": "dbYyBrnzs3d3"}, "preview": null, "uuid": "FzB8VfyIKsrh", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "IzRxdWXfI6Us": {"selector": "ol", "style": {"__ref": "9FqB8TLizCkB"}, "__type": "ThemeStyle"}, "EqD4k-fz2vwx": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "XK0EIgLTvWt4": {"name": "Default \"ul\"", "rs": {"__ref": "EqD4k-fz2vwx"}, "preview": null, "uuid": "F6CBg_hTZRJ6", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "BbvvUybfhLEe": {"selector": "ul", "style": {"__ref": "XK0EIgLTvWt4"}, "__type": "ThemeStyle"}, "9KCiiDBSorcv": {"defaultStyle": {"__ref": "1fLFumwuth8K"}, "styles": [{"__ref": "TSbaacmeT3pO"}, {"__ref": "Rt6Kp90e90am"}, {"__ref": "n_wYcbdSBu5c"}, {"__ref": "iuFFhWvNtxoy"}, {"__ref": "ts3_I66D1MPI"}, {"__ref": "Ci54heeaBkw4"}, {"__ref": "2H6QtOHDSeuu"}, {"__ref": "1mgTigoScETJ"}, {"__ref": "umpOYLMj3IBi"}, {"__ref": "Af8xBi5A8a8g"}, {"__ref": "A7D7wzAv3qG-"}, {"__ref": "IzRxdWXfI6Us"}, {"__ref": "BbvvUybfhLEe"}], "layout": {"__ref": "SP2PWWxkt0m2"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "8nBYE85F8tK3": {"name": "text", "__type": "Text"}, "WhnHtmZ_iv-x": {"name": "Screen", "uuid": "9gJVhZx78hqY", "__type": "Var"}, "z4QMZMa2zo00": {"type": {"__ref": "8nBYE85F8tK3"}, "variable": {"__ref": "WhnHtmZ_iv-x"}, "uuid": "fKh5bjBgOKLd", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "sabKQqOjEw-m": {"type": "global-screen", "param": {"__ref": "z4QMZMa2zo00"}, "uuid": "xZmoAniT4wNV", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "BM3CyjR6PREH": {"uuid": "I7_XstFXzjGo", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Mgq9mDrXxf1Y": {"components": [{"__ref": "gcp-I4c9H1d1"}, {"__ref": "b3kNX_TdMIFo"}, {"__ref": "XHMug2V6u6yT"}], "arenas": [], "pageArenas": [], "componentArenas": [{"__ref": "mYcmyyF9lJfB"}], "globalVariantGroups": [{"__ref": "sabKQqOjEw-m"}], "userManagedFonts": [], "globalVariant": {"__ref": "BM3CyjR6PREH"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "9KCiiDBSorcv"}], "activeTheme": {"__ref": "9KCiiDBSorcv"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "sabKQqOjEw-m"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "gcp-I4c9H1d1": {"uuid": "U_fhnoosZ7Db", "name": "hostless-plasmic-head", "params": [{"__ref": "BhZY-ZzTkguj"}, {"__ref": "wZ0sdohDIPEm"}, {"__ref": "hiJsGCL2mJ5Q"}, {"__ref": "57VTsyCYgG-F"}], "states": [], "tplTree": {"__ref": "F167GOzF4and"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "I3Bh_D87PXms"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "4BuFqnuQppTZ"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "b3kNX_TdMIFo": {"uuid": "zZQVXzbZDClb", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "s6tO-w_oxl8R"}, {"__ref": "-0y9FETBa5Ml"}, {"__ref": "YGv2gd6Y420z"}, {"__ref": "lzLCvqozElaP"}, {"__ref": "o7PWQIHjQtly"}], "states": [], "tplTree": {"__ref": "UM5nkp0liC3Y"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "BnExkTBlWt3X"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "PovgJWLYYDEa"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component"}, "BhZY-ZzTkguj": {"type": {"__ref": "0pGeXrADFz6E"}, "variable": {"__ref": "UaN1AFzlCTTm"}, "uuid": "yrsRqvHfPTBq", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "wZ0sdohDIPEm": {"type": {"__ref": "f1bppQceBhIc"}, "variable": {"__ref": "QRAvMZ69oS17"}, "uuid": "5s16PGVHnb5z", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "hiJsGCL2mJ5Q": {"type": {"__ref": "wcee0p59WHO_"}, "variable": {"__ref": "O6gV9maQ3H6S"}, "uuid": "xECPLI4sIZow", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "57VTsyCYgG-F": {"type": {"__ref": "gbWOsB0DM410"}, "variable": {"__ref": "Gft7UMJNIu4u"}, "uuid": "7gARgRtAAamA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "F167GOzF4and": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "qVgxiRh0YS9W", "parent": null, "locked": null, "vsettings": [{"__ref": "3vSsjE3JCnBW"}], "__type": "TplTag"}, "I3Bh_D87PXms": {"uuid": "fBa5fK4AkXlF", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "4BuFqnuQppTZ": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "s6tO-w_oxl8R": {"type": {"__ref": "kXWE95a_KQen"}, "variable": {"__ref": "o05oSKcSV_1n"}, "uuid": "IDMn4tmgA12n", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "-0y9FETBa5Ml": {"type": {"__ref": "xf1HBIXAOdQC"}, "variable": {"__ref": "-CN3Q9pgMDCr"}, "uuid": "ayoSsFneIxAe", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "YGv2gd6Y420z": {"type": {"__ref": "NWmGpsLzSYM_"}, "tplSlot": {"__ref": "Dm15C1lfrHgk"}, "variable": {"__ref": "TI8vBek4XK6R"}, "uuid": "O-AxbHZWOeKG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "lzLCvqozElaP": {"type": {"__ref": "PUKNtlyX4GUx"}, "variable": {"__ref": "bzHAakq11OlZ"}, "uuid": "6W-PGaYccBo2", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "o7PWQIHjQtly": {"type": {"__ref": "_0RzfhFZzy13"}, "variable": {"__ref": "MjnaSp7ouUTr"}, "uuid": "-t5pIdRQIuMI", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "UM5nkp0liC3Y": {"tag": "div", "name": null, "children": [{"__ref": "Dm15C1lfrHgk"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "BuNYC8Bqk0ji", "parent": null, "locked": null, "vsettings": [{"__ref": "wFPcwPY_2bc8"}], "__type": "TplTag"}, "BnExkTBlWt3X": {"uuid": "C7wxqkRJSmAR", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "PovgJWLYYDEa": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "0pGeXrADFz6E": {"name": "text", "__type": "Text"}, "UaN1AFzlCTTm": {"name": "title", "uuid": "EHHW6xOiX7az", "__type": "Var"}, "f1bppQceBhIc": {"name": "text", "__type": "Text"}, "QRAvMZ69oS17": {"name": "description", "uuid": "HxKVM6f8_MVg", "__type": "Var"}, "wcee0p59WHO_": {"name": "img", "__type": "Img"}, "O6gV9maQ3H6S": {"name": "image", "uuid": "zqxNv3q2oxCF", "__type": "Var"}, "gbWOsB0DM410": {"name": "text", "__type": "Text"}, "Gft7UMJNIu4u": {"name": "canonical", "uuid": "UvBP-LsIetOF", "__type": "Var"}, "3vSsjE3JCnBW": {"variants": [{"__ref": "I3Bh_D87PXms"}], "args": [], "attrs": {}, "rs": {"__ref": "vTiozrBLAX4D"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kXWE95a_KQen": {"name": "any", "__type": "AnyType"}, "o05oSKcSV_1n": {"name": "dataOp", "uuid": "-nswZuROfzbz", "__type": "Var"}, "xf1HBIXAOdQC": {"name": "text", "__type": "Text"}, "-CN3Q9pgMDCr": {"name": "name", "uuid": "VDbmJAbeZoyE", "__type": "Var"}, "NWmGpsLzSYM_": {"name": "renderFunc", "params": [{"__ref": "HD0nOe8Qw4jw"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "TI8vBek4XK6R": {"name": "children", "uuid": "KsfCDsQFwFTs", "__type": "Var"}, "PUKNtlyX4GUx": {"name": "num", "__type": "<PERSON><PERSON>"}, "bzHAakq11OlZ": {"name": "pageSize", "uuid": "UCpscAos_0b0", "__type": "Var"}, "_0RzfhFZzy13": {"name": "num", "__type": "<PERSON><PERSON>"}, "MjnaSp7ouUTr": {"name": "pageIndex", "uuid": "6HuZ6Jmh3pTf", "__type": "Var"}, "Dm15C1lfrHgk": {"param": {"__ref": "YGv2gd6Y420z"}, "defaultContents": [], "uuid": "b_h2Pt8-5reo", "parent": {"__ref": "UM5nkp0liC3Y"}, "locked": null, "vsettings": [{"__ref": "7RAGJkDNDsx7"}], "__type": "TplSlot"}, "wFPcwPY_2bc8": {"variants": [{"__ref": "BnExkTBlWt3X"}], "args": [], "attrs": {}, "rs": {"__ref": "B_i6Ktv0bxRP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "vTiozrBLAX4D": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "HD0nOe8Qw4jw": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "MyiCeqZg5ocp"}, "__type": "ArgType"}, "7RAGJkDNDsx7": {"variants": [{"__ref": "BnExkTBlWt3X"}], "args": [], "attrs": {}, "rs": {"__ref": "PJFDVzGzntiu"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "B_i6Ktv0bxRP": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "MyiCeqZg5ocp": {"name": "any", "__type": "AnyType"}, "PJFDVzGzntiu": {"values": {}, "mixins": [], "__type": "RuleSet"}, "XHMug2V6u6yT": {"uuid": "5o2U-A5VF0v4", "name": "Test", "params": [], "states": [], "tplTree": {"__ref": "z6isWRvWn2jh"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "LlGnndGQwqPL"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "mYcmyyF9lJfB": {"component": {"__ref": "XHMug2V6u6yT"}, "matrix": {"__ref": "j4deGyI3n11-"}, "customMatrix": {"__ref": "3_qjh5IxKuk-"}, "__type": "ComponentArena"}, "LlGnndGQwqPL": {"uuid": "M5HqTEcbKju0", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "j4deGyI3n11-": {"rows": [{"__ref": "ze7QVVCnA183"}], "__type": "ArenaFrameGrid"}, "3_qjh5IxKuk-": {"rows": [{"__ref": "yw8cp67gUTwd"}], "__type": "ArenaFrameGrid"}, "ze7QVVCnA183": {"cols": [{"__ref": "Yfi7SzBh1V2u"}], "rowKey": null, "__type": "ArenaFrameRow"}, "yw8cp67gUTwd": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "Yfi7SzBh1V2u": {"frame": {"__ref": "0MgCnVwIHCVI"}, "cellKey": {"__ref": "LlGnndGQwqPL"}, "__type": "ArenaFrameCell"}, "0MgCnVwIHCVI": {"uuid": "LgSUgY0u8qSc", "width": 1180, "height": 540, "container": {"__ref": "eb_dr27OdLT5"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "LlGnndGQwqPL"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "eb_dr27OdLT5": {"name": null, "component": {"__ref": "XHMug2V6u6yT"}, "uuid": "hX2MsLkvRmht", "parent": null, "locked": null, "vsettings": [{"__ref": "M56xc4fKVUeM"}], "__type": "TplComponent"}, "M56xc4fKVUeM": {"variants": [{"__ref": "BM3CyjR6PREH"}], "args": [], "attrs": {}, "rs": {"__ref": "ytX4tIUys93B"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ytX4tIUys93B": {"values": {}, "mixins": [], "__type": "RuleSet"}, "OBbOy4AkwwkT": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "nuXUX1c6wCJt", "parent": {"__ref": "z6isWRvWn2jh"}, "locked": null, "vsettings": [{"__ref": "Vm7w8uOGGWHr"}], "__type": "TplTag"}, "Vm7w8uOGGWHr": {"variants": [{"__ref": "LlGnndGQwqPL"}], "args": [], "attrs": {}, "rs": {"__ref": "3_6FNpbTOhGD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3_6FNpbTOhGD": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px"}, "mixins": [], "__type": "RuleSet"}, "z6isWRvWn2jh": {"tag": "div", "name": null, "children": [{"__ref": "OBbOy4AkwwkT"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "kXLBN8Hl829b", "parent": null, "locked": null, "vsettings": [{"__ref": "9TJkKZVCiXJe"}], "__type": "TplTag"}, "9TJkKZVCiXJe": {"variants": [{"__ref": "LlGnndGQwqPL"}], "args": [], "attrs": {}, "rs": {"__ref": "9LmfXpHcp7TJ"}, "dataCond": {"__ref": "icp-UNYj1XKh"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9LmfXpHcp7TJ": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "icp-UNYj1XKh": {"code": "true", "fallback": null, "__type": "CustomCode"}, "xHLk5sYdKoX_": {"uuid": "133ZQ6C_qoL0", "pkgId": "fcfc34f8-4ed8-4cbe-8066-dc0b3e7d3e35", "projectId": "3Ly8WyuoJ263bQVv2eUgq7", "version": "0.0.1", "name": "Untitled Project", "site": {"__ref": "Mgq9mDrXxf1Y"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}, "projectId": "3Ly8WyuoJ263bQVv2eUgq7", "version": "0.0.1", "branchId": "jJhYw1pP4mnJVCFrhiNsk9"}, {"id": "a1eb3c80-23bc-4a7a-a641-a66eb77c0453", "data": {"root": "uHA2AFZ3XVWd", "map": {"rtH3qI7L415j": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "1fLFumwuth8K": {"name": "Default Typography", "rs": {"__ref": "rtH3qI7L415j"}, "preview": null, "uuid": "sagEmhwjYOTv", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "BVAkgseP-CCN": {"values": {}, "mixins": [], "__type": "RuleSet"}, "SP2PWWxkt0m2": {"rs": {"__ref": "BVAkgseP-CCN"}, "__type": "ThemeLayoutSettings"}, "cvvQFii1k4BV": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "N-pVtSn_n0zF": {"name": "Default \"h1\"", "rs": {"__ref": "cvvQFii1k4BV"}, "preview": null, "uuid": "a6mQakstSSb4", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "TSbaacmeT3pO": {"selector": "h1", "style": {"__ref": "N-pVtSn_n0zF"}, "__type": "ThemeStyle"}, "7gC2lqy7T1BZ": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "lbegziOdqTpT": {"name": "Default \"h2\"", "rs": {"__ref": "7gC2lqy7T1BZ"}, "preview": null, "uuid": "Fqj9l7eVvVRQ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Rt6Kp90e90am": {"selector": "h2", "style": {"__ref": "lbegziOdqTpT"}, "__type": "ThemeStyle"}, "jUxQ_rCDUH-R": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "hveAsZ0hEnq6": {"name": "Default \"h3\"", "rs": {"__ref": "jUxQ_rCDUH-R"}, "preview": null, "uuid": "zE0GdkrnVwD7", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "n_wYcbdSBu5c": {"selector": "h3", "style": {"__ref": "hveAsZ0hEnq6"}, "__type": "ThemeStyle"}, "f-JVl75nBMC6": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "HtFfryIXI-43": {"name": "Default \"h4\"", "rs": {"__ref": "f-JVl75nBMC6"}, "preview": null, "uuid": "KUFwR3EAOnM0", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "iuFFhWvNtxoy": {"selector": "h4", "style": {"__ref": "HtFfryIXI-43"}, "__type": "ThemeStyle"}, "X86rGAeSOUMf": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "XJfgBHB02oRq": {"name": "Default \"h5\"", "rs": {"__ref": "X86rGAeSOUMf"}, "preview": null, "uuid": "DdIdJg54vX8n", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ts3_I66D1MPI": {"selector": "h5", "style": {"__ref": "XJfgBHB02oRq"}, "__type": "ThemeStyle"}, "oPuOJF1qFR8k": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "5R0CNHcUANBU": {"name": "Default \"h6\"", "rs": {"__ref": "oPuOJF1qFR8k"}, "preview": null, "uuid": "Jo3U8t7XTYPO", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Ci54heeaBkw4": {"selector": "h6", "style": {"__ref": "5R0CNHcUANBU"}, "__type": "ThemeStyle"}, "s4epEyOGiffY": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "bGRWpAemJxYO": {"name": "Default \"a\"", "rs": {"__ref": "s4epEyOGiffY"}, "preview": null, "uuid": "VEpAY9G4fAsp", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "2H6QtOHDSeuu": {"selector": "a", "style": {"__ref": "bGRWpAemJxYO"}, "__type": "ThemeStyle"}, "39P_5NDX3Yg4": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "1g9-hvXSp-H6": {"name": "Default \"a:hover\"", "rs": {"__ref": "39P_5NDX3Yg4"}, "preview": null, "uuid": "DrtH_8PmDu0U", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "1mgTigoScETJ": {"selector": "a:hover", "style": {"__ref": "1g9-hvXSp-H6"}, "__type": "ThemeStyle"}, "l-7somvmAiFg": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "ekZQ9vXL8RHe": {"name": "Default \"blockquote\"", "rs": {"__ref": "l-7somvmAiFg"}, "preview": null, "uuid": "nDc0dI9YwsuK", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "umpOYLMj3IBi": {"selector": "blockquote", "style": {"__ref": "ekZQ9vXL8RHe"}, "__type": "ThemeStyle"}, "EVeK8uNZ3fp3": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "NbfGIq7obiGd": {"name": "Default \"code\"", "rs": {"__ref": "EVeK8uNZ3fp3"}, "preview": null, "uuid": "okl61CsLJAw7", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Af8xBi5A8a8g": {"selector": "code", "style": {"__ref": "NbfGIq7obiGd"}, "__type": "ThemeStyle"}, "Og8vedb-uqkf": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "rxCOi8l7bi78": {"name": "Default \"pre\"", "rs": {"__ref": "Og8vedb-uqkf"}, "preview": null, "uuid": "BRhb2LGnCjLb", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "A7D7wzAv3qG-": {"selector": "pre", "style": {"__ref": "rxCOi8l7bi78"}, "__type": "ThemeStyle"}, "dbYyBrnzs3d3": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "9FqB8TLizCkB": {"name": "Default \"ol\"", "rs": {"__ref": "dbYyBrnzs3d3"}, "preview": null, "uuid": "FzB8VfyIKsrh", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "IzRxdWXfI6Us": {"selector": "ol", "style": {"__ref": "9FqB8TLizCkB"}, "__type": "ThemeStyle"}, "EqD4k-fz2vwx": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "XK0EIgLTvWt4": {"name": "Default \"ul\"", "rs": {"__ref": "EqD4k-fz2vwx"}, "preview": null, "uuid": "F6CBg_hTZRJ6", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "BbvvUybfhLEe": {"selector": "ul", "style": {"__ref": "XK0EIgLTvWt4"}, "__type": "ThemeStyle"}, "9KCiiDBSorcv": {"defaultStyle": {"__ref": "1fLFumwuth8K"}, "styles": [{"__ref": "TSbaacmeT3pO"}, {"__ref": "Rt6Kp90e90am"}, {"__ref": "n_wYcbdSBu5c"}, {"__ref": "iuFFhWvNtxoy"}, {"__ref": "ts3_I66D1MPI"}, {"__ref": "Ci54heeaBkw4"}, {"__ref": "2H6QtOHDSeuu"}, {"__ref": "1mgTigoScETJ"}, {"__ref": "umpOYLMj3IBi"}, {"__ref": "Af8xBi5A8a8g"}, {"__ref": "A7D7wzAv3qG-"}, {"__ref": "IzRxdWXfI6Us"}, {"__ref": "BbvvUybfhLEe"}], "layout": {"__ref": "SP2PWWxkt0m2"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "8nBYE85F8tK3": {"name": "text", "__type": "Text"}, "WhnHtmZ_iv-x": {"name": "Screen", "uuid": "9gJVhZx78hqY", "__type": "Var"}, "z4QMZMa2zo00": {"type": {"__ref": "8nBYE85F8tK3"}, "variable": {"__ref": "WhnHtmZ_iv-x"}, "uuid": "fKh5bjBgOKLd", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "sabKQqOjEw-m": {"type": "global-screen", "param": {"__ref": "z4QMZMa2zo00"}, "uuid": "xZmoAniT4wNV", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "BM3CyjR6PREH": {"uuid": "I7_XstFXzjGo", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Mgq9mDrXxf1Y": {"components": [{"__ref": "gcp-I4c9H1d1"}, {"__ref": "b3kNX_TdMIFo"}, {"__ref": "XHMug2V6u6yT"}], "arenas": [], "pageArenas": [], "componentArenas": [{"__ref": "mYcmyyF9lJfB"}], "globalVariantGroups": [{"__ref": "sabKQqOjEw-m"}], "userManagedFonts": [], "globalVariant": {"__ref": "BM3CyjR6PREH"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "9KCiiDBSorcv"}], "activeTheme": {"__ref": "9KCiiDBSorcv"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "sabKQqOjEw-m"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "gcp-I4c9H1d1": {"uuid": "U_fhnoosZ7Db", "name": "hostless-plasmic-head", "params": [{"__ref": "BhZY-ZzTkguj"}, {"__ref": "wZ0sdohDIPEm"}, {"__ref": "hiJsGCL2mJ5Q"}, {"__ref": "57VTsyCYgG-F"}], "states": [], "tplTree": {"__ref": "F167GOzF4and"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "I3Bh_D87PXms"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "4BuFqnuQppTZ"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "b3kNX_TdMIFo": {"uuid": "zZQVXzbZDClb", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "s6tO-w_oxl8R"}, {"__ref": "-0y9FETBa5Ml"}, {"__ref": "YGv2gd6Y420z"}, {"__ref": "lzLCvqozElaP"}, {"__ref": "o7PWQIHjQtly"}], "states": [], "tplTree": {"__ref": "UM5nkp0liC3Y"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "BnExkTBlWt3X"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "PovgJWLYYDEa"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component"}, "BhZY-ZzTkguj": {"type": {"__ref": "0pGeXrADFz6E"}, "variable": {"__ref": "UaN1AFzlCTTm"}, "uuid": "yrsRqvHfPTBq", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "wZ0sdohDIPEm": {"type": {"__ref": "f1bppQceBhIc"}, "variable": {"__ref": "QRAvMZ69oS17"}, "uuid": "5s16PGVHnb5z", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "hiJsGCL2mJ5Q": {"type": {"__ref": "wcee0p59WHO_"}, "variable": {"__ref": "O6gV9maQ3H6S"}, "uuid": "xECPLI4sIZow", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "57VTsyCYgG-F": {"type": {"__ref": "gbWOsB0DM410"}, "variable": {"__ref": "Gft7UMJNIu4u"}, "uuid": "7gARgRtAAamA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "F167GOzF4and": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "qVgxiRh0YS9W", "parent": null, "locked": null, "vsettings": [{"__ref": "3vSsjE3JCnBW"}], "__type": "TplTag"}, "I3Bh_D87PXms": {"uuid": "fBa5fK4AkXlF", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "4BuFqnuQppTZ": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "s6tO-w_oxl8R": {"type": {"__ref": "kXWE95a_KQen"}, "variable": {"__ref": "o05oSKcSV_1n"}, "uuid": "IDMn4tmgA12n", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "-0y9FETBa5Ml": {"type": {"__ref": "xf1HBIXAOdQC"}, "variable": {"__ref": "-CN3Q9pgMDCr"}, "uuid": "ayoSsFneIxAe", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "YGv2gd6Y420z": {"type": {"__ref": "NWmGpsLzSYM_"}, "tplSlot": {"__ref": "Dm15C1lfrHgk"}, "variable": {"__ref": "TI8vBek4XK6R"}, "uuid": "O-AxbHZWOeKG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "lzLCvqozElaP": {"type": {"__ref": "PUKNtlyX4GUx"}, "variable": {"__ref": "bzHAakq11OlZ"}, "uuid": "6W-PGaYccBo2", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "o7PWQIHjQtly": {"type": {"__ref": "_0RzfhFZzy13"}, "variable": {"__ref": "MjnaSp7ouUTr"}, "uuid": "-t5pIdRQIuMI", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "UM5nkp0liC3Y": {"tag": "div", "name": null, "children": [{"__ref": "Dm15C1lfrHgk"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "BuNYC8Bqk0ji", "parent": null, "locked": null, "vsettings": [{"__ref": "wFPcwPY_2bc8"}], "__type": "TplTag"}, "BnExkTBlWt3X": {"uuid": "C7wxqkRJSmAR", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "PovgJWLYYDEa": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "0pGeXrADFz6E": {"name": "text", "__type": "Text"}, "UaN1AFzlCTTm": {"name": "title", "uuid": "EHHW6xOiX7az", "__type": "Var"}, "f1bppQceBhIc": {"name": "text", "__type": "Text"}, "QRAvMZ69oS17": {"name": "description", "uuid": "HxKVM6f8_MVg", "__type": "Var"}, "wcee0p59WHO_": {"name": "img", "__type": "Img"}, "O6gV9maQ3H6S": {"name": "image", "uuid": "zqxNv3q2oxCF", "__type": "Var"}, "gbWOsB0DM410": {"name": "text", "__type": "Text"}, "Gft7UMJNIu4u": {"name": "canonical", "uuid": "UvBP-LsIetOF", "__type": "Var"}, "3vSsjE3JCnBW": {"variants": [{"__ref": "I3Bh_D87PXms"}], "args": [], "attrs": {}, "rs": {"__ref": "vTiozrBLAX4D"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kXWE95a_KQen": {"name": "any", "__type": "AnyType"}, "o05oSKcSV_1n": {"name": "dataOp", "uuid": "-nswZuROfzbz", "__type": "Var"}, "xf1HBIXAOdQC": {"name": "text", "__type": "Text"}, "-CN3Q9pgMDCr": {"name": "name", "uuid": "VDbmJAbeZoyE", "__type": "Var"}, "NWmGpsLzSYM_": {"name": "renderFunc", "params": [{"__ref": "HD0nOe8Qw4jw"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "TI8vBek4XK6R": {"name": "children", "uuid": "KsfCDsQFwFTs", "__type": "Var"}, "PUKNtlyX4GUx": {"name": "num", "__type": "<PERSON><PERSON>"}, "bzHAakq11OlZ": {"name": "pageSize", "uuid": "UCpscAos_0b0", "__type": "Var"}, "_0RzfhFZzy13": {"name": "num", "__type": "<PERSON><PERSON>"}, "MjnaSp7ouUTr": {"name": "pageIndex", "uuid": "6HuZ6Jmh3pTf", "__type": "Var"}, "Dm15C1lfrHgk": {"param": {"__ref": "YGv2gd6Y420z"}, "defaultContents": [], "uuid": "b_h2Pt8-5reo", "parent": {"__ref": "UM5nkp0liC3Y"}, "locked": null, "vsettings": [{"__ref": "7RAGJkDNDsx7"}], "__type": "TplSlot"}, "wFPcwPY_2bc8": {"variants": [{"__ref": "BnExkTBlWt3X"}], "args": [], "attrs": {}, "rs": {"__ref": "B_i6Ktv0bxRP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "vTiozrBLAX4D": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "HD0nOe8Qw4jw": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "MyiCeqZg5ocp"}, "__type": "ArgType"}, "7RAGJkDNDsx7": {"variants": [{"__ref": "BnExkTBlWt3X"}], "args": [], "attrs": {}, "rs": {"__ref": "PJFDVzGzntiu"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "B_i6Ktv0bxRP": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "MyiCeqZg5ocp": {"name": "any", "__type": "AnyType"}, "PJFDVzGzntiu": {"values": {}, "mixins": [], "__type": "RuleSet"}, "XHMug2V6u6yT": {"uuid": "5o2U-A5VF0v4", "name": "Test", "params": [], "states": [], "tplTree": {"__ref": "PtRrF7gywoLu"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "LlGnndGQwqPL"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "mYcmyyF9lJfB": {"component": {"__ref": "XHMug2V6u6yT"}, "matrix": {"__ref": "j4deGyI3n11-"}, "customMatrix": {"__ref": "3_qjh5IxKuk-"}, "__type": "ComponentArena"}, "PtRrF7gywoLu": {"tag": "div", "name": null, "children": [{"__ref": "OBbOy4AkwwkT"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "FUn4j9VYQ7_o", "parent": null, "locked": null, "vsettings": [{"__ref": "SqqiVORcnrVb"}], "__type": "TplTag"}, "LlGnndGQwqPL": {"uuid": "M5HqTEcbKju0", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "j4deGyI3n11-": {"rows": [{"__ref": "ze7QVVCnA183"}], "__type": "ArenaFrameGrid"}, "3_qjh5IxKuk-": {"rows": [{"__ref": "yw8cp67gUTwd"}], "__type": "ArenaFrameGrid"}, "SqqiVORcnrVb": {"variants": [{"__ref": "LlGnndGQwqPL"}], "args": [], "attrs": {}, "rs": {"__ref": "qFx3upfB3VcG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ze7QVVCnA183": {"cols": [{"__ref": "Yfi7SzBh1V2u"}], "rowKey": null, "__type": "ArenaFrameRow"}, "yw8cp67gUTwd": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "qFx3upfB3VcG": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "Yfi7SzBh1V2u": {"frame": {"__ref": "0MgCnVwIHCVI"}, "cellKey": {"__ref": "LlGnndGQwqPL"}, "__type": "ArenaFrameCell"}, "0MgCnVwIHCVI": {"uuid": "LgSUgY0u8qSc", "width": 1180, "height": 540, "container": {"__ref": "eb_dr27OdLT5"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "LlGnndGQwqPL"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "eb_dr27OdLT5": {"name": null, "component": {"__ref": "XHMug2V6u6yT"}, "uuid": "hX2MsLkvRmht", "parent": null, "locked": null, "vsettings": [{"__ref": "M56xc4fKVUeM"}], "__type": "TplComponent"}, "M56xc4fKVUeM": {"variants": [{"__ref": "BM3CyjR6PREH"}], "args": [], "attrs": {}, "rs": {"__ref": "ytX4tIUys93B"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ytX4tIUys93B": {"values": {}, "mixins": [], "__type": "RuleSet"}, "OBbOy4AkwwkT": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "nuXUX1c6wCJt", "parent": {"__ref": "PtRrF7gywoLu"}, "locked": null, "vsettings": [{"__ref": "Vm7w8uOGGWHr"}], "__type": "TplTag"}, "Vm7w8uOGGWHr": {"variants": [{"__ref": "LlGnndGQwqPL"}], "args": [], "attrs": {}, "rs": {"__ref": "3_6FNpbTOhGD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3_6FNpbTOhGD": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px"}, "mixins": [], "__type": "RuleSet"}, "uHA2AFZ3XVWd": {"uuid": "PRDHKeRxQO-j", "pkgId": "fcfc34f8-4ed8-4cbe-8066-dc0b3e7d3e35", "projectId": "3Ly8WyuoJ263bQVv2eUgq7", "version": "0.0.1", "name": "Untitled Project", "site": {"__ref": "Mgq9mDrXxf1Y"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}, "projectId": "3Ly8WyuoJ263bQVv2eUgq7", "version": "0.0.1", "branchId": "main"}], "project": {"id": "3Ly8WyuoJ263bQVv2eUgq7", "name": "Untitled Project", "commitGraph": {"parents": {"83eeb470-6a3a-46c0-8729-1c6c3af0663f": ["92029f97-7e74-40be-9276-7b932cd19305", "a1eb3c80-23bc-4a7a-a641-a66eb77c0453"], "92029f97-7e74-40be-9276-7b932cd19305": ["a1eb3c80-23bc-4a7a-a641-a66eb77c0453"], "a1eb3c80-23bc-4a7a-a641-a66eb77c0453": []}, "branches": {"main": "a1eb3c80-23bc-4a7a-a641-a66eb77c0453", "jJhYw1pP4mnJVCFrhiNsk9": "92029f97-7e74-40be-9276-7b932cd19305"}}}, "revisions": [{"branchId": "jJhYw1pP4mnJVCFrhiNsk9", "data": {"root": "Mgq9mDrXxf1Y", "map": {"rtH3qI7L415j": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "1fLFumwuth8K": {"name": "Default Typography", "rs": {"__ref": "rtH3qI7L415j"}, "preview": null, "uuid": "sagEmhwjYOTv", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "BVAkgseP-CCN": {"values": {}, "mixins": [], "__type": "RuleSet"}, "SP2PWWxkt0m2": {"rs": {"__ref": "BVAkgseP-CCN"}, "__type": "ThemeLayoutSettings"}, "cvvQFii1k4BV": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "N-pVtSn_n0zF": {"name": "Default \"h1\"", "rs": {"__ref": "cvvQFii1k4BV"}, "preview": null, "uuid": "a6mQakstSSb4", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "TSbaacmeT3pO": {"selector": "h1", "style": {"__ref": "N-pVtSn_n0zF"}, "__type": "ThemeStyle"}, "7gC2lqy7T1BZ": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "lbegziOdqTpT": {"name": "Default \"h2\"", "rs": {"__ref": "7gC2lqy7T1BZ"}, "preview": null, "uuid": "Fqj9l7eVvVRQ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Rt6Kp90e90am": {"selector": "h2", "style": {"__ref": "lbegziOdqTpT"}, "__type": "ThemeStyle"}, "jUxQ_rCDUH-R": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "hveAsZ0hEnq6": {"name": "Default \"h3\"", "rs": {"__ref": "jUxQ_rCDUH-R"}, "preview": null, "uuid": "zE0GdkrnVwD7", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "n_wYcbdSBu5c": {"selector": "h3", "style": {"__ref": "hveAsZ0hEnq6"}, "__type": "ThemeStyle"}, "f-JVl75nBMC6": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "HtFfryIXI-43": {"name": "Default \"h4\"", "rs": {"__ref": "f-JVl75nBMC6"}, "preview": null, "uuid": "KUFwR3EAOnM0", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "iuFFhWvNtxoy": {"selector": "h4", "style": {"__ref": "HtFfryIXI-43"}, "__type": "ThemeStyle"}, "X86rGAeSOUMf": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "XJfgBHB02oRq": {"name": "Default \"h5\"", "rs": {"__ref": "X86rGAeSOUMf"}, "preview": null, "uuid": "DdIdJg54vX8n", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ts3_I66D1MPI": {"selector": "h5", "style": {"__ref": "XJfgBHB02oRq"}, "__type": "ThemeStyle"}, "oPuOJF1qFR8k": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "5R0CNHcUANBU": {"name": "Default \"h6\"", "rs": {"__ref": "oPuOJF1qFR8k"}, "preview": null, "uuid": "Jo3U8t7XTYPO", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Ci54heeaBkw4": {"selector": "h6", "style": {"__ref": "5R0CNHcUANBU"}, "__type": "ThemeStyle"}, "s4epEyOGiffY": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "bGRWpAemJxYO": {"name": "Default \"a\"", "rs": {"__ref": "s4epEyOGiffY"}, "preview": null, "uuid": "VEpAY9G4fAsp", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "2H6QtOHDSeuu": {"selector": "a", "style": {"__ref": "bGRWpAemJxYO"}, "__type": "ThemeStyle"}, "39P_5NDX3Yg4": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "1g9-hvXSp-H6": {"name": "Default \"a:hover\"", "rs": {"__ref": "39P_5NDX3Yg4"}, "preview": null, "uuid": "DrtH_8PmDu0U", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "1mgTigoScETJ": {"selector": "a:hover", "style": {"__ref": "1g9-hvXSp-H6"}, "__type": "ThemeStyle"}, "l-7somvmAiFg": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "ekZQ9vXL8RHe": {"name": "Default \"blockquote\"", "rs": {"__ref": "l-7somvmAiFg"}, "preview": null, "uuid": "nDc0dI9YwsuK", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "umpOYLMj3IBi": {"selector": "blockquote", "style": {"__ref": "ekZQ9vXL8RHe"}, "__type": "ThemeStyle"}, "EVeK8uNZ3fp3": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "NbfGIq7obiGd": {"name": "Default \"code\"", "rs": {"__ref": "EVeK8uNZ3fp3"}, "preview": null, "uuid": "okl61CsLJAw7", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Af8xBi5A8a8g": {"selector": "code", "style": {"__ref": "NbfGIq7obiGd"}, "__type": "ThemeStyle"}, "Og8vedb-uqkf": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "rxCOi8l7bi78": {"name": "Default \"pre\"", "rs": {"__ref": "Og8vedb-uqkf"}, "preview": null, "uuid": "BRhb2LGnCjLb", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "A7D7wzAv3qG-": {"selector": "pre", "style": {"__ref": "rxCOi8l7bi78"}, "__type": "ThemeStyle"}, "dbYyBrnzs3d3": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "9FqB8TLizCkB": {"name": "Default \"ol\"", "rs": {"__ref": "dbYyBrnzs3d3"}, "preview": null, "uuid": "FzB8VfyIKsrh", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "IzRxdWXfI6Us": {"selector": "ol", "style": {"__ref": "9FqB8TLizCkB"}, "__type": "ThemeStyle"}, "EqD4k-fz2vwx": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "XK0EIgLTvWt4": {"name": "Default \"ul\"", "rs": {"__ref": "EqD4k-fz2vwx"}, "preview": null, "uuid": "F6CBg_hTZRJ6", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "BbvvUybfhLEe": {"selector": "ul", "style": {"__ref": "XK0EIgLTvWt4"}, "__type": "ThemeStyle"}, "9KCiiDBSorcv": {"defaultStyle": {"__ref": "1fLFumwuth8K"}, "styles": [{"__ref": "TSbaacmeT3pO"}, {"__ref": "Rt6Kp90e90am"}, {"__ref": "n_wYcbdSBu5c"}, {"__ref": "iuFFhWvNtxoy"}, {"__ref": "ts3_I66D1MPI"}, {"__ref": "Ci54heeaBkw4"}, {"__ref": "2H6QtOHDSeuu"}, {"__ref": "1mgTigoScETJ"}, {"__ref": "umpOYLMj3IBi"}, {"__ref": "Af8xBi5A8a8g"}, {"__ref": "A7D7wzAv3qG-"}, {"__ref": "IzRxdWXfI6Us"}, {"__ref": "BbvvUybfhLEe"}], "layout": {"__ref": "SP2PWWxkt0m2"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "8nBYE85F8tK3": {"name": "text", "__type": "Text"}, "WhnHtmZ_iv-x": {"name": "Screen", "uuid": "9gJVhZx78hqY", "__type": "Var"}, "z4QMZMa2zo00": {"type": {"__ref": "8nBYE85F8tK3"}, "variable": {"__ref": "WhnHtmZ_iv-x"}, "uuid": "fKh5bjBgOKLd", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "sabKQqOjEw-m": {"type": "global-screen", "param": {"__ref": "z4QMZMa2zo00"}, "uuid": "xZmoAniT4wNV", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "BM3CyjR6PREH": {"uuid": "I7_XstFXzjGo", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Mgq9mDrXxf1Y": {"components": [{"__ref": "gcp-I4c9H1d1"}, {"__ref": "b3kNX_TdMIFo"}, {"__ref": "XHMug2V6u6yT"}], "arenas": [], "pageArenas": [], "componentArenas": [{"__ref": "mYcmyyF9lJfB"}], "globalVariantGroups": [{"__ref": "sabKQqOjEw-m"}], "userManagedFonts": [], "globalVariant": {"__ref": "BM3CyjR6PREH"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "9KCiiDBSorcv"}], "activeTheme": {"__ref": "9KCiiDBSorcv"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "sabKQqOjEw-m"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "gcp-I4c9H1d1": {"uuid": "U_fhnoosZ7Db", "name": "hostless-plasmic-head", "params": [{"__ref": "BhZY-ZzTkguj"}, {"__ref": "wZ0sdohDIPEm"}, {"__ref": "hiJsGCL2mJ5Q"}, {"__ref": "57VTsyCYgG-F"}], "states": [], "tplTree": {"__ref": "F167GOzF4and"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "I3Bh_D87PXms"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "4BuFqnuQppTZ"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "b3kNX_TdMIFo": {"uuid": "zZQVXzbZDClb", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "s6tO-w_oxl8R"}, {"__ref": "-0y9FETBa5Ml"}, {"__ref": "YGv2gd6Y420z"}, {"__ref": "lzLCvqozElaP"}, {"__ref": "o7PWQIHjQtly"}], "states": [], "tplTree": {"__ref": "UM5nkp0liC3Y"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "BnExkTBlWt3X"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "PovgJWLYYDEa"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component"}, "BhZY-ZzTkguj": {"type": {"__ref": "0pGeXrADFz6E"}, "variable": {"__ref": "UaN1AFzlCTTm"}, "uuid": "yrsRqvHfPTBq", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "wZ0sdohDIPEm": {"type": {"__ref": "f1bppQceBhIc"}, "variable": {"__ref": "QRAvMZ69oS17"}, "uuid": "5s16PGVHnb5z", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "hiJsGCL2mJ5Q": {"type": {"__ref": "wcee0p59WHO_"}, "variable": {"__ref": "O6gV9maQ3H6S"}, "uuid": "xECPLI4sIZow", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "57VTsyCYgG-F": {"type": {"__ref": "gbWOsB0DM410"}, "variable": {"__ref": "Gft7UMJNIu4u"}, "uuid": "7gARgRtAAamA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "F167GOzF4and": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "qVgxiRh0YS9W", "parent": null, "locked": null, "vsettings": [{"__ref": "3vSsjE3JCnBW"}], "__type": "TplTag"}, "I3Bh_D87PXms": {"uuid": "fBa5fK4AkXlF", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "4BuFqnuQppTZ": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "s6tO-w_oxl8R": {"type": {"__ref": "kXWE95a_KQen"}, "variable": {"__ref": "o05oSKcSV_1n"}, "uuid": "IDMn4tmgA12n", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "-0y9FETBa5Ml": {"type": {"__ref": "xf1HBIXAOdQC"}, "variable": {"__ref": "-CN3Q9pgMDCr"}, "uuid": "ayoSsFneIxAe", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "YGv2gd6Y420z": {"type": {"__ref": "NWmGpsLzSYM_"}, "tplSlot": {"__ref": "Dm15C1lfrHgk"}, "variable": {"__ref": "TI8vBek4XK6R"}, "uuid": "O-AxbHZWOeKG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "lzLCvqozElaP": {"type": {"__ref": "PUKNtlyX4GUx"}, "variable": {"__ref": "bzHAakq11OlZ"}, "uuid": "6W-PGaYccBo2", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "o7PWQIHjQtly": {"type": {"__ref": "_0RzfhFZzy13"}, "variable": {"__ref": "MjnaSp7ouUTr"}, "uuid": "-t5pIdRQIuMI", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "UM5nkp0liC3Y": {"tag": "div", "name": null, "children": [{"__ref": "Dm15C1lfrHgk"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "BuNYC8Bqk0ji", "parent": null, "locked": null, "vsettings": [{"__ref": "wFPcwPY_2bc8"}], "__type": "TplTag"}, "BnExkTBlWt3X": {"uuid": "C7wxqkRJSmAR", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "PovgJWLYYDEa": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "0pGeXrADFz6E": {"name": "text", "__type": "Text"}, "UaN1AFzlCTTm": {"name": "title", "uuid": "EHHW6xOiX7az", "__type": "Var"}, "f1bppQceBhIc": {"name": "text", "__type": "Text"}, "QRAvMZ69oS17": {"name": "description", "uuid": "HxKVM6f8_MVg", "__type": "Var"}, "wcee0p59WHO_": {"name": "img", "__type": "Img"}, "O6gV9maQ3H6S": {"name": "image", "uuid": "zqxNv3q2oxCF", "__type": "Var"}, "gbWOsB0DM410": {"name": "text", "__type": "Text"}, "Gft7UMJNIu4u": {"name": "canonical", "uuid": "UvBP-LsIetOF", "__type": "Var"}, "3vSsjE3JCnBW": {"variants": [{"__ref": "I3Bh_D87PXms"}], "args": [], "attrs": {}, "rs": {"__ref": "vTiozrBLAX4D"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kXWE95a_KQen": {"name": "any", "__type": "AnyType"}, "o05oSKcSV_1n": {"name": "dataOp", "uuid": "-nswZuROfzbz", "__type": "Var"}, "xf1HBIXAOdQC": {"name": "text", "__type": "Text"}, "-CN3Q9pgMDCr": {"name": "name", "uuid": "VDbmJAbeZoyE", "__type": "Var"}, "NWmGpsLzSYM_": {"name": "renderFunc", "params": [{"__ref": "HD0nOe8Qw4jw"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "TI8vBek4XK6R": {"name": "children", "uuid": "KsfCDsQFwFTs", "__type": "Var"}, "PUKNtlyX4GUx": {"name": "num", "__type": "<PERSON><PERSON>"}, "bzHAakq11OlZ": {"name": "pageSize", "uuid": "UCpscAos_0b0", "__type": "Var"}, "_0RzfhFZzy13": {"name": "num", "__type": "<PERSON><PERSON>"}, "MjnaSp7ouUTr": {"name": "pageIndex", "uuid": "6HuZ6Jmh3pTf", "__type": "Var"}, "Dm15C1lfrHgk": {"param": {"__ref": "YGv2gd6Y420z"}, "defaultContents": [], "uuid": "b_h2Pt8-5reo", "parent": {"__ref": "UM5nkp0liC3Y"}, "locked": null, "vsettings": [{"__ref": "7RAGJkDNDsx7"}], "__type": "TplSlot"}, "wFPcwPY_2bc8": {"variants": [{"__ref": "BnExkTBlWt3X"}], "args": [], "attrs": {}, "rs": {"__ref": "B_i6Ktv0bxRP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "vTiozrBLAX4D": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "HD0nOe8Qw4jw": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "MyiCeqZg5ocp"}, "__type": "ArgType"}, "7RAGJkDNDsx7": {"variants": [{"__ref": "BnExkTBlWt3X"}], "args": [], "attrs": {}, "rs": {"__ref": "PJFDVzGzntiu"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "B_i6Ktv0bxRP": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "MyiCeqZg5ocp": {"name": "any", "__type": "AnyType"}, "PJFDVzGzntiu": {"values": {}, "mixins": [], "__type": "RuleSet"}, "XHMug2V6u6yT": {"uuid": "5o2U-A5VF0v4", "name": "Test", "params": [], "states": [], "tplTree": {"__ref": "z6isWRvWn2jh"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "LlGnndGQwqPL"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "mYcmyyF9lJfB": {"component": {"__ref": "XHMug2V6u6yT"}, "matrix": {"__ref": "j4deGyI3n11-"}, "customMatrix": {"__ref": "3_qjh5IxKuk-"}, "__type": "ComponentArena"}, "LlGnndGQwqPL": {"uuid": "M5HqTEcbKju0", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "j4deGyI3n11-": {"rows": [{"__ref": "ze7QVVCnA183"}], "__type": "ArenaFrameGrid"}, "3_qjh5IxKuk-": {"rows": [{"__ref": "yw8cp67gUTwd"}], "__type": "ArenaFrameGrid"}, "ze7QVVCnA183": {"cols": [{"__ref": "Yfi7SzBh1V2u"}], "rowKey": null, "__type": "ArenaFrameRow"}, "yw8cp67gUTwd": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "Yfi7SzBh1V2u": {"frame": {"__ref": "0MgCnVwIHCVI"}, "cellKey": {"__ref": "LlGnndGQwqPL"}, "__type": "ArenaFrameCell"}, "0MgCnVwIHCVI": {"uuid": "LgSUgY0u8qSc", "width": 1180, "height": 540, "container": {"__ref": "eb_dr27OdLT5"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "LlGnndGQwqPL"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "eb_dr27OdLT5": {"name": null, "component": {"__ref": "XHMug2V6u6yT"}, "uuid": "hX2MsLkvRmht", "parent": null, "locked": null, "vsettings": [{"__ref": "M56xc4fKVUeM"}], "__type": "TplComponent"}, "M56xc4fKVUeM": {"variants": [{"__ref": "BM3CyjR6PREH"}], "args": [], "attrs": {}, "rs": {"__ref": "ytX4tIUys93B"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ytX4tIUys93B": {"values": {}, "mixins": [], "__type": "RuleSet"}, "OBbOy4AkwwkT": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "nuXUX1c6wCJt", "parent": {"__ref": "z6isWRvWn2jh"}, "locked": null, "vsettings": [{"__ref": "Vm7w8uOGGWHr"}], "__type": "TplTag"}, "Vm7w8uOGGWHr": {"variants": [{"__ref": "LlGnndGQwqPL"}], "args": [], "attrs": {}, "rs": {"__ref": "3_6FNpbTOhGD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3_6FNpbTOhGD": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px"}, "mixins": [], "__type": "RuleSet"}, "z6isWRvWn2jh": {"tag": "div", "name": null, "children": [{"__ref": "OBbOy4AkwwkT"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "kXLBN8Hl829b", "parent": null, "locked": null, "vsettings": [{"__ref": "9TJkKZVCiXJe"}], "__type": "TplTag"}, "9TJkKZVCiXJe": {"variants": [{"__ref": "LlGnndGQwqPL"}], "args": [], "attrs": {}, "rs": {"__ref": "9LmfXpHcp7TJ"}, "dataCond": {"__ref": "icp-UNYj1XKh"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9LmfXpHcp7TJ": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "icp-UNYj1XKh": {"code": "true", "fallback": null, "__type": "CustomCode"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}}, {"branchId": "main", "data": {"root": "Mgq9mDrXxf1Y", "map": {"rtH3qI7L415j": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "1fLFumwuth8K": {"name": "Default Typography", "rs": {"__ref": "rtH3qI7L415j"}, "preview": null, "uuid": "sagEmhwjYOTv", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "BVAkgseP-CCN": {"values": {}, "mixins": [], "__type": "RuleSet"}, "SP2PWWxkt0m2": {"rs": {"__ref": "BVAkgseP-CCN"}, "__type": "ThemeLayoutSettings"}, "cvvQFii1k4BV": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "N-pVtSn_n0zF": {"name": "Default \"h1\"", "rs": {"__ref": "cvvQFii1k4BV"}, "preview": null, "uuid": "a6mQakstSSb4", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "TSbaacmeT3pO": {"selector": "h1", "style": {"__ref": "N-pVtSn_n0zF"}, "__type": "ThemeStyle"}, "7gC2lqy7T1BZ": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "lbegziOdqTpT": {"name": "Default \"h2\"", "rs": {"__ref": "7gC2lqy7T1BZ"}, "preview": null, "uuid": "Fqj9l7eVvVRQ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Rt6Kp90e90am": {"selector": "h2", "style": {"__ref": "lbegziOdqTpT"}, "__type": "ThemeStyle"}, "jUxQ_rCDUH-R": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "hveAsZ0hEnq6": {"name": "Default \"h3\"", "rs": {"__ref": "jUxQ_rCDUH-R"}, "preview": null, "uuid": "zE0GdkrnVwD7", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "n_wYcbdSBu5c": {"selector": "h3", "style": {"__ref": "hveAsZ0hEnq6"}, "__type": "ThemeStyle"}, "f-JVl75nBMC6": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "HtFfryIXI-43": {"name": "Default \"h4\"", "rs": {"__ref": "f-JVl75nBMC6"}, "preview": null, "uuid": "KUFwR3EAOnM0", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "iuFFhWvNtxoy": {"selector": "h4", "style": {"__ref": "HtFfryIXI-43"}, "__type": "ThemeStyle"}, "X86rGAeSOUMf": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "XJfgBHB02oRq": {"name": "Default \"h5\"", "rs": {"__ref": "X86rGAeSOUMf"}, "preview": null, "uuid": "DdIdJg54vX8n", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ts3_I66D1MPI": {"selector": "h5", "style": {"__ref": "XJfgBHB02oRq"}, "__type": "ThemeStyle"}, "oPuOJF1qFR8k": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "5R0CNHcUANBU": {"name": "Default \"h6\"", "rs": {"__ref": "oPuOJF1qFR8k"}, "preview": null, "uuid": "Jo3U8t7XTYPO", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Ci54heeaBkw4": {"selector": "h6", "style": {"__ref": "5R0CNHcUANBU"}, "__type": "ThemeStyle"}, "s4epEyOGiffY": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "bGRWpAemJxYO": {"name": "Default \"a\"", "rs": {"__ref": "s4epEyOGiffY"}, "preview": null, "uuid": "VEpAY9G4fAsp", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "2H6QtOHDSeuu": {"selector": "a", "style": {"__ref": "bGRWpAemJxYO"}, "__type": "ThemeStyle"}, "39P_5NDX3Yg4": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "1g9-hvXSp-H6": {"name": "Default \"a:hover\"", "rs": {"__ref": "39P_5NDX3Yg4"}, "preview": null, "uuid": "DrtH_8PmDu0U", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "1mgTigoScETJ": {"selector": "a:hover", "style": {"__ref": "1g9-hvXSp-H6"}, "__type": "ThemeStyle"}, "l-7somvmAiFg": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "ekZQ9vXL8RHe": {"name": "Default \"blockquote\"", "rs": {"__ref": "l-7somvmAiFg"}, "preview": null, "uuid": "nDc0dI9YwsuK", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "umpOYLMj3IBi": {"selector": "blockquote", "style": {"__ref": "ekZQ9vXL8RHe"}, "__type": "ThemeStyle"}, "EVeK8uNZ3fp3": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "NbfGIq7obiGd": {"name": "Default \"code\"", "rs": {"__ref": "EVeK8uNZ3fp3"}, "preview": null, "uuid": "okl61CsLJAw7", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Af8xBi5A8a8g": {"selector": "code", "style": {"__ref": "NbfGIq7obiGd"}, "__type": "ThemeStyle"}, "Og8vedb-uqkf": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "rxCOi8l7bi78": {"name": "Default \"pre\"", "rs": {"__ref": "Og8vedb-uqkf"}, "preview": null, "uuid": "BRhb2LGnCjLb", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "A7D7wzAv3qG-": {"selector": "pre", "style": {"__ref": "rxCOi8l7bi78"}, "__type": "ThemeStyle"}, "dbYyBrnzs3d3": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "9FqB8TLizCkB": {"name": "Default \"ol\"", "rs": {"__ref": "dbYyBrnzs3d3"}, "preview": null, "uuid": "FzB8VfyIKsrh", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "IzRxdWXfI6Us": {"selector": "ol", "style": {"__ref": "9FqB8TLizCkB"}, "__type": "ThemeStyle"}, "EqD4k-fz2vwx": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "XK0EIgLTvWt4": {"name": "Default \"ul\"", "rs": {"__ref": "EqD4k-fz2vwx"}, "preview": null, "uuid": "F6CBg_hTZRJ6", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "BbvvUybfhLEe": {"selector": "ul", "style": {"__ref": "XK0EIgLTvWt4"}, "__type": "ThemeStyle"}, "9KCiiDBSorcv": {"defaultStyle": {"__ref": "1fLFumwuth8K"}, "styles": [{"__ref": "TSbaacmeT3pO"}, {"__ref": "Rt6Kp90e90am"}, {"__ref": "n_wYcbdSBu5c"}, {"__ref": "iuFFhWvNtxoy"}, {"__ref": "ts3_I66D1MPI"}, {"__ref": "Ci54heeaBkw4"}, {"__ref": "2H6QtOHDSeuu"}, {"__ref": "1mgTigoScETJ"}, {"__ref": "umpOYLMj3IBi"}, {"__ref": "Af8xBi5A8a8g"}, {"__ref": "A7D7wzAv3qG-"}, {"__ref": "IzRxdWXfI6Us"}, {"__ref": "BbvvUybfhLEe"}], "layout": {"__ref": "SP2PWWxkt0m2"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "8nBYE85F8tK3": {"name": "text", "__type": "Text"}, "WhnHtmZ_iv-x": {"name": "Screen", "uuid": "9gJVhZx78hqY", "__type": "Var"}, "z4QMZMa2zo00": {"type": {"__ref": "8nBYE85F8tK3"}, "variable": {"__ref": "WhnHtmZ_iv-x"}, "uuid": "fKh5bjBgOKLd", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "sabKQqOjEw-m": {"type": "global-screen", "param": {"__ref": "z4QMZMa2zo00"}, "uuid": "xZmoAniT4wNV", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "BM3CyjR6PREH": {"uuid": "I7_XstFXzjGo", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Mgq9mDrXxf1Y": {"components": [{"__ref": "gcp-I4c9H1d1"}, {"__ref": "b3kNX_TdMIFo"}, {"__ref": "XHMug2V6u6yT"}], "arenas": [], "pageArenas": [], "componentArenas": [{"__ref": "mYcmyyF9lJfB"}], "globalVariantGroups": [{"__ref": "sabKQqOjEw-m"}], "userManagedFonts": [], "globalVariant": {"__ref": "BM3CyjR6PREH"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "9KCiiDBSorcv"}], "activeTheme": {"__ref": "9KCiiDBSorcv"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "sabKQqOjEw-m"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "gcp-I4c9H1d1": {"uuid": "U_fhnoosZ7Db", "name": "hostless-plasmic-head", "params": [{"__ref": "BhZY-ZzTkguj"}, {"__ref": "wZ0sdohDIPEm"}, {"__ref": "hiJsGCL2mJ5Q"}, {"__ref": "57VTsyCYgG-F"}], "states": [], "tplTree": {"__ref": "F167GOzF4and"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "I3Bh_D87PXms"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "4BuFqnuQppTZ"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "b3kNX_TdMIFo": {"uuid": "zZQVXzbZDClb", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "s6tO-w_oxl8R"}, {"__ref": "-0y9FETBa5Ml"}, {"__ref": "YGv2gd6Y420z"}, {"__ref": "lzLCvqozElaP"}, {"__ref": "o7PWQIHjQtly"}], "states": [], "tplTree": {"__ref": "UM5nkp0liC3Y"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "BnExkTBlWt3X"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "PovgJWLYYDEa"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component"}, "BhZY-ZzTkguj": {"type": {"__ref": "0pGeXrADFz6E"}, "variable": {"__ref": "UaN1AFzlCTTm"}, "uuid": "yrsRqvHfPTBq", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "wZ0sdohDIPEm": {"type": {"__ref": "f1bppQceBhIc"}, "variable": {"__ref": "QRAvMZ69oS17"}, "uuid": "5s16PGVHnb5z", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "hiJsGCL2mJ5Q": {"type": {"__ref": "wcee0p59WHO_"}, "variable": {"__ref": "O6gV9maQ3H6S"}, "uuid": "xECPLI4sIZow", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "57VTsyCYgG-F": {"type": {"__ref": "gbWOsB0DM410"}, "variable": {"__ref": "Gft7UMJNIu4u"}, "uuid": "7gARgRtAAamA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "F167GOzF4and": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "qVgxiRh0YS9W", "parent": null, "locked": null, "vsettings": [{"__ref": "3vSsjE3JCnBW"}], "__type": "TplTag"}, "I3Bh_D87PXms": {"uuid": "fBa5fK4AkXlF", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "4BuFqnuQppTZ": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "s6tO-w_oxl8R": {"type": {"__ref": "kXWE95a_KQen"}, "variable": {"__ref": "o05oSKcSV_1n"}, "uuid": "IDMn4tmgA12n", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "-0y9FETBa5Ml": {"type": {"__ref": "xf1HBIXAOdQC"}, "variable": {"__ref": "-CN3Q9pgMDCr"}, "uuid": "ayoSsFneIxAe", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "YGv2gd6Y420z": {"type": {"__ref": "NWmGpsLzSYM_"}, "tplSlot": {"__ref": "Dm15C1lfrHgk"}, "variable": {"__ref": "TI8vBek4XK6R"}, "uuid": "O-AxbHZWOeKG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "lzLCvqozElaP": {"type": {"__ref": "PUKNtlyX4GUx"}, "variable": {"__ref": "bzHAakq11OlZ"}, "uuid": "6W-PGaYccBo2", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "o7PWQIHjQtly": {"type": {"__ref": "_0RzfhFZzy13"}, "variable": {"__ref": "MjnaSp7ouUTr"}, "uuid": "-t5pIdRQIuMI", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "UM5nkp0liC3Y": {"tag": "div", "name": null, "children": [{"__ref": "Dm15C1lfrHgk"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "BuNYC8Bqk0ji", "parent": null, "locked": null, "vsettings": [{"__ref": "wFPcwPY_2bc8"}], "__type": "TplTag"}, "BnExkTBlWt3X": {"uuid": "C7wxqkRJSmAR", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "PovgJWLYYDEa": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "0pGeXrADFz6E": {"name": "text", "__type": "Text"}, "UaN1AFzlCTTm": {"name": "title", "uuid": "EHHW6xOiX7az", "__type": "Var"}, "f1bppQceBhIc": {"name": "text", "__type": "Text"}, "QRAvMZ69oS17": {"name": "description", "uuid": "HxKVM6f8_MVg", "__type": "Var"}, "wcee0p59WHO_": {"name": "img", "__type": "Img"}, "O6gV9maQ3H6S": {"name": "image", "uuid": "zqxNv3q2oxCF", "__type": "Var"}, "gbWOsB0DM410": {"name": "text", "__type": "Text"}, "Gft7UMJNIu4u": {"name": "canonical", "uuid": "UvBP-LsIetOF", "__type": "Var"}, "3vSsjE3JCnBW": {"variants": [{"__ref": "I3Bh_D87PXms"}], "args": [], "attrs": {}, "rs": {"__ref": "vTiozrBLAX4D"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kXWE95a_KQen": {"name": "any", "__type": "AnyType"}, "o05oSKcSV_1n": {"name": "dataOp", "uuid": "-nswZuROfzbz", "__type": "Var"}, "xf1HBIXAOdQC": {"name": "text", "__type": "Text"}, "-CN3Q9pgMDCr": {"name": "name", "uuid": "VDbmJAbeZoyE", "__type": "Var"}, "NWmGpsLzSYM_": {"name": "renderFunc", "params": [{"__ref": "HD0nOe8Qw4jw"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "TI8vBek4XK6R": {"name": "children", "uuid": "KsfCDsQFwFTs", "__type": "Var"}, "PUKNtlyX4GUx": {"name": "num", "__type": "<PERSON><PERSON>"}, "bzHAakq11OlZ": {"name": "pageSize", "uuid": "UCpscAos_0b0", "__type": "Var"}, "_0RzfhFZzy13": {"name": "num", "__type": "<PERSON><PERSON>"}, "MjnaSp7ouUTr": {"name": "pageIndex", "uuid": "6HuZ6Jmh3pTf", "__type": "Var"}, "Dm15C1lfrHgk": {"param": {"__ref": "YGv2gd6Y420z"}, "defaultContents": [], "uuid": "b_h2Pt8-5reo", "parent": {"__ref": "UM5nkp0liC3Y"}, "locked": null, "vsettings": [{"__ref": "7RAGJkDNDsx7"}], "__type": "TplSlot"}, "wFPcwPY_2bc8": {"variants": [{"__ref": "BnExkTBlWt3X"}], "args": [], "attrs": {}, "rs": {"__ref": "B_i6Ktv0bxRP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "vTiozrBLAX4D": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "HD0nOe8Qw4jw": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "MyiCeqZg5ocp"}, "__type": "ArgType"}, "7RAGJkDNDsx7": {"variants": [{"__ref": "BnExkTBlWt3X"}], "args": [], "attrs": {}, "rs": {"__ref": "PJFDVzGzntiu"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "B_i6Ktv0bxRP": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "MyiCeqZg5ocp": {"name": "any", "__type": "AnyType"}, "PJFDVzGzntiu": {"values": {}, "mixins": [], "__type": "RuleSet"}, "XHMug2V6u6yT": {"uuid": "5o2U-A5VF0v4", "name": "Test", "params": [], "states": [], "tplTree": {"__ref": "PtRrF7gywoLu"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "LlGnndGQwqPL"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "mYcmyyF9lJfB": {"component": {"__ref": "XHMug2V6u6yT"}, "matrix": {"__ref": "j4deGyI3n11-"}, "customMatrix": {"__ref": "3_qjh5IxKuk-"}, "__type": "ComponentArena"}, "PtRrF7gywoLu": {"tag": "div", "name": null, "children": [{"__ref": "OBbOy4AkwwkT"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "FUn4j9VYQ7_o", "parent": null, "locked": null, "vsettings": [{"__ref": "SqqiVORcnrVb"}], "__type": "TplTag"}, "LlGnndGQwqPL": {"uuid": "M5HqTEcbKju0", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "j4deGyI3n11-": {"rows": [{"__ref": "ze7QVVCnA183"}], "__type": "ArenaFrameGrid"}, "3_qjh5IxKuk-": {"rows": [{"__ref": "yw8cp67gUTwd"}], "__type": "ArenaFrameGrid"}, "SqqiVORcnrVb": {"variants": [{"__ref": "LlGnndGQwqPL"}], "args": [], "attrs": {}, "rs": {"__ref": "qFx3upfB3VcG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ze7QVVCnA183": {"cols": [{"__ref": "Yfi7SzBh1V2u"}], "rowKey": null, "__type": "ArenaFrameRow"}, "yw8cp67gUTwd": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "qFx3upfB3VcG": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "Yfi7SzBh1V2u": {"frame": {"__ref": "0MgCnVwIHCVI"}, "cellKey": {"__ref": "LlGnndGQwqPL"}, "__type": "ArenaFrameCell"}, "0MgCnVwIHCVI": {"uuid": "LgSUgY0u8qSc", "width": 1180, "height": 540, "container": {"__ref": "eb_dr27OdLT5"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "LlGnndGQwqPL"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "eb_dr27OdLT5": {"name": null, "component": {"__ref": "XHMug2V6u6yT"}, "uuid": "hX2MsLkvRmht", "parent": null, "locked": null, "vsettings": [{"__ref": "M56xc4fKVUeM"}], "__type": "TplComponent"}, "M56xc4fKVUeM": {"variants": [{"__ref": "BM3CyjR6PREH"}], "args": [], "attrs": {}, "rs": {"__ref": "ytX4tIUys93B"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ytX4tIUys93B": {"values": {}, "mixins": [], "__type": "RuleSet"}, "OBbOy4AkwwkT": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "nuXUX1c6wCJt", "parent": {"__ref": "PtRrF7gywoLu"}, "locked": null, "vsettings": [{"__ref": "Vm7w8uOGGWHr"}], "__type": "TplTag"}, "Vm7w8uOGGWHr": {"variants": [{"__ref": "LlGnndGQwqPL"}], "args": [], "attrs": {}, "rs": {"__ref": "3_6FNpbTOhGD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3_6FNpbTOhGD": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px"}, "mixins": [], "__type": "RuleSet"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}}]}