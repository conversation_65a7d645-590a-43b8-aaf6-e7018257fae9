[["rqScarHZ7pjA4hGcZFnYtH", {"root": "33859001", "map": {"2001401": {"type": {"__ref": "2001403"}, "variable": {"__ref": "2001402"}, "uuid": "lK5p7ozwUz", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "2001402": {"name": "submitsForm", "uuid": "zNSgJsjZGa", "__type": "Var"}, "2001403": {"name": "bool", "__type": "BoolType"}, "2001404": {"param": {"__ref": "2001401"}, "expr": {"__ref": "2001405"}, "__type": "Arg"}, "2001405": {"code": "true", "fallback": null, "__type": "CustomCode"}, "2001406": {"param": {"__ref": "2001401"}, "expr": {"__ref": "2001407"}, "__type": "Arg"}, "2001407": {"code": "true", "fallback": null, "__type": "CustomCode"}, "2001408": {"param": {"__ref": "2001401"}, "expr": {"__ref": "2001409"}, "__type": "Arg"}, "2001409": {"code": "true", "fallback": null, "__type": "CustomCode"}, "2001410": {"param": {"__ref": "2001401"}, "expr": {"__ref": "2001411"}, "__type": "Arg"}, "2001411": {"code": "true", "fallback": null, "__type": "CustomCode"}, "3312801": {"rows": [{"__ref": "3312802"}], "__type": "ArenaFrameGrid"}, "3312802": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "3378002": {"type": {"__ref": "3378006"}, "tplSlot": {"__ref": "3378003"}, "variable": {"__ref": "3378005"}, "uuid": "weMWfU0lXk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "3378003": {"param": {"__ref": "3378002"}, "defaultContents": [{"__ref": "3378007"}], "uuid": "2bIB8gdpIe", "parent": {"__ref": "3378020"}, "locked": null, "vsettings": [{"__ref": "3378008"}], "__type": "TplSlot"}, "3378004": {"param": {"__ref": "3378002"}, "expr": {"__ref": "3378019"}, "__type": "Arg"}, "3378005": {"name": "children", "uuid": "hv-swMc4w", "__type": "Var"}, "3378006": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "3378007": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "HHpiXR8DwJ", "parent": {"__ref": "3378003"}, "locked": null, "vsettings": [{"__ref": "3378010"}], "__type": "TplTag"}, "3378008": {"variants": [{"__ref": "36693109"}], "args": [], "attrs": {}, "rs": {"__ref": "3378011"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378010": {"variants": [{"__ref": "36693109"}], "args": [], "attrs": {}, "rs": {"__ref": "3378013"}, "dataCond": null, "dataRep": null, "text": {"__ref": "3378014"}, "columnsConfig": null, "__type": "VariantSetting"}, "3378011": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378012": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "1yPDDE79wn", "parent": {"__ref": "64439090"}, "locked": null, "vsettings": [{"__ref": "3378015"}], "__type": "TplTag"}, "3378013": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378014": {"markers": [], "text": "Label", "__type": "RawText"}, "3378015": {"variants": [{"__ref": "36693076"}], "args": [], "attrs": {}, "rs": {"__ref": "3378016"}, "dataCond": null, "dataRep": null, "text": {"__ref": "3378018"}, "columnsConfig": null, "__type": "VariantSetting"}, "3378016": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378018": {"markers": [], "text": "First Name", "__type": "RawText"}, "3378019": {"tpl": [{"__ref": "3378012"}], "__type": "RenderExpr"}, "3378020": {"tag": "div", "name": null, "children": [{"__ref": "3378003"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "fHIADJHZs", "parent": {"__ref": "36693108"}, "locked": null, "vsettings": [{"__ref": "3378021"}], "__type": "TplTag"}, "3378021": {"variants": [{"__ref": "36693109"}], "args": [], "attrs": {}, "rs": {"__ref": "3378022"}, "dataCond": {"__ref": "3378023"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378022": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "plasmic-display-none": "false", "min-width": "100px"}, "mixins": [], "__type": "RuleSet"}, "3378023": {"code": "true", "fallback": null, "__type": "CustomCode"}, "3378031": {"name": "LastName", "component": {"__ref": "36693106"}, "uuid": "LuGUAlRkOr", "parent": {"__ref": "36693075"}, "locked": null, "vsettings": [{"__ref": "3378034"}], "__type": "TplComponent"}, "3378032": {"param": {"__ref": "3378033"}, "accessType": "private", "variableType": "text", "onChangeParam": {"__ref": "64439099"}, "tplNode": {"__ref": "3378031"}, "implicitState": {"__ref": "64439002"}, "__type": "State"}, "3378033": {"type": {"__ref": "3378036"}, "state": {"__ref": "3378032"}, "variable": {"__ref": "3378035"}, "uuid": "uGMzVkH3oM", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "3378034": {"variants": [{"__ref": "36693076"}], "args": [{"__ref": "3378037"}, {"__ref": "3378038"}, {"__ref": "3379381"}, {"__ref": "6345018"}], "attrs": {}, "rs": {"__ref": "3378039"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378035": {"name": "FormField value", "uuid": "B65qzle3b7", "__type": "Var"}, "3378036": {"name": "text", "__type": "Text"}, "3378037": {"param": {"__ref": "64439003"}, "expr": {"__ref": "3379369"}, "__type": "Arg"}, "3378038": {"param": {"__ref": "3378002"}, "expr": {"__ref": "3378041"}, "__type": "Arg"}, "3378039": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "3378041": {"tpl": [{"__ref": "3378044"}], "__type": "RenderExpr"}, "3378044": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "oYr70Tqrse", "parent": {"__ref": "3378031"}, "locked": null, "vsettings": [{"__ref": "3378045"}], "__type": "TplTag"}, "3378045": {"variants": [{"__ref": "36693076"}], "args": [], "attrs": {}, "rs": {"__ref": "3378046"}, "dataCond": null, "dataRep": null, "text": {"__ref": "3378048"}, "columnsConfig": null, "__type": "VariantSetting"}, "3378046": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378048": {"markers": [], "text": "Last Name", "__type": "RawText"}, "3378052": {"uuid": "p3sLnfPQ7v", "name": "Nicknames", "params": [{"__ref": "3378923"}, {"__ref": "3379393"}, {"__ref": "6345031"}, {"__ref": "6345097"}, {"__ref": "64439134"}, {"__ref": "64439149"}], "states": [{"__ref": "3378922"}, {"__ref": "3379392"}], "tplTree": {"__ref": "3378961"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "3378055"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [{"__ref": "3379461"}], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "3378053": {"component": {"__ref": "3378052"}, "matrix": {"__ref": "3378056"}, "customMatrix": {"__ref": "3378057"}, "__type": "ComponentArena"}, "3378054": {"tag": "div", "name": null, "children": [{"__ref": "3379130"}, {"__ref": "3379015"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "T1fW2U7qz", "parent": {"__ref": "3378961"}, "locked": null, "vsettings": [{"__ref": "3378058"}], "__type": "TplTag"}, "3378055": {"uuid": "A0ZsrxNQf8", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378056": {"rows": [{"__ref": "3378059"}], "__type": "ArenaFrameGrid"}, "3378057": {"rows": [{"__ref": "3378060"}], "__type": "ArenaFrameGrid"}, "3378058": {"variants": [{"__ref": "3378055"}], "args": [], "attrs": {}, "rs": {"__ref": "3378061"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378059": {"cols": [{"__ref": "3378062"}], "rowKey": null, "__type": "ArenaFrameRow"}, "3378060": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "3378061": {"values": {"display": "flex", "flex-direction": "column", "width": "stretch", "height": "wrap", "flex-row-gap": "10px"}, "mixins": [], "__type": "RuleSet"}, "3378062": {"frame": {"__ref": "3378068"}, "cellKey": {"__ref": "3378055"}, "__type": "ArenaFrameCell"}, "3378068": {"uuid": "usKsAQNC83", "width": 340, "height": 340, "container": {"__ref": "3378069"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378055"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378069": {"name": null, "component": {"__ref": "3378052"}, "uuid": "wWxsQZW_25", "parent": null, "locked": null, "vsettings": [{"__ref": "3378070"}], "__type": "TplComponent"}, "3378070": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378071"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378071": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378087": {"uuid": "ZTjirRZFGbs", "name": "icon", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHN0cm9rZT0iY3VycmVudENvbG9yIiBmaWxsPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMCIgdmlld0JveD0iMCAwIDE2IDE2IiBoZWlnaHQ9IjFlbSIgd2lkdGg9IjFlbSI+CiAgPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBkPSJNMSA4YS41LjUgMCAwMS41LS41aDExLjc5M2wtMy4xNDctMy4xNDZhLjUuNSAwIDAxLjcwOC0uNzA4bDQgNGEuNS41IDAgMDEwIC43MDhsLTQgNGEuNS41IDAgMDEtLjcwOC0uNzA4TDEzLjI5MyA4LjVIMS41QS41LjUgMCAwMTEgOHoiIHN0cm9rZT0ibm9uZSIvPgo8L3N2Zz4=", "width": 150, "height": 150, "aspectRatio": 1000000, "__type": "ImageAsset"}, "3378088": {"uuid": "45zd_SDBQSO", "name": "<PERSON><PERSON>", "params": [{"__ref": "3378091"}, {"__ref": "3378092"}, {"__ref": "3378093"}, {"__ref": "3378094"}, {"__ref": "3378095"}, {"__ref": "3378096"}, {"__ref": "3378097"}, {"__ref": "3378098"}, {"__ref": "3378099"}, {"__ref": "3378100"}, {"__ref": "60137002"}, {"__ref": "2001401"}, {"__ref": "64439104"}, {"__ref": "64439109"}, {"__ref": "64439114"}, {"__ref": "64439119"}, {"__ref": "64439124"}, {"__ref": "64439129"}], "states": [{"__ref": "3378101"}, {"__ref": "3378102"}, {"__ref": "3378103"}, {"__ref": "3378104"}, {"__ref": "3378105"}, {"__ref": "3378106"}], "tplTree": {"__ref": "3378107"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "3378108"}, {"__ref": "3378109"}, {"__ref": "3378110"}, {"__ref": "3378111"}, {"__ref": "3378112"}], "variantGroups": [{"__ref": "3378113"}, {"__ref": "3378114"}, {"__ref": "3378115"}, {"__ref": "3378116"}, {"__ref": "3378117"}, {"__ref": "3378118"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": {"__ref": "3378119"}, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "3378089": {"component": {"__ref": "3378088"}, "matrix": {"__ref": "3378120"}, "customMatrix": {"__ref": "3378121"}, "__type": "ComponentArena"}, "3378090": {"name": null, "component": {"__ref": "3378088"}, "uuid": "0TOZ27WR8X2", "parent": {"__ref": "3379130"}, "locked": null, "vsettings": [{"__ref": "3378122"}], "__type": "TplComponent"}, "3378091": {"type": {"__ref": "3378124"}, "tplSlot": {"__ref": "3378244"}, "variable": {"__ref": "3378123"}, "uuid": "IydXEKVucV", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "3378092": {"type": {"__ref": "3378126"}, "state": {"__ref": "3378101"}, "variable": {"__ref": "3378125"}, "uuid": "VKVZo98Djs", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "3378093": {"type": {"__ref": "3378128"}, "state": {"__ref": "3378102"}, "variable": {"__ref": "3378127"}, "uuid": "apT0jdpHJb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "3378094": {"type": {"__ref": "3378130"}, "tplSlot": {"__ref": "3378239"}, "variable": {"__ref": "3378129"}, "uuid": "mRFnBrN1tG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "3378095": {"type": {"__ref": "3378132"}, "tplSlot": {"__ref": "3378250"}, "variable": {"__ref": "3378131"}, "uuid": "GRjxUX1TiL", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "3378096": {"type": {"__ref": "3378134"}, "state": {"__ref": "3378103"}, "variable": {"__ref": "3378133"}, "uuid": "cCsNv3v5dr", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "3378097": {"type": {"__ref": "3378136"}, "variable": {"__ref": "3378135"}, "uuid": "KSEqadiYnz", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "3378098": {"type": {"__ref": "3378138"}, "state": {"__ref": "3378106"}, "variable": {"__ref": "3378137"}, "uuid": "Ox-4JPXsqE", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "3378099": {"type": {"__ref": "3378140"}, "state": {"__ref": "3378105"}, "variable": {"__ref": "3378139"}, "uuid": "M3qIlX4WsjK", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "3378100": {"type": {"__ref": "3378142"}, "state": {"__ref": "3378104"}, "variable": {"__ref": "3378141"}, "uuid": "x2kyIxlukI9", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "3378101": {"variantGroup": {"__ref": "3378113"}, "param": {"__ref": "3378092"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64439104"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "3378102": {"variantGroup": {"__ref": "3378114"}, "param": {"__ref": "3378093"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64439109"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "3378103": {"variantGroup": {"__ref": "3378115"}, "param": {"__ref": "3378096"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64439114"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "3378104": {"variantGroup": {"__ref": "3378116"}, "param": {"__ref": "3378100"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64439119"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "3378105": {"variantGroup": {"__ref": "3378117"}, "param": {"__ref": "3378099"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64439124"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "3378106": {"variantGroup": {"__ref": "3378118"}, "param": {"__ref": "3378098"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64439129"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "3378107": {"tag": "button", "name": null, "children": [{"__ref": "3378143"}, {"__ref": "3378144"}, {"__ref": "3378145"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "KN84XAlFZ3R", "parent": null, "locked": null, "vsettings": [{"__ref": "3378146"}, {"__ref": "3378147"}, {"__ref": "3378148"}, {"__ref": "3378149"}, {"__ref": "3378150"}, {"__ref": "3378151"}, {"__ref": "3378152"}, {"__ref": "3378153"}, {"__ref": "3378154"}, {"__ref": "3378155"}, {"__ref": "3378156"}, {"__ref": "3378157"}, {"__ref": "3378158"}, {"__ref": "3378159"}, {"__ref": "3378160"}, {"__ref": "3378161"}, {"__ref": "3378162"}, {"__ref": "3378163"}, {"__ref": "3378164"}, {"__ref": "3378165"}, {"__ref": "3378166"}, {"__ref": "3378167"}, {"__ref": "3378168"}, {"__ref": "3378169"}, {"__ref": "3378170"}, {"__ref": "3378171"}, {"__ref": "3378172"}, {"__ref": "3378173"}, {"__ref": "3378174"}, {"__ref": "3378175"}, {"__ref": "3378176"}, {"__ref": "3378177"}, {"__ref": "3378178"}, {"__ref": "3378179"}, {"__ref": "3378180"}, {"__ref": "3378181"}, {"__ref": "3378182"}, {"__ref": "3378183"}, {"__ref": "3378184"}, {"__ref": "3378185"}, {"__ref": "3378186"}, {"__ref": "3378187"}, {"__ref": "3378188"}, {"__ref": "3378189"}, {"__ref": "3378190"}, {"__ref": "3378191"}, {"__ref": "3378192"}, {"__ref": "3378193"}, {"__ref": "3378194"}, {"__ref": "3378195"}, {"__ref": "3378196"}, {"__ref": "3378197"}, {"__ref": "3378198"}, {"__ref": "3378199"}, {"__ref": "3378200"}, {"__ref": "3378201"}, {"__ref": "3378202"}, {"__ref": "3378203"}, {"__ref": "3378204"}, {"__ref": "3378205"}], "__type": "TplTag"}, "3378108": {"uuid": "7bnEZH6QdOQ", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378109": {"uuid": "klXwr7zi6Ah", "name": "", "selectors": [":focus-visible-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378110": {"uuid": "UHx_LsZ6vru", "name": "", "selectors": [":focus-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378111": {"uuid": "fjPTiwAqmel", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378112": {"uuid": "HnB-ZzJEYgV", "name": "", "selectors": [":active"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378113": {"type": "component", "param": {"__ref": "3378092"}, "linkedState": {"__ref": "3378101"}, "uuid": "SXYuQjmTPAS", "variants": [{"__ref": "3378206"}], "multi": false, "__type": "ComponentVariantGroup"}, "3378114": {"type": "component", "param": {"__ref": "3378093"}, "linkedState": {"__ref": "3378102"}, "uuid": "ar8zjJK5xSw", "variants": [{"__ref": "3378207"}], "multi": false, "__type": "ComponentVariantGroup"}, "3378115": {"type": "component", "param": {"__ref": "3378096"}, "linkedState": {"__ref": "3378103"}, "uuid": "OQr0-cdINht", "variants": [{"__ref": "3378208"}], "multi": false, "__type": "ComponentVariantGroup"}, "3378116": {"type": "component", "param": {"__ref": "3378100"}, "linkedState": {"__ref": "3378104"}, "uuid": "8Z_6dB2YbjD", "variants": [{"__ref": "3378209"}, {"__ref": "3378210"}, {"__ref": "3378211"}], "multi": false, "__type": "ComponentVariantGroup"}, "3378117": {"type": "component", "param": {"__ref": "3378099"}, "linkedState": {"__ref": "3378105"}, "uuid": "AfbBNuNrhus", "variants": [{"__ref": "3378212"}, {"__ref": "3378213"}], "multi": false, "__type": "ComponentVariantGroup"}, "3378118": {"type": "component", "param": {"__ref": "3378098"}, "linkedState": {"__ref": "3378106"}, "uuid": "h1ChWZxnXkw", "variants": [{"__ref": "3378214"}, {"__ref": "3378215"}, {"__ref": "3378216"}, {"__ref": "3378217"}, {"__ref": "3378218"}, {"__ref": "3378219"}, {"__ref": "3378220"}, {"__ref": "3378221"}, {"__ref": "3378222"}, {"__ref": "3378223"}, {"__ref": "3378224"}, {"__ref": "3378225"}, {"__ref": "3378226"}], "multi": false, "__type": "ComponentVariantGroup"}, "3378119": {"type": "button", "__type": "PlumeInfo"}, "3378120": {"rows": [{"__ref": "3378227"}, {"__ref": "3378228"}, {"__ref": "3378229"}, {"__ref": "3378230"}, {"__ref": "3378231"}, {"__ref": "3378232"}, {"__ref": "3378233"}], "__type": "ArenaFrameGrid"}, "3378121": {"rows": [{"__ref": "3378234"}], "__type": "ArenaFrameGrid"}, "3378122": {"variants": [{"__ref": "3378055"}], "args": [{"__ref": "3378235"}, {"__ref": "3378236"}, {"__ref": "3378237"}, {"__ref": "3378952"}, {"__ref": "60137010"}, {"__ref": "2001404"}], "attrs": {"onClick": {"__ref": "3379424"}}, "rs": {"__ref": "3378238"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378123": {"name": "children", "uuid": "e6CYxuB8B", "__type": "Var"}, "3378124": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "3378125": {"name": "Show Start Icon", "uuid": "obRScYDxub", "__type": "Var"}, "3378126": {"name": "any", "__type": "AnyType"}, "3378127": {"name": "Show End Icon", "uuid": "L0LjduiQjw", "__type": "Var"}, "3378128": {"name": "any", "__type": "AnyType"}, "3378129": {"name": "start icon", "uuid": "RZDEQY8LCy", "__type": "Var"}, "3378130": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "3378131": {"name": "end icon", "uuid": "-lo5bWQ7Gq", "__type": "Var"}, "3378132": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "3378133": {"name": "Is Disabled", "uuid": "Scs0cH6HsI", "__type": "Var"}, "3378134": {"name": "any", "__type": "AnyType"}, "3378135": {"name": "link", "uuid": "zA1EDFqRmy", "__type": "Var"}, "3378136": {"name": "href", "__type": "HrefType"}, "3378137": {"name": "Color", "uuid": "5b3PFtlI9m", "__type": "Var"}, "3378138": {"name": "any", "__type": "AnyType"}, "3378139": {"name": "Size", "uuid": "hv2w3xcUVgn", "__type": "Var"}, "3378140": {"name": "any", "__type": "AnyType"}, "3378141": {"name": "<PERSON><PERSON><PERSON>", "uuid": "Pwoh0m00w3Y", "__type": "Var"}, "3378142": {"name": "any", "__type": "AnyType"}, "3378143": {"tag": "div", "name": "start icon container", "children": [{"__ref": "3378239"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "HPaEv9OVE_R", "parent": {"__ref": "3378107"}, "locked": null, "vsettings": [{"__ref": "3378240"}, {"__ref": "3378241"}, {"__ref": "3378242"}, {"__ref": "3378243"}], "__type": "TplTag"}, "3378144": {"tag": "div", "name": "content container", "children": [{"__ref": "3378244"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "SYcdp7DQ2js", "parent": {"__ref": "3378107"}, "locked": null, "vsettings": [{"__ref": "3378245"}, {"__ref": "3378246"}, {"__ref": "3378247"}, {"__ref": "3378248"}, {"__ref": "3378249"}], "__type": "TplTag"}, "3378145": {"tag": "div", "name": "end icon container", "children": [{"__ref": "3378250"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "alrqQ_qOQ5d", "parent": {"__ref": "3378107"}, "locked": null, "vsettings": [{"__ref": "3378251"}, {"__ref": "3378252"}, {"__ref": "3378253"}, {"__ref": "3378254"}], "__type": "TplTag"}, "3378146": {"variants": [{"__ref": "3378108"}], "args": [], "attrs": {"data-testid": {"__ref": "60137003"}}, "rs": {"__ref": "3378255"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378147": {"variants": [{"__ref": "3378109"}], "args": [], "attrs": {}, "rs": {"__ref": "3378256"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378148": {"variants": [{"__ref": "3378110"}], "args": [], "attrs": {}, "rs": {"__ref": "3378257"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378149": {"variants": [{"__ref": "3378208"}], "args": [], "attrs": {}, "rs": {"__ref": "3378258"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378150": {"variants": [{"__ref": "3378207"}], "args": [], "attrs": {}, "rs": {"__ref": "3378259"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378151": {"variants": [{"__ref": "3378206"}], "args": [], "attrs": {}, "rs": {"__ref": "3378260"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378152": {"variants": [{"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378261"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378153": {"variants": [{"__ref": "3378112"}], "args": [], "attrs": {}, "rs": {"__ref": "3378262"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378154": {"variants": [{"__ref": "3378220"}], "args": [], "attrs": {}, "rs": {"__ref": "3378263"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378155": {"variants": [{"__ref": "3378220"}, {"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378264"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378156": {"variants": [{"__ref": "3378220"}, {"__ref": "3378112"}], "args": [], "attrs": {}, "rs": {"__ref": "3378265"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378157": {"variants": [{"__ref": "3378221"}], "args": [], "attrs": {}, "rs": {"__ref": "3378266"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378158": {"variants": [{"__ref": "3378221"}, {"__ref": "3378112"}], "args": [], "attrs": {}, "rs": {"__ref": "3378267"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378159": {"variants": [{"__ref": "3378221"}, {"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378268"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378160": {"variants": [{"__ref": "3378216"}], "args": [], "attrs": {}, "rs": {"__ref": "3378269"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378161": {"variants": [{"__ref": "3378216"}, {"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378270"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378162": {"variants": [{"__ref": "3378216"}, {"__ref": "3378112"}], "args": [], "attrs": {}, "rs": {"__ref": "3378271"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378163": {"variants": [{"__ref": "3378217"}], "args": [], "attrs": {}, "rs": {"__ref": "3378272"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378164": {"variants": [{"__ref": "3378217"}, {"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378273"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378165": {"variants": [{"__ref": "3378217"}, {"__ref": "3378112"}], "args": [], "attrs": {}, "rs": {"__ref": "3378274"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378166": {"variants": [{"__ref": "3378222"}], "args": [], "attrs": {}, "rs": {"__ref": "3378275"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378167": {"variants": [{"__ref": "3378222"}, {"__ref": "3378112"}], "args": [], "attrs": {}, "rs": {"__ref": "3378276"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378168": {"variants": [{"__ref": "3378222"}, {"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378277"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378169": {"variants": [{"__ref": "3378223"}], "args": [], "attrs": {}, "rs": {"__ref": "3378278"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378170": {"variants": [{"__ref": "3378223"}, {"__ref": "3378112"}], "args": [], "attrs": {}, "rs": {"__ref": "3378279"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378171": {"variants": [{"__ref": "3378223"}, {"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378280"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378172": {"variants": [{"__ref": "3378224"}], "args": [], "attrs": {}, "rs": {"__ref": "3378281"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378173": {"variants": [{"__ref": "3378224"}, {"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378282"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378174": {"variants": [{"__ref": "3378224"}, {"__ref": "3378112"}], "args": [], "attrs": {}, "rs": {"__ref": "3378283"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378175": {"variants": [{"__ref": "3378214"}], "args": [], "attrs": {}, "rs": {"__ref": "3378284"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378176": {"variants": [{"__ref": "3378214"}, {"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378285"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378177": {"variants": [{"__ref": "3378214"}, {"__ref": "3378112"}], "args": [], "attrs": {}, "rs": {"__ref": "3378286"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378178": {"variants": [{"__ref": "3378215"}], "args": [], "attrs": {}, "rs": {"__ref": "3378287"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378179": {"variants": [{"__ref": "3378215"}, {"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378288"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378180": {"variants": [{"__ref": "3378215"}, {"__ref": "3378112"}], "args": [], "attrs": {}, "rs": {"__ref": "3378289"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378181": {"variants": [{"__ref": "3378218"}], "args": [], "attrs": {}, "rs": {"__ref": "3378290"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378182": {"variants": [{"__ref": "3378218"}, {"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378291"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378183": {"variants": [{"__ref": "3378218"}, {"__ref": "3378112"}], "args": [], "attrs": {}, "rs": {"__ref": "3378292"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378184": {"variants": [{"__ref": "3378212"}], "args": [], "attrs": {}, "rs": {"__ref": "3378293"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378185": {"variants": [{"__ref": "3378212"}, {"__ref": "3378206"}], "args": [], "attrs": {}, "rs": {"__ref": "3378294"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378186": {"variants": [{"__ref": "3378212"}, {"__ref": "3378206"}, {"__ref": "3378207"}], "args": [], "attrs": {}, "rs": {"__ref": "3378295"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378187": {"variants": [{"__ref": "3378212"}, {"__ref": "3378207"}], "args": [], "attrs": {}, "rs": {"__ref": "3378296"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378188": {"variants": [{"__ref": "3378209"}], "args": [], "attrs": {}, "rs": {"__ref": "3378297"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378189": {"variants": [{"__ref": "3378209"}, {"__ref": "3378206"}], "args": [], "attrs": {}, "rs": {"__ref": "3378298"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378190": {"variants": [{"__ref": "3378207"}, {"__ref": "3378209"}], "args": [], "attrs": {}, "rs": {"__ref": "3378299"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378191": {"variants": [{"__ref": "3378212"}, {"__ref": "3378209"}], "args": [], "attrs": {}, "rs": {"__ref": "3378300"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378192": {"variants": [{"__ref": "3378225"}], "args": [], "attrs": {}, "rs": {"__ref": "3378301"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378193": {"variants": [{"__ref": "3378225"}, {"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378302"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378194": {"variants": [{"__ref": "3378225"}, {"__ref": "3378112"}], "args": [], "attrs": {}, "rs": {"__ref": "3378303"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378195": {"variants": [{"__ref": "3378210"}], "args": [], "attrs": {}, "rs": {"__ref": "3378304"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378196": {"variants": [{"__ref": "3378210"}, {"__ref": "3378212"}], "args": [], "attrs": {}, "rs": {"__ref": "3378305"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378197": {"variants": [{"__ref": "3378226"}], "args": [], "attrs": {}, "rs": {"__ref": "3378306"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378198": {"variants": [{"__ref": "3378226"}, {"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378307"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378199": {"variants": [{"__ref": "3378226"}, {"__ref": "3378112"}], "args": [], "attrs": {}, "rs": {"__ref": "3378308"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378200": {"variants": [{"__ref": "3378226"}, {"__ref": "3378213"}], "args": [], "attrs": {}, "rs": {"__ref": "3378309"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378201": {"variants": [{"__ref": "3378213"}], "args": [], "attrs": {}, "rs": {"__ref": "3378310"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378202": {"variants": [{"__ref": "3378219"}], "args": [], "attrs": {}, "rs": {"__ref": "3378311"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378203": {"variants": [{"__ref": "3378219"}, {"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378312"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378204": {"variants": [{"__ref": "3378219"}, {"__ref": "3378112"}], "args": [], "attrs": {}, "rs": {"__ref": "3378313"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378205": {"variants": [{"__ref": "3378211"}], "args": [], "attrs": {}, "rs": {"__ref": "3378314"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378206": {"uuid": "Bvzxtm82xHO", "name": "Show Start Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378113"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378207": {"uuid": "1Y4xTiYAvXX", "name": "Show End Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378114"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378208": {"uuid": "FZcAO8vXBkP", "name": "Is Disabled", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378115"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378209": {"uuid": "7x6ap-_30By", "name": "Rounded", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378116"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378210": {"uuid": "4fGDsm-Euwv", "name": "Round", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378116"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378211": {"uuid": "vA30BFaXn78", "name": "<PERSON>", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378116"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378212": {"uuid": "9U2QzcPGgQ8", "name": "Compact", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378117"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378213": {"uuid": "adV7MTfN5tR", "name": "Minimal", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378117"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378214": {"uuid": "ciaquswbIs1", "name": "Blue", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378118"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378215": {"uuid": "9s0S-leTkcW", "name": "Green", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378118"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378216": {"uuid": "9S4b2pGJD_x", "name": "Yellow", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378118"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378217": {"uuid": "T5O3OX-4Mzs", "name": "Red", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378118"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378218": {"uuid": "x0m16oQXLL9", "name": "Sand", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378118"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378219": {"uuid": "ZILPyxQKZOF", "name": "White", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378118"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378220": {"uuid": "2zHkykWACXT", "name": "Soft Blue", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378118"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378221": {"uuid": "fFB7W21HRJ9", "name": "Soft Green", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378118"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378222": {"uuid": "BuiRKeGchRX", "name": "Soft Yellow", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378118"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378223": {"uuid": "4DUNdn6MYeB", "name": "Soft Red", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378118"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378224": {"uuid": "xCkQEvctq6J", "name": "Soft Sand", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378118"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378225": {"uuid": "Vq2-rlZlu2b", "name": "Clear", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378118"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378226": {"uuid": "U3RqVz-BCPa", "name": "Link", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3378118"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3378227": {"cols": [{"__ref": "3378315"}, {"__ref": "3378316"}, {"__ref": "3378317"}, {"__ref": "3378318"}, {"__ref": "3378319"}], "rowKey": null, "__type": "ArenaFrameRow"}, "3378228": {"cols": [{"__ref": "3378320"}], "rowKey": {"__ref": "3378113"}, "__type": "ArenaFrameRow"}, "3378229": {"cols": [{"__ref": "3378321"}], "rowKey": {"__ref": "3378114"}, "__type": "ArenaFrameRow"}, "3378230": {"cols": [{"__ref": "3378322"}], "rowKey": {"__ref": "3378115"}, "__type": "ArenaFrameRow"}, "3378231": {"cols": [{"__ref": "3378323"}, {"__ref": "3378324"}, {"__ref": "3378325"}], "rowKey": {"__ref": "3378116"}, "__type": "ArenaFrameRow"}, "3378232": {"cols": [{"__ref": "3378326"}, {"__ref": "3378327"}], "rowKey": {"__ref": "3378117"}, "__type": "ArenaFrameRow"}, "3378233": {"cols": [{"__ref": "3378328"}, {"__ref": "3378329"}, {"__ref": "3378330"}, {"__ref": "3378331"}, {"__ref": "3378332"}, {"__ref": "3378333"}, {"__ref": "3378334"}, {"__ref": "3378335"}, {"__ref": "3378336"}, {"__ref": "3378337"}, {"__ref": "3378338"}, {"__ref": "3378339"}, {"__ref": "3378340"}], "rowKey": {"__ref": "3378118"}, "__type": "ArenaFrameRow"}, "3378234": {"cols": [{"__ref": "3378341"}, {"__ref": "3378342"}, {"__ref": "3378343"}, {"__ref": "3378344"}, {"__ref": "3378345"}, {"__ref": "3378346"}, {"__ref": "3378347"}, {"__ref": "3378348"}, {"__ref": "3378349"}, {"__ref": "3378350"}, {"__ref": "3378351"}, {"__ref": "3378352"}, {"__ref": "3378353"}, {"__ref": "3378354"}, {"__ref": "3378355"}, {"__ref": "3378356"}, {"__ref": "3378357"}, {"__ref": "3378358"}, {"__ref": "3378359"}, {"__ref": "3378360"}, {"__ref": "3378361"}, {"__ref": "3378362"}, {"__ref": "3378363"}, {"__ref": "3378364"}, {"__ref": "3378365"}, {"__ref": "3378366"}, {"__ref": "3378367"}, {"__ref": "3378368"}, {"__ref": "3378369"}], "rowKey": null, "__type": "ArenaFrameRow"}, "3378235": {"param": {"__ref": "3378094"}, "expr": {"__ref": "3379133"}, "__type": "Arg"}, "3378236": {"param": {"__ref": "3378091"}, "expr": {"__ref": "3378955"}, "__type": "Arg"}, "3378237": {"param": {"__ref": "3378095"}, "expr": {"__ref": "3379134"}, "__type": "Arg"}, "3378238": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "3378239": {"param": {"__ref": "3378094"}, "defaultContents": [{"__ref": "3378375"}], "uuid": "ddtocbCVngq", "parent": {"__ref": "3378143"}, "locked": null, "vsettings": [{"__ref": "3378376"}, {"__ref": "3378377"}, {"__ref": "3378378"}, {"__ref": "3378379"}, {"__ref": "3378380"}, {"__ref": "3378381"}, {"__ref": "3378382"}, {"__ref": "3378383"}, {"__ref": "3378384"}, {"__ref": "3378385"}, {"__ref": "3378386"}, {"__ref": "3378387"}, {"__ref": "3378388"}, {"__ref": "3378389"}], "__type": "TplSlot"}, "3378240": {"variants": [{"__ref": "3378108"}], "args": [], "attrs": {}, "rs": {"__ref": "3378390"}, "dataCond": {"__ref": "3378391"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378241": {"variants": [{"__ref": "3378206"}], "args": [], "attrs": {}, "rs": {"__ref": "3378392"}, "dataCond": {"__ref": "3378393"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378242": {"variants": [{"__ref": "3378214"}], "args": [], "attrs": {}, "rs": {"__ref": "3378394"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378243": {"variants": [{"__ref": "3378209"}, {"__ref": "3378206"}], "args": [], "attrs": {}, "rs": {"__ref": "3378395"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378244": {"param": {"__ref": "3378091"}, "defaultContents": [{"__ref": "3378396"}], "uuid": "ywi_TT9y0HZ", "parent": {"__ref": "3378144"}, "locked": null, "vsettings": [{"__ref": "3378397"}, {"__ref": "3378398"}, {"__ref": "3378399"}, {"__ref": "3378400"}, {"__ref": "3378401"}, {"__ref": "3378402"}, {"__ref": "3378403"}, {"__ref": "3378404"}, {"__ref": "3378405"}, {"__ref": "3378406"}, {"__ref": "3378407"}, {"__ref": "3378408"}, {"__ref": "3378409"}, {"__ref": "3378410"}, {"__ref": "3378411"}, {"__ref": "3378412"}, {"__ref": "3378413"}, {"__ref": "3378414"}, {"__ref": "3378415"}, {"__ref": "3378416"}, {"__ref": "3378417"}, {"__ref": "3378418"}, {"__ref": "3378419"}, {"__ref": "3378420"}, {"__ref": "3378421"}, {"__ref": "3378422"}], "__type": "TplSlot"}, "3378245": {"variants": [{"__ref": "3378108"}], "args": [], "attrs": {}, "rs": {"__ref": "3378423"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378246": {"variants": [{"__ref": "3378208"}], "args": [], "attrs": {}, "rs": {"__ref": "3378424"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378247": {"variants": [{"__ref": "3378207"}], "args": [], "attrs": {}, "rs": {"__ref": "3378425"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378248": {"variants": [{"__ref": "3378109"}], "args": [], "attrs": {}, "rs": {"__ref": "3378426"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378249": {"variants": [{"__ref": "3378209"}], "args": [], "attrs": {}, "rs": {"__ref": "3378427"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378250": {"param": {"__ref": "3378095"}, "defaultContents": [{"__ref": "3378428"}], "uuid": "ziujITZbRn0", "parent": {"__ref": "3378145"}, "locked": null, "vsettings": [{"__ref": "3378429"}, {"__ref": "3378430"}, {"__ref": "3378431"}, {"__ref": "3378432"}, {"__ref": "3378433"}, {"__ref": "3378434"}, {"__ref": "3378435"}, {"__ref": "3378436"}, {"__ref": "3378437"}, {"__ref": "3378438"}, {"__ref": "3378439"}, {"__ref": "3378440"}, {"__ref": "3378441"}], "__type": "TplSlot"}, "3378251": {"variants": [{"__ref": "3378108"}], "args": [], "attrs": {}, "rs": {"__ref": "3378442"}, "dataCond": {"__ref": "3378443"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378252": {"variants": [{"__ref": "3378207"}], "args": [], "attrs": {}, "rs": {"__ref": "3378444"}, "dataCond": {"__ref": "3378445"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378253": {"variants": [{"__ref": "3378216"}], "args": [], "attrs": {}, "rs": {"__ref": "3378446"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378254": {"variants": [{"__ref": "3378219"}], "args": [], "attrs": {}, "rs": {"__ref": "3378447"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378255": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "center", "justify-content": "center", "padding-top": "12px", "padding-right": "20px", "padding-bottom": "12px", "padding-left": "20px", "flex-column-gap": "8px", "background": "linear-gradient(#232320, #232320)", "border-top-width": "0px", "border-right-width": "0px", "border-bottom-width": "0px", "border-left-width": "0px", "cursor": "pointer", "transition-property": "background", "transition-duration": "0.1s", "border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px"}, "mixins": [], "__type": "RuleSet"}, "3378256": {"values": {"box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "3378257": {"values": {"box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "3378258": {"values": {"cursor": "not-allowed", "opacity": "0.6"}, "mixins": [], "__type": "RuleSet"}, "3378259": {"values": {"padding-right": "16px"}, "mixins": [], "__type": "RuleSet"}, "3378260": {"values": {"padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "3378261": {"values": {"background": "linear-gradient(#282826, #282826)"}, "mixins": [], "__type": "RuleSet"}, "3378262": {"values": {"background": "linear-gradient(#2E2E2B, #2E2E2B)"}, "mixins": [], "__type": "RuleSet"}, "3378263": {"values": {"background": "linear-gradient(#EDF6FF, #EDF6FF)"}, "mixins": [], "__type": "RuleSet"}, "3378264": {"values": {"background": "linear-gradient(#E1F0FF, #E1F0FF)"}, "mixins": [], "__type": "RuleSet"}, "3378265": {"values": {"background": "linear-gradient(#CEE7FE, #CEE7FE)"}, "mixins": [], "__type": "RuleSet"}, "3378266": {"values": {"background": "linear-gradient(#E9F9EE, #E9F9EE)"}, "mixins": [], "__type": "RuleSet"}, "3378267": {"values": {"background": "linear-gradient(#CCEBD7, #CCEBD7)"}, "mixins": [], "__type": "RuleSet"}, "3378268": {"values": {"background": "linear-gradient(#DDF3E4, #DDF3E4)"}, "mixins": [], "__type": "RuleSet"}, "3378269": {"values": {"background": "linear-gradient(#F5D90A, #F5D90A)"}, "mixins": [], "__type": "RuleSet"}, "3378270": {"values": {"background": "linear-gradient(#FFEF5C, #FFEF5C)"}, "mixins": [], "__type": "RuleSet"}, "3378271": {"values": {"background": "linear-gradient(#F0C000, #F0C000)"}, "mixins": [], "__type": "RuleSet"}, "3378272": {"values": {"background": "linear-gradient(#E54D2E, #E54D2E)"}, "mixins": [], "__type": "RuleSet"}, "3378273": {"values": {"background": "linear-gradient(#EC5E41, #EC5E41)"}, "mixins": [], "__type": "RuleSet"}, "3378274": {"values": {"background": "linear-gradient(#F16A50, #F16A50)"}, "mixins": [], "__type": "RuleSet"}, "3378275": {"values": {"background": "linear-gradient(#FFFBD1, #FFFBD1)"}, "mixins": [], "__type": "RuleSet"}, "3378276": {"values": {"background": "linear-gradient(#FEF2A4, #FEF2A4)"}, "mixins": [], "__type": "RuleSet"}, "3378277": {"values": {"background": "linear-gradient(#FFF8BB, #FFF8BB)"}, "mixins": [], "__type": "RuleSet"}, "3378278": {"values": {"background": "linear-gradient(#FFF0EE, #FFF0EE)"}, "mixins": [], "__type": "RuleSet"}, "3378279": {"values": {"background": "linear-gradient(#FDD8D3, #FDD8D3)"}, "mixins": [], "__type": "RuleSet"}, "3378280": {"values": {"background": "linear-gradient(#FFE6E2, #FFE6E2)"}, "mixins": [], "__type": "RuleSet"}, "3378281": {"values": {"background": "linear-gradient(#EEEEEC, #EEEEEC)"}, "mixins": [], "__type": "RuleSet"}, "3378282": {"values": {"background": "linear-gradient(#E9E9E6, #E9E9E6)"}, "mixins": [], "__type": "RuleSet"}, "3378283": {"values": {"background": "linear-gradient(#E3E3E0, #E3E3E0)"}, "mixins": [], "__type": "RuleSet"}, "3378284": {"values": {"background": "linear-gradient(#0091FF, #0091FF)"}, "mixins": [], "__type": "RuleSet"}, "3378285": {"values": {"background": "linear-gradient(#369EFF, #369EFF)"}, "mixins": [], "__type": "RuleSet"}, "3378286": {"values": {"background": "linear-gradient(#52A9FF, #52A9FF)"}, "mixins": [], "__type": "RuleSet"}, "3378287": {"values": {"background": "linear-gradient(#30A46C, #30A46C)"}, "mixins": [], "__type": "RuleSet"}, "3378288": {"values": {"background": "linear-gradient(#3CB179, #3CB179)"}, "mixins": [], "__type": "RuleSet"}, "3378289": {"values": {"background": "linear-gradient(#4CC38A, #4CC38A)"}, "mixins": [], "__type": "RuleSet"}, "3378290": {"values": {"background": "linear-gradient(#717069, #717069)"}, "mixins": [], "__type": "RuleSet"}, "3378291": {"values": {"background": "linear-gradient(#7F7E77, #7F7E77)"}, "mixins": [], "__type": "RuleSet"}, "3378292": {"values": {"background": "linear-gradient(#A1A09A, #A1A09A)"}, "mixins": [], "__type": "RuleSet"}, "3378293": {"values": {"padding-top": "6px", "padding-right": "16px", "padding-bottom": "6px", "padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "3378294": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378295": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378296": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378297": {"values": {"border-top-left-radius": "999px", "border-top-right-radius": "999px", "border-bottom-right-radius": "999px", "border-bottom-left-radius": "999px", "padding-left": "20px", "padding-right": "20px", "min-width": "100px"}, "mixins": [], "__type": "RuleSet"}, "3378298": {"values": {"padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "3378299": {"values": {"padding-right": "16px"}, "mixins": [], "__type": "RuleSet"}, "3378300": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378301": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "3378302": {"values": {"background": "linear-gradient(#E9E9E6, #E9E9E6)"}, "mixins": [], "__type": "RuleSet"}, "3378303": {"values": {"background": "linear-gradient(#E3E3E0, #E3E3E0)"}, "mixins": [], "__type": "RuleSet"}, "3378304": {"values": {"padding-top": "12px", "padding-right": "12px", "padding-bottom": "12px", "padding-left": "12px", "border-top-left-radius": "50%", "border-top-right-radius": "50%", "border-bottom-right-radius": "50%", "border-bottom-left-radius": "50%"}, "mixins": [], "__type": "RuleSet"}, "3378305": {"values": {"padding-top": "6px", "padding-right": "6px", "padding-bottom": "6px", "padding-left": "6px"}, "mixins": [], "__type": "RuleSet"}, "3378306": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "3378307": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "3378308": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "3378309": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378310": {"values": {"padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px"}, "mixins": [], "__type": "RuleSet"}, "3378311": {"values": {"background": "linear-gradient(#FFFFFF, #FFFFFF)"}, "mixins": [], "__type": "RuleSet"}, "3378312": {"values": {"background": "linear-gradient(#FFEF5C, #FFEF5C)"}, "mixins": [], "__type": "RuleSet"}, "3378313": {"values": {"background": "linear-gradient(#F0C000, #F0C000)"}, "mixins": [], "__type": "RuleSet"}, "3378314": {"values": {"border-top-left-radius": "0px", "border-top-right-radius": "0px", "border-bottom-right-radius": "0px", "border-bottom-left-radius": "0px"}, "mixins": [], "__type": "RuleSet"}, "3378315": {"frame": {"__ref": "3378550"}, "cellKey": {"__ref": "3378108"}, "__type": "ArenaFrameCell"}, "3378316": {"frame": {"__ref": "3378551"}, "cellKey": {"__ref": "3378109"}, "__type": "ArenaFrameCell"}, "3378317": {"frame": {"__ref": "3378552"}, "cellKey": {"__ref": "3378110"}, "__type": "ArenaFrameCell"}, "3378318": {"frame": {"__ref": "3378553"}, "cellKey": {"__ref": "3378111"}, "__type": "ArenaFrameCell"}, "3378319": {"frame": {"__ref": "3378554"}, "cellKey": {"__ref": "3378112"}, "__type": "ArenaFrameCell"}, "3378320": {"frame": {"__ref": "3378555"}, "cellKey": {"__ref": "3378206"}, "__type": "ArenaFrameCell"}, "3378321": {"frame": {"__ref": "3378556"}, "cellKey": {"__ref": "3378207"}, "__type": "ArenaFrameCell"}, "3378322": {"frame": {"__ref": "3378557"}, "cellKey": {"__ref": "3378208"}, "__type": "ArenaFrameCell"}, "3378323": {"frame": {"__ref": "3378558"}, "cellKey": {"__ref": "3378209"}, "__type": "ArenaFrameCell"}, "3378324": {"frame": {"__ref": "3378559"}, "cellKey": {"__ref": "3378210"}, "__type": "ArenaFrameCell"}, "3378325": {"frame": {"__ref": "3378560"}, "cellKey": {"__ref": "3378211"}, "__type": "ArenaFrameCell"}, "3378326": {"frame": {"__ref": "3378561"}, "cellKey": {"__ref": "3378212"}, "__type": "ArenaFrameCell"}, "3378327": {"frame": {"__ref": "3378562"}, "cellKey": {"__ref": "3378213"}, "__type": "ArenaFrameCell"}, "3378328": {"frame": {"__ref": "3378563"}, "cellKey": {"__ref": "3378214"}, "__type": "ArenaFrameCell"}, "3378329": {"frame": {"__ref": "3378564"}, "cellKey": {"__ref": "3378215"}, "__type": "ArenaFrameCell"}, "3378330": {"frame": {"__ref": "3378565"}, "cellKey": {"__ref": "3378216"}, "__type": "ArenaFrameCell"}, "3378331": {"frame": {"__ref": "3378566"}, "cellKey": {"__ref": "3378217"}, "__type": "ArenaFrameCell"}, "3378332": {"frame": {"__ref": "3378567"}, "cellKey": {"__ref": "3378218"}, "__type": "ArenaFrameCell"}, "3378333": {"frame": {"__ref": "3378568"}, "cellKey": {"__ref": "3378219"}, "__type": "ArenaFrameCell"}, "3378334": {"frame": {"__ref": "3378569"}, "cellKey": {"__ref": "3378220"}, "__type": "ArenaFrameCell"}, "3378335": {"frame": {"__ref": "3378570"}, "cellKey": {"__ref": "3378221"}, "__type": "ArenaFrameCell"}, "3378336": {"frame": {"__ref": "3378571"}, "cellKey": {"__ref": "3378222"}, "__type": "ArenaFrameCell"}, "3378337": {"frame": {"__ref": "3378572"}, "cellKey": {"__ref": "3378223"}, "__type": "ArenaFrameCell"}, "3378338": {"frame": {"__ref": "3378573"}, "cellKey": {"__ref": "3378224"}, "__type": "ArenaFrameCell"}, "3378339": {"frame": {"__ref": "3378574"}, "cellKey": {"__ref": "3378225"}, "__type": "ArenaFrameCell"}, "3378340": {"frame": {"__ref": "3378575"}, "cellKey": {"__ref": "3378226"}, "__type": "ArenaFrameCell"}, "3378341": {"frame": {"__ref": "3378576"}, "cellKey": [{"__ref": "3378220"}, {"__ref": "3378111"}], "__type": "ArenaFrameCell"}, "3378342": {"frame": {"__ref": "3378577"}, "cellKey": [{"__ref": "3378220"}, {"__ref": "3378112"}], "__type": "ArenaFrameCell"}, "3378343": {"frame": {"__ref": "3378578"}, "cellKey": [{"__ref": "3378221"}, {"__ref": "3378112"}], "__type": "ArenaFrameCell"}, "3378344": {"frame": {"__ref": "3378579"}, "cellKey": [{"__ref": "3378221"}, {"__ref": "3378111"}], "__type": "ArenaFrameCell"}, "3378345": {"frame": {"__ref": "3378580"}, "cellKey": [{"__ref": "3378216"}, {"__ref": "3378111"}], "__type": "ArenaFrameCell"}, "3378346": {"frame": {"__ref": "3378581"}, "cellKey": [{"__ref": "3378216"}, {"__ref": "3378112"}], "__type": "ArenaFrameCell"}, "3378347": {"frame": {"__ref": "3378582"}, "cellKey": [{"__ref": "3378217"}, {"__ref": "3378111"}], "__type": "ArenaFrameCell"}, "3378348": {"frame": {"__ref": "3378583"}, "cellKey": [{"__ref": "3378217"}, {"__ref": "3378112"}], "__type": "ArenaFrameCell"}, "3378349": {"frame": {"__ref": "3378584"}, "cellKey": [{"__ref": "3378222"}, {"__ref": "3378112"}], "__type": "ArenaFrameCell"}, "3378350": {"frame": {"__ref": "3378585"}, "cellKey": [{"__ref": "3378222"}, {"__ref": "3378111"}], "__type": "ArenaFrameCell"}, "3378351": {"frame": {"__ref": "3378586"}, "cellKey": [{"__ref": "3378223"}, {"__ref": "3378112"}], "__type": "ArenaFrameCell"}, "3378352": {"frame": {"__ref": "3378587"}, "cellKey": [{"__ref": "3378223"}, {"__ref": "3378111"}], "__type": "ArenaFrameCell"}, "3378353": {"frame": {"__ref": "3378588"}, "cellKey": [{"__ref": "3378224"}, {"__ref": "3378111"}], "__type": "ArenaFrameCell"}, "3378354": {"frame": {"__ref": "3378589"}, "cellKey": [{"__ref": "3378224"}, {"__ref": "3378112"}], "__type": "ArenaFrameCell"}, "3378355": {"frame": {"__ref": "3378590"}, "cellKey": [{"__ref": "3378214"}, {"__ref": "3378111"}], "__type": "ArenaFrameCell"}, "3378356": {"frame": {"__ref": "3378591"}, "cellKey": [{"__ref": "3378214"}, {"__ref": "3378112"}], "__type": "ArenaFrameCell"}, "3378357": {"frame": {"__ref": "3378592"}, "cellKey": [{"__ref": "3378215"}, {"__ref": "3378111"}], "__type": "ArenaFrameCell"}, "3378358": {"frame": {"__ref": "3378593"}, "cellKey": [{"__ref": "3378215"}, {"__ref": "3378112"}], "__type": "ArenaFrameCell"}, "3378359": {"frame": {"__ref": "3378594"}, "cellKey": [{"__ref": "3378218"}, {"__ref": "3378111"}], "__type": "ArenaFrameCell"}, "3378360": {"frame": {"__ref": "3378595"}, "cellKey": [{"__ref": "3378218"}, {"__ref": "3378112"}], "__type": "ArenaFrameCell"}, "3378361": {"frame": {"__ref": "3378596"}, "cellKey": [{"__ref": "3378209"}, {"__ref": "3378206"}], "__type": "ArenaFrameCell"}, "3378362": {"frame": {"__ref": "3378597"}, "cellKey": [{"__ref": "3378207"}, {"__ref": "3378209"}], "__type": "ArenaFrameCell"}, "3378363": {"frame": {"__ref": "3378598"}, "cellKey": [{"__ref": "3378225"}, {"__ref": "3378111"}], "__type": "ArenaFrameCell"}, "3378364": {"frame": {"__ref": "3378599"}, "cellKey": [{"__ref": "3378225"}, {"__ref": "3378112"}], "__type": "ArenaFrameCell"}, "3378365": {"frame": {"__ref": "3378600"}, "cellKey": [{"__ref": "3378210"}, {"__ref": "3378212"}], "__type": "ArenaFrameCell"}, "3378366": {"frame": {"__ref": "3378601"}, "cellKey": [{"__ref": "3378226"}, {"__ref": "3378111"}], "__type": "ArenaFrameCell"}, "3378367": {"frame": {"__ref": "3378602"}, "cellKey": [{"__ref": "3378226"}, {"__ref": "3378112"}], "__type": "ArenaFrameCell"}, "3378368": {"frame": {"__ref": "3378603"}, "cellKey": [{"__ref": "3378219"}, {"__ref": "3378111"}], "__type": "ArenaFrameCell"}, "3378369": {"frame": {"__ref": "3378604"}, "cellKey": [{"__ref": "3378219"}, {"__ref": "3378112"}], "__type": "ArenaFrameCell"}, "3378375": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "Kl0qgssGXra", "parent": {"__ref": "3378239"}, "locked": null, "vsettings": [{"__ref": "3378608"}], "__type": "TplTag"}, "3378376": {"variants": [{"__ref": "3378108"}], "args": [], "attrs": {}, "rs": {"__ref": "3378609"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378377": {"variants": [{"__ref": "3378206"}], "args": [], "attrs": {}, "rs": {"__ref": "3378610"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378378": {"variants": [{"__ref": "3378214"}], "args": [], "attrs": {}, "rs": {"__ref": "3378611"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378379": {"variants": [{"__ref": "3378220"}], "args": [], "attrs": {}, "rs": {"__ref": "3378612"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378380": {"variants": [{"__ref": "3378221"}], "args": [], "attrs": {}, "rs": {"__ref": "3378613"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378381": {"variants": [{"__ref": "3378222"}], "args": [], "attrs": {}, "rs": {"__ref": "3378614"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378382": {"variants": [{"__ref": "3378223"}], "args": [], "attrs": {}, "rs": {"__ref": "3378615"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378383": {"variants": [{"__ref": "3378224"}], "args": [], "attrs": {}, "rs": {"__ref": "3378616"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378384": {"variants": [{"__ref": "3378216"}], "args": [], "attrs": {}, "rs": {"__ref": "3378617"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378385": {"variants": [{"__ref": "3378226"}], "args": [], "attrs": {}, "rs": {"__ref": "3378618"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378386": {"variants": [{"__ref": "3378226"}, {"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378619"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378387": {"variants": [{"__ref": "3378226"}, {"__ref": "3378112"}], "args": [], "attrs": {}, "rs": {"__ref": "3378620"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378388": {"variants": [{"__ref": "3378225"}], "args": [], "attrs": {}, "rs": {"__ref": "3378621"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378389": {"variants": [{"__ref": "3378219"}], "args": [], "attrs": {}, "rs": {"__ref": "3378622"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378390": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "3378391": {"code": "false", "fallback": null, "__type": "CustomCode"}, "3378392": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "3378393": {"code": "true", "fallback": null, "__type": "CustomCode"}, "3378394": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378395": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378396": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "1oiZZjF_86R", "parent": {"__ref": "3378244"}, "locked": null, "vsettings": [{"__ref": "3378629"}], "__type": "TplTag"}, "3378397": {"variants": [{"__ref": "3378108"}], "args": [], "attrs": {}, "rs": {"__ref": "3378630"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378398": {"variants": [{"__ref": "3378110"}], "args": [], "attrs": {}, "rs": {"__ref": "3378631"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378399": {"variants": [{"__ref": "3378109"}], "args": [], "attrs": {}, "rs": {"__ref": "3378632"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378400": {"variants": [{"__ref": "3378206"}], "args": [], "attrs": {}, "rs": {"__ref": "3378633"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378401": {"variants": [{"__ref": "3378207"}], "args": [], "attrs": {}, "rs": {"__ref": "3378634"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378402": {"variants": [{"__ref": "3378208"}], "args": [], "attrs": {}, "rs": {"__ref": "3378635"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378403": {"variants": [{"__ref": "3378220"}], "args": [], "attrs": {}, "rs": {"__ref": "3378636"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378404": {"variants": [{"__ref": "3378221"}], "args": [], "attrs": {}, "rs": {"__ref": "3378637"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378405": {"variants": [{"__ref": "3378216"}], "args": [], "attrs": {}, "rs": {"__ref": "3378638"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378406": {"variants": [{"__ref": "3378222"}], "args": [], "attrs": {}, "rs": {"__ref": "3378639"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378407": {"variants": [{"__ref": "3378223"}], "args": [], "attrs": {}, "rs": {"__ref": "3378640"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378408": {"variants": [{"__ref": "3378224"}], "args": [], "attrs": {}, "rs": {"__ref": "3378641"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378409": {"variants": [{"__ref": "3378214"}], "args": [], "attrs": {}, "rs": {"__ref": "3378642"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378410": {"variants": [{"__ref": "3378215"}], "args": [], "attrs": {}, "rs": {"__ref": "3378643"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378411": {"variants": [{"__ref": "3378218"}], "args": [], "attrs": {}, "rs": {"__ref": "3378644"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378412": {"variants": [{"__ref": "3378217"}], "args": [], "attrs": {}, "rs": {"__ref": "3378645"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378413": {"variants": [{"__ref": "3378209"}], "args": [], "attrs": {}, "rs": {"__ref": "3378646"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378414": {"variants": [{"__ref": "3378225"}], "args": [], "attrs": {}, "rs": {"__ref": "3378647"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378415": {"variants": [{"__ref": "3378226"}], "args": [], "attrs": {}, "rs": {"__ref": "3378648"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378416": {"variants": [{"__ref": "3378226"}, {"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378649"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378417": {"variants": [{"__ref": "3378226"}, {"__ref": "3378112"}], "args": [], "attrs": {}, "rs": {"__ref": "3378650"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378418": {"variants": [{"__ref": "3378226"}, {"__ref": "3378213"}], "args": [], "attrs": {}, "rs": {"__ref": "3378651"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378419": {"variants": [{"__ref": "3378226"}, {"__ref": "3378213"}, {"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378652"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378420": {"variants": [{"__ref": "3378213"}], "args": [], "attrs": {}, "rs": {"__ref": "3378653"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378421": {"variants": [{"__ref": "3378213"}, {"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378654"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378422": {"variants": [{"__ref": "3378219"}], "args": [], "attrs": {}, "rs": {"__ref": "3378655"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378423": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "3378424": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378425": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378426": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378427": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378428": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "LSsbshVm5cZ", "parent": {"__ref": "3378250"}, "locked": null, "vsettings": [{"__ref": "3378661"}], "__type": "TplTag"}, "3378429": {"variants": [{"__ref": "3378108"}], "args": [], "attrs": {}, "rs": {"__ref": "3378662"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378430": {"variants": [{"__ref": "3378207"}], "args": [], "attrs": {}, "rs": {"__ref": "3378663"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378431": {"variants": [{"__ref": "3378220"}], "args": [], "attrs": {}, "rs": {"__ref": "3378664"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378432": {"variants": [{"__ref": "3378221"}], "args": [], "attrs": {}, "rs": {"__ref": "3378665"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378433": {"variants": [{"__ref": "3378222"}], "args": [], "attrs": {}, "rs": {"__ref": "3378666"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378434": {"variants": [{"__ref": "3378223"}], "args": [], "attrs": {}, "rs": {"__ref": "3378667"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378435": {"variants": [{"__ref": "3378224"}], "args": [], "attrs": {}, "rs": {"__ref": "3378668"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378436": {"variants": [{"__ref": "3378216"}], "args": [], "attrs": {}, "rs": {"__ref": "3378669"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378437": {"variants": [{"__ref": "3378226"}], "args": [], "attrs": {}, "rs": {"__ref": "3378670"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378438": {"variants": [{"__ref": "3378226"}, {"__ref": "3378111"}], "args": [], "attrs": {}, "rs": {"__ref": "3378671"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378439": {"variants": [{"__ref": "3378226"}, {"__ref": "3378112"}], "args": [], "attrs": {}, "rs": {"__ref": "3378672"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378440": {"variants": [{"__ref": "3378225"}], "args": [], "attrs": {}, "rs": {"__ref": "3378673"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378441": {"variants": [{"__ref": "3378219"}], "args": [], "attrs": {}, "rs": {"__ref": "3378674"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378442": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "3378443": {"code": "false", "fallback": null, "__type": "CustomCode"}, "3378444": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "3378445": {"code": "true", "fallback": null, "__type": "CustomCode"}, "3378446": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378447": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378550": {"uuid": "sH2drWzvcTS", "width": 340, "height": 340, "container": {"__ref": "3378681"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378108"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378551": {"uuid": "Hnt29OHeMAy", "width": 340, "height": 340, "container": {"__ref": "3378682"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378109"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378552": {"uuid": "ivfqwXCGbNe", "width": 340, "height": 340, "container": {"__ref": "3378683"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378110"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378553": {"uuid": "Es9-6Hzza_x", "width": 340, "height": 340, "container": {"__ref": "3378684"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378111"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378554": {"uuid": "UJVkTkwZug2", "width": 340, "height": 340, "container": {"__ref": "3378685"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378555": {"uuid": "Zl4ILNqp2b9", "width": 340, "height": 340, "container": {"__ref": "3378686"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378206"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378556": {"uuid": "_G40jQki7ZE", "width": 340, "height": 340, "container": {"__ref": "3378687"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378207"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378557": {"uuid": "g4JxAnAHSQk", "width": 340, "height": 340, "container": {"__ref": "3378688"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378208"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378558": {"uuid": "iRR8LteQDfr", "width": 340, "height": 340, "container": {"__ref": "3378689"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378209"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378559": {"uuid": "SdJjtgX0Rf0", "width": 340, "height": 340, "container": {"__ref": "3378690"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378210"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378560": {"uuid": "4SrbAzAH75u", "width": 340, "height": 340, "container": {"__ref": "3378691"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378211"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378561": {"uuid": "AYCH8wymAeX", "width": 340, "height": 340, "container": {"__ref": "3378692"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378212"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378562": {"uuid": "IfT059EJJ8G", "width": 340, "height": 340, "container": {"__ref": "3378693"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378213"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378563": {"uuid": "XjHFQN7K8pv", "width": 340, "height": 340, "container": {"__ref": "3378694"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378214"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378564": {"uuid": "amnil7i6AWm", "width": 340, "height": 340, "container": {"__ref": "3378695"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378215"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378565": {"uuid": "aPVPAW6ZiZL", "width": 340, "height": 340, "container": {"__ref": "3378696"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378216"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378566": {"uuid": "DDTPerZXNYk", "width": 340, "height": 340, "container": {"__ref": "3378697"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378217"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378567": {"uuid": "bEhUKZF6_a3", "width": 340, "height": 340, "container": {"__ref": "3378698"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378218"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378568": {"uuid": "J6Bg1l5SVwP", "width": 340, "height": 340, "container": {"__ref": "3378699"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378219"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378569": {"uuid": "slP7W2sE-tT", "width": 340, "height": 340, "container": {"__ref": "3378700"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378220"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378570": {"uuid": "mQuLZjyeS-M", "width": 340, "height": 340, "container": {"__ref": "3378701"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378221"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378571": {"uuid": "S463nYblz9m", "width": 340, "height": 340, "container": {"__ref": "3378702"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378222"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378572": {"uuid": "w0jrszzzsYR", "width": 340, "height": 340, "container": {"__ref": "3378703"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378223"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378573": {"uuid": "JuKzNOgPncy", "width": 340, "height": 340, "container": {"__ref": "3378704"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378224"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378574": {"uuid": "lk2w-wgXSRQ", "width": 340, "height": 340, "container": {"__ref": "3378705"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378225"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378575": {"uuid": "fEg_BAS_KQK", "width": 340, "height": 340, "container": {"__ref": "3378706"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378226"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378576": {"uuid": "_PSd_tYZ-la", "width": 340, "height": 340, "container": {"__ref": "3378707"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378220"}, {"__ref": "3378111"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378577": {"uuid": "d84oVH39eJo", "width": 340, "height": 340, "container": {"__ref": "3378708"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378220"}, {"__ref": "3378112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378578": {"uuid": "Xy10zB-2QXL", "width": 340, "height": 340, "container": {"__ref": "3378709"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378221"}, {"__ref": "3378112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378579": {"uuid": "KHCz6ZMN6C5", "width": 340, "height": 340, "container": {"__ref": "3378710"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378221"}, {"__ref": "3378111"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378580": {"uuid": "sng7Bwafhhf", "width": 340, "height": 340, "container": {"__ref": "3378711"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378216"}, {"__ref": "3378111"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378581": {"uuid": "QUYv8GqLWcj", "width": 340, "height": 340, "container": {"__ref": "3378712"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378216"}, {"__ref": "3378112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378582": {"uuid": "Fb-qaXZ3Abb", "width": 340, "height": 340, "container": {"__ref": "3378713"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378217"}, {"__ref": "3378111"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378583": {"uuid": "prRR7hFfqiQ", "width": 340, "height": 340, "container": {"__ref": "3378714"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378217"}, {"__ref": "3378112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378584": {"uuid": "7CT-ob_lt7T", "width": 340, "height": 340, "container": {"__ref": "3378715"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378222"}, {"__ref": "3378112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378585": {"uuid": "OMcqWKUa14C", "width": 340, "height": 340, "container": {"__ref": "3378716"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378222"}, {"__ref": "3378111"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378586": {"uuid": "SveVXtxgaHu", "width": 340, "height": 340, "container": {"__ref": "3378717"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378223"}, {"__ref": "3378112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378587": {"uuid": "azIzzgM1kQ2", "width": 340, "height": 340, "container": {"__ref": "3378718"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378223"}, {"__ref": "3378111"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378588": {"uuid": "9r_hX7pPXuY", "width": 340, "height": 340, "container": {"__ref": "3378719"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378224"}, {"__ref": "3378111"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378589": {"uuid": "nc4fxDG6mOR", "width": 340, "height": 340, "container": {"__ref": "3378720"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378224"}, {"__ref": "3378112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378590": {"uuid": "vzonGas2TwU", "width": 340, "height": 340, "container": {"__ref": "3378721"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378214"}, {"__ref": "3378111"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378591": {"uuid": "gzKQMjmuHO-", "width": 340, "height": 340, "container": {"__ref": "3378722"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378214"}, {"__ref": "3378112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378592": {"uuid": "z4vaV8YDMCH", "width": 340, "height": 340, "container": {"__ref": "3378723"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378215"}, {"__ref": "3378111"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378593": {"uuid": "1lHBd0NeRrY", "width": 340, "height": 340, "container": {"__ref": "3378724"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378215"}, {"__ref": "3378112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378594": {"uuid": "c-nDqXCHmfj", "width": 340, "height": 340, "container": {"__ref": "3378725"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378218"}, {"__ref": "3378111"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378595": {"uuid": "r-b4bOY_Rl4", "width": 340, "height": 340, "container": {"__ref": "3378726"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378218"}, {"__ref": "3378112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378596": {"uuid": "0aSnldiDrBi", "width": 340, "height": 340, "container": {"__ref": "3378727"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378209"}, {"__ref": "3378206"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378597": {"uuid": "tq_hCrFb0Ci", "width": 340, "height": 340, "container": {"__ref": "3378728"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378207"}, {"__ref": "3378209"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378598": {"uuid": "M068Qpaew7r", "width": 340, "height": 340, "container": {"__ref": "3378729"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378225"}, {"__ref": "3378111"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378599": {"uuid": "q-Ghc6CjxIJ", "width": 340, "height": 340, "container": {"__ref": "3378730"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378225"}, {"__ref": "3378112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378600": {"uuid": "SjkURu1jl7V", "width": 340, "height": 340, "container": {"__ref": "3378731"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378210"}, {"__ref": "3378212"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378601": {"uuid": "XzGXjU7d7h4", "width": 340, "height": 340, "container": {"__ref": "3378732"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378226"}, {"__ref": "3378111"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378602": {"uuid": "0m3RWJZJRaY", "width": 340, "height": 340, "container": {"__ref": "3378733"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378226"}, {"__ref": "3378112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378603": {"uuid": "KylE7QHA5BT", "width": 340, "height": 340, "container": {"__ref": "3378734"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378219"}, {"__ref": "3378111"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378604": {"uuid": "lcVhYgkarxs", "width": 340, "height": 340, "container": {"__ref": "3378735"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3378219"}, {"__ref": "3378112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3378606": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "wC9OfEcHyXE", "parent": {"__ref": "3378090"}, "locked": null, "vsettings": [{"__ref": "3378737"}], "__type": "TplTag"}, "3378608": {"variants": [{"__ref": "3378108"}], "args": [], "attrs": {"outerHTML": {"__ref": "3378739"}}, "rs": {"__ref": "3378740"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378609": {"values": {"color": "#EDEDEC"}, "mixins": [], "__type": "RuleSet"}, "3378610": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378611": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378612": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "3378613": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "3378614": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "3378615": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "3378616": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "3378617": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "3378618": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "3378619": {"values": {"color": "#0081F1"}, "mixins": [], "__type": "RuleSet"}, "3378620": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "3378621": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "3378622": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "3378629": {"variants": [{"__ref": "3378108"}], "args": [], "attrs": {}, "rs": {"__ref": "3378753"}, "dataCond": null, "dataRep": null, "text": {"__ref": "3378754"}, "columnsConfig": null, "__type": "VariantSetting"}, "3378630": {"values": {"color": "#EDEDEC", "font-weight": "500", "white-space": "nowrap"}, "mixins": [], "__type": "RuleSet"}, "3378631": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378632": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378633": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378634": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378635": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378636": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "3378637": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "3378638": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "3378639": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "3378640": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "3378641": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "3378642": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "3378643": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "3378644": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "3378645": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "3378646": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378647": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "3378648": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "3378649": {"values": {"color": "#0081F1", "text-decoration-line": "underline"}, "mixins": [], "__type": "RuleSet"}, "3378650": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "3378651": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378652": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378653": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378654": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378655": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "3378661": {"variants": [{"__ref": "3378108"}], "args": [], "attrs": {"outerHTML": {"__ref": "3378774"}}, "rs": {"__ref": "3378775"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378662": {"values": {"color": "#EDEDEC"}, "mixins": [], "__type": "RuleSet"}, "3378663": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378664": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "3378665": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "3378666": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "3378667": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "3378668": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "3378669": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "3378670": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "3378671": {"values": {"color": "#0081F1"}, "mixins": [], "__type": "RuleSet"}, "3378672": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "3378673": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "3378674": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "3378681": {"name": null, "component": {"__ref": "3378088"}, "uuid": "7exUJHzSfkW", "parent": null, "locked": null, "vsettings": [{"__ref": "3378788"}], "__type": "TplComponent"}, "3378682": {"name": null, "component": {"__ref": "3378088"}, "uuid": "giSJZYMsAGx", "parent": null, "locked": null, "vsettings": [{"__ref": "3378789"}], "__type": "TplComponent"}, "3378683": {"name": null, "component": {"__ref": "3378088"}, "uuid": "BzSHv_nwswZ", "parent": null, "locked": null, "vsettings": [{"__ref": "3378790"}], "__type": "TplComponent"}, "3378684": {"name": null, "component": {"__ref": "3378088"}, "uuid": "732hUjcmx0T", "parent": null, "locked": null, "vsettings": [{"__ref": "3378791"}], "__type": "TplComponent"}, "3378685": {"name": null, "component": {"__ref": "3378088"}, "uuid": "66iF9eOgVop", "parent": null, "locked": null, "vsettings": [{"__ref": "3378792"}], "__type": "TplComponent"}, "3378686": {"name": null, "component": {"__ref": "3378088"}, "uuid": "nEdoEWa7pwa", "parent": null, "locked": null, "vsettings": [{"__ref": "3378793"}], "__type": "TplComponent"}, "3378687": {"name": null, "component": {"__ref": "3378088"}, "uuid": "1dZG0GuUUeq", "parent": null, "locked": null, "vsettings": [{"__ref": "3378794"}], "__type": "TplComponent"}, "3378688": {"name": null, "component": {"__ref": "3378088"}, "uuid": "2TOGNnIgAmk", "parent": null, "locked": null, "vsettings": [{"__ref": "3378795"}], "__type": "TplComponent"}, "3378689": {"name": null, "component": {"__ref": "3378088"}, "uuid": "ELKWWUmEgwl", "parent": null, "locked": null, "vsettings": [{"__ref": "3378796"}], "__type": "TplComponent"}, "3378690": {"name": null, "component": {"__ref": "3378088"}, "uuid": "9bjDZjoZlm9", "parent": null, "locked": null, "vsettings": [{"__ref": "3378797"}], "__type": "TplComponent"}, "3378691": {"name": null, "component": {"__ref": "3378088"}, "uuid": "1JsYFWmhdje", "parent": null, "locked": null, "vsettings": [{"__ref": "3378798"}], "__type": "TplComponent"}, "3378692": {"name": null, "component": {"__ref": "3378088"}, "uuid": "cnk36VqpuCk", "parent": null, "locked": null, "vsettings": [{"__ref": "3378799"}], "__type": "TplComponent"}, "3378693": {"name": null, "component": {"__ref": "3378088"}, "uuid": "mtm2iufMokV", "parent": null, "locked": null, "vsettings": [{"__ref": "3378800"}], "__type": "TplComponent"}, "3378694": {"name": null, "component": {"__ref": "3378088"}, "uuid": "n3234Iq8UPH", "parent": null, "locked": null, "vsettings": [{"__ref": "3378801"}], "__type": "TplComponent"}, "3378695": {"name": null, "component": {"__ref": "3378088"}, "uuid": "DsCzpnCo3R0", "parent": null, "locked": null, "vsettings": [{"__ref": "3378802"}], "__type": "TplComponent"}, "3378696": {"name": null, "component": {"__ref": "3378088"}, "uuid": "DyOGfxTXAtR", "parent": null, "locked": null, "vsettings": [{"__ref": "3378803"}], "__type": "TplComponent"}, "3378697": {"name": null, "component": {"__ref": "3378088"}, "uuid": "MIgi-OFxVuA", "parent": null, "locked": null, "vsettings": [{"__ref": "3378804"}], "__type": "TplComponent"}, "3378698": {"name": null, "component": {"__ref": "3378088"}, "uuid": "ft4YnppyYin", "parent": null, "locked": null, "vsettings": [{"__ref": "3378805"}], "__type": "TplComponent"}, "3378699": {"name": null, "component": {"__ref": "3378088"}, "uuid": "7n8bgRy93Ao", "parent": null, "locked": null, "vsettings": [{"__ref": "3378806"}], "__type": "TplComponent"}, "3378700": {"name": null, "component": {"__ref": "3378088"}, "uuid": "BYQKHKhvmuV", "parent": null, "locked": null, "vsettings": [{"__ref": "3378807"}], "__type": "TplComponent"}, "3378701": {"name": null, "component": {"__ref": "3378088"}, "uuid": "tpmRzbYXBSN", "parent": null, "locked": null, "vsettings": [{"__ref": "3378808"}], "__type": "TplComponent"}, "3378702": {"name": null, "component": {"__ref": "3378088"}, "uuid": "PciPare9AAJ", "parent": null, "locked": null, "vsettings": [{"__ref": "3378809"}], "__type": "TplComponent"}, "3378703": {"name": null, "component": {"__ref": "3378088"}, "uuid": "BlDBVyEGS9G", "parent": null, "locked": null, "vsettings": [{"__ref": "3378810"}], "__type": "TplComponent"}, "3378704": {"name": null, "component": {"__ref": "3378088"}, "uuid": "tqztnv7cV_J", "parent": null, "locked": null, "vsettings": [{"__ref": "3378811"}], "__type": "TplComponent"}, "3378705": {"name": null, "component": {"__ref": "3378088"}, "uuid": "v4jmrAfL40T", "parent": null, "locked": null, "vsettings": [{"__ref": "3378812"}], "__type": "TplComponent"}, "3378706": {"name": null, "component": {"__ref": "3378088"}, "uuid": "CS3MyJfRr2R", "parent": null, "locked": null, "vsettings": [{"__ref": "3378813"}], "__type": "TplComponent"}, "3378707": {"name": null, "component": {"__ref": "3378088"}, "uuid": "H9cno6irpnA", "parent": null, "locked": null, "vsettings": [{"__ref": "3378814"}], "__type": "TplComponent"}, "3378708": {"name": null, "component": {"__ref": "3378088"}, "uuid": "SjqPvLtKKy6", "parent": null, "locked": null, "vsettings": [{"__ref": "3378815"}], "__type": "TplComponent"}, "3378709": {"name": null, "component": {"__ref": "3378088"}, "uuid": "XlqhzkNmk_9", "parent": null, "locked": null, "vsettings": [{"__ref": "3378816"}], "__type": "TplComponent"}, "3378710": {"name": null, "component": {"__ref": "3378088"}, "uuid": "gdyUpP--aCu", "parent": null, "locked": null, "vsettings": [{"__ref": "3378817"}], "__type": "TplComponent"}, "3378711": {"name": null, "component": {"__ref": "3378088"}, "uuid": "Vg-W0KzbSay", "parent": null, "locked": null, "vsettings": [{"__ref": "3378818"}], "__type": "TplComponent"}, "3378712": {"name": null, "component": {"__ref": "3378088"}, "uuid": "V6P7xiIZ-UE", "parent": null, "locked": null, "vsettings": [{"__ref": "3378819"}], "__type": "TplComponent"}, "3378713": {"name": null, "component": {"__ref": "3378088"}, "uuid": "NfRywBZ_jP-", "parent": null, "locked": null, "vsettings": [{"__ref": "3378820"}], "__type": "TplComponent"}, "3378714": {"name": null, "component": {"__ref": "3378088"}, "uuid": "Ti0-z6hdxeo", "parent": null, "locked": null, "vsettings": [{"__ref": "3378821"}], "__type": "TplComponent"}, "3378715": {"name": null, "component": {"__ref": "3378088"}, "uuid": "-ay6tgnS6nQ", "parent": null, "locked": null, "vsettings": [{"__ref": "3378822"}], "__type": "TplComponent"}, "3378716": {"name": null, "component": {"__ref": "3378088"}, "uuid": "p5RdVIBdevp", "parent": null, "locked": null, "vsettings": [{"__ref": "3378823"}], "__type": "TplComponent"}, "3378717": {"name": null, "component": {"__ref": "3378088"}, "uuid": "cdOKAq3vBmI", "parent": null, "locked": null, "vsettings": [{"__ref": "3378824"}], "__type": "TplComponent"}, "3378718": {"name": null, "component": {"__ref": "3378088"}, "uuid": "ow05mSud1Et", "parent": null, "locked": null, "vsettings": [{"__ref": "3378825"}], "__type": "TplComponent"}, "3378719": {"name": null, "component": {"__ref": "3378088"}, "uuid": "oKveV_qDDm1", "parent": null, "locked": null, "vsettings": [{"__ref": "3378826"}], "__type": "TplComponent"}, "3378720": {"name": null, "component": {"__ref": "3378088"}, "uuid": "TSsK8TEIHiZ", "parent": null, "locked": null, "vsettings": [{"__ref": "3378827"}], "__type": "TplComponent"}, "3378721": {"name": null, "component": {"__ref": "3378088"}, "uuid": "4Lu6JTmGQso", "parent": null, "locked": null, "vsettings": [{"__ref": "3378828"}], "__type": "TplComponent"}, "3378722": {"name": null, "component": {"__ref": "3378088"}, "uuid": "gwwKkRgMZKm", "parent": null, "locked": null, "vsettings": [{"__ref": "3378829"}], "__type": "TplComponent"}, "3378723": {"name": null, "component": {"__ref": "3378088"}, "uuid": "wSqo7saKi4Q", "parent": null, "locked": null, "vsettings": [{"__ref": "3378830"}], "__type": "TplComponent"}, "3378724": {"name": null, "component": {"__ref": "3378088"}, "uuid": "hWtAf48k9vi", "parent": null, "locked": null, "vsettings": [{"__ref": "3378831"}], "__type": "TplComponent"}, "3378725": {"name": null, "component": {"__ref": "3378088"}, "uuid": "yCb6ehLVSw2", "parent": null, "locked": null, "vsettings": [{"__ref": "3378832"}], "__type": "TplComponent"}, "3378726": {"name": null, "component": {"__ref": "3378088"}, "uuid": "cr4wR-AKetr", "parent": null, "locked": null, "vsettings": [{"__ref": "3378833"}], "__type": "TplComponent"}, "3378727": {"name": null, "component": {"__ref": "3378088"}, "uuid": "hwVPN-53aWz", "parent": null, "locked": null, "vsettings": [{"__ref": "3378834"}], "__type": "TplComponent"}, "3378728": {"name": null, "component": {"__ref": "3378088"}, "uuid": "2AXPejiuz0J", "parent": null, "locked": null, "vsettings": [{"__ref": "3378835"}], "__type": "TplComponent"}, "3378729": {"name": null, "component": {"__ref": "3378088"}, "uuid": "Ipgr490_fBt", "parent": null, "locked": null, "vsettings": [{"__ref": "3378836"}], "__type": "TplComponent"}, "3378730": {"name": null, "component": {"__ref": "3378088"}, "uuid": "hb5fdALiyUx", "parent": null, "locked": null, "vsettings": [{"__ref": "3378837"}], "__type": "TplComponent"}, "3378731": {"name": null, "component": {"__ref": "3378088"}, "uuid": "9TZI6ojm99l", "parent": null, "locked": null, "vsettings": [{"__ref": "3378838"}], "__type": "TplComponent"}, "3378732": {"name": null, "component": {"__ref": "3378088"}, "uuid": "41r1IdEVtlk", "parent": null, "locked": null, "vsettings": [{"__ref": "3378839"}], "__type": "TplComponent"}, "3378733": {"name": null, "component": {"__ref": "3378088"}, "uuid": "LqxgKFktM61", "parent": null, "locked": null, "vsettings": [{"__ref": "3378840"}], "__type": "TplComponent"}, "3378734": {"name": null, "component": {"__ref": "3378088"}, "uuid": "Tw3mCG-Q7wH", "parent": null, "locked": null, "vsettings": [{"__ref": "3378841"}], "__type": "TplComponent"}, "3378735": {"name": null, "component": {"__ref": "3378088"}, "uuid": "l_2NthYy5yy", "parent": null, "locked": null, "vsettings": [{"__ref": "3378842"}], "__type": "TplComponent"}, "3378737": {"variants": [{"__ref": "3378055"}], "args": [], "attrs": {}, "rs": {"__ref": "3378845"}, "dataCond": null, "dataRep": null, "text": {"__ref": "3378954"}, "columnsConfig": null, "__type": "VariantSetting"}, "3378739": {"asset": {"__ref": "36693133"}, "__type": "ImageAssetRef"}, "3378740": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "3378753": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378754": {"markers": [], "text": "<PERSON><PERSON>", "__type": "RawText"}, "3378774": {"asset": {"__ref": "3378087"}, "__type": "ImageAssetRef"}, "3378775": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "3378788": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378857"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378789": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378858"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378790": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378859"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378791": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378860"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378792": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378861"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378793": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378862"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378794": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378863"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378795": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378864"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378796": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378865"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378797": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378866"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378798": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378867"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378799": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378868"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378800": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378869"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378801": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378870"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378802": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378871"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378803": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378872"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378804": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378873"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378805": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378874"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378806": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378875"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378807": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378876"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378808": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378877"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378809": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378878"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378810": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378879"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378811": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378880"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378812": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378881"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378813": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378882"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378814": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378883"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378815": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378884"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378816": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378885"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378817": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378886"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378818": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378887"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378819": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378888"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378820": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378889"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378821": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378890"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378822": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378891"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378823": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378892"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378824": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378893"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378825": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378894"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378826": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378895"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378827": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378896"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378828": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378897"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378829": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378898"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378830": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378899"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378831": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378900"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378832": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378901"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378833": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378902"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378834": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378903"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378835": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378904"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378836": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378905"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378837": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378906"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378838": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378907"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378839": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378908"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378840": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378909"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378841": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378910"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378842": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "3378911"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378845": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378857": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378858": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378859": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378860": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378861": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378862": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378863": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378864": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378865": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378866": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378867": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378868": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378869": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378870": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378871": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378872": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378873": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378874": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378875": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378876": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378877": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378878": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378879": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378880": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378881": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378882": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378883": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378884": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378885": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378886": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378887": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378888": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378889": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378890": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378891": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378892": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378893": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378894": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378895": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378896": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378897": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378898": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378899": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378900": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378901": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378902": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378903": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378904": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378905": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378906": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378907": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378908": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378909": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378910": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378911": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3378921": {"name": "TextInput", "component": {"__ref": "36693134"}, "uuid": "rTGuAzQF5", "parent": {"__ref": "3379130"}, "locked": null, "vsettings": [{"__ref": "3378924"}], "__type": "TplComponent"}, "3378922": {"param": {"__ref": "3378923"}, "accessType": "private", "variableType": "text", "onChangeParam": {"__ref": "64439134"}, "tplNode": {"__ref": "3378921"}, "implicitState": {"__ref": "36693156"}, "__type": "State"}, "3378923": {"type": {"__ref": "3378926"}, "state": {"__ref": "3378922"}, "variable": {"__ref": "3378925"}, "uuid": "ZI4aJ41Js2", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "3378924": {"variants": [{"__ref": "3378055"}], "args": [{"__ref": "3378927"}, {"__ref": "3378928"}, {"__ref": "3378929"}, {"__ref": "3379454"}, {"__ref": "6345020"}], "attrs": {}, "rs": {"__ref": "3378930"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378925": {"name": "undefined value", "uuid": "EtJUMflwty", "__type": "Var"}, "3378926": {"name": "text", "__type": "Text"}, "3378927": {"param": {"__ref": "36693146"}, "expr": {"__ref": "3379406"}, "__type": "Arg"}, "3378928": {"param": {"__ref": "36693141"}, "expr": {"__ref": "3379131"}, "__type": "Arg"}, "3378929": {"param": {"__ref": "36693140"}, "expr": {"__ref": "3379132"}, "__type": "Arg"}, "3378930": {"values": {"max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "3378952": {"param": {"__ref": "3378098"}, "expr": {"__ref": "3378953"}, "__type": "Arg"}, "3378953": {"variants": [{"__ref": "3378217"}], "__type": "VariantsRef"}, "3378954": {"markers": [], "text": "X", "__type": "RawText"}, "3378955": {"tpl": [{"__ref": "3378606"}], "__type": "RenderExpr"}, "3378961": {"tag": "div", "name": null, "children": [{"__ref": "3379007"}, {"__ref": "3378054"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "aEPQyC3sN", "parent": null, "locked": null, "vsettings": [{"__ref": "3378966"}], "__type": "TplTag"}, "3378966": {"variants": [{"__ref": "3378055"}], "args": [], "attrs": {}, "rs": {"__ref": "3378971"}, "dataCond": {"__ref": "3378972"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3378971": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "plasmic-display-none": "false", "flex-column-gap": "8px"}, "mixins": [], "__type": "RuleSet"}, "3378972": {"code": "true", "fallback": null, "__type": "CustomCode"}, "3379007": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "8JyidV-f_", "parent": {"__ref": "3378961"}, "locked": null, "vsettings": [{"__ref": "3379008"}], "__type": "TplTag"}, "3379008": {"variants": [{"__ref": "3378055"}], "args": [], "attrs": {}, "rs": {"__ref": "3379009"}, "dataCond": null, "dataRep": null, "text": {"__ref": "3379014"}, "columnsConfig": null, "__type": "VariantSetting"}, "3379009": {"values": {"position": "relative", "min-width": "100px"}, "mixins": [], "__type": "RuleSet"}, "3379014": {"markers": [], "text": "Nicknames", "__type": "RawText"}, "3379015": {"name": null, "component": {"__ref": "3378088"}, "uuid": "AfFYRgElV", "parent": {"__ref": "3378054"}, "locked": null, "vsettings": [{"__ref": "3379016"}], "__type": "TplComponent"}, "3379016": {"variants": [{"__ref": "3378055"}], "args": [{"__ref": "3379017"}, {"__ref": "3379018"}, {"__ref": "3379019"}, {"__ref": "3379046"}, {"__ref": "60137008"}, {"__ref": "2001406"}], "attrs": {"onClick": {"__ref": "3379408"}}, "rs": {"__ref": "3379020"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3379017": {"param": {"__ref": "3378094"}, "expr": {"__ref": "3379021"}, "__type": "Arg"}, "3379018": {"param": {"__ref": "3378091"}, "expr": {"__ref": "3379049"}, "__type": "Arg"}, "3379019": {"param": {"__ref": "3378095"}, "expr": {"__ref": "3379023"}, "__type": "Arg"}, "3379020": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "3379021": {"tpl": [{"__ref": "3379026"}], "__type": "VirtualRenderExpr"}, "3379023": {"tpl": [{"__ref": "3379028"}], "__type": "VirtualRenderExpr"}, "3379026": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "NrPJL5LvkD", "parent": {"__ref": "3379015"}, "locked": null, "vsettings": [{"__ref": "3379029"}], "__type": "TplTag"}, "3379027": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "CjjcHDqRJj", "parent": {"__ref": "3379015"}, "locked": null, "vsettings": [{"__ref": "3379030"}], "__type": "TplTag"}, "3379028": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "-P8_VQIdYf", "parent": {"__ref": "3379015"}, "locked": null, "vsettings": [{"__ref": "3379031"}], "__type": "TplTag"}, "3379029": {"variants": [{"__ref": "3378055"}], "args": [], "attrs": {"outerHTML": {"__ref": "3379032"}}, "rs": {"__ref": "3379033"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3379030": {"variants": [{"__ref": "3378055"}], "args": [], "attrs": {}, "rs": {"__ref": "3379034"}, "dataCond": null, "dataRep": null, "text": {"__ref": "3379048"}, "columnsConfig": null, "__type": "VariantSetting"}, "3379031": {"variants": [{"__ref": "3378055"}], "args": [], "attrs": {"outerHTML": {"__ref": "3379036"}}, "rs": {"__ref": "3379037"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3379032": {"asset": {"__ref": "36693133"}, "__type": "ImageAssetRef"}, "3379033": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "3379034": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3379036": {"asset": {"__ref": "3378087"}, "__type": "ImageAssetRef"}, "3379037": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "3379046": {"param": {"__ref": "3378098"}, "expr": {"__ref": "3379047"}, "__type": "Arg"}, "3379047": {"variants": [{"__ref": "3378214"}], "__type": "VariantsRef"}, "3379048": {"markers": [], "text": "Add", "__type": "RawText"}, "3379049": {"tpl": [{"__ref": "3379027"}], "__type": "RenderExpr"}, "3379050": {"name": "Nicknames", "component": {"__ref": "3378052"}, "uuid": "v6Ci8b39I", "parent": {"__ref": "36693075"}, "locked": null, "vsettings": [{"__ref": "3379051"}], "__type": "TplComponent"}, "3379051": {"variants": [{"__ref": "36693076"}], "args": [{"__ref": "28105002"}, {"__ref": "6345105"}, {"__ref": "60137015"}], "attrs": {}, "rs": {"__ref": "3379052"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3379052": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "3379067": {"name": null, "component": {"__ref": "36693073"}, "uuid": "Vi0pDPCI3", "parent": {"__ref": "3379334"}, "locked": null, "vsettings": [{"__ref": "3379068"}], "__type": "TplComponent"}, "3379068": {"variants": [{"__ref": "36693052"}], "args": [{"__ref": "46885014"}, {"__ref": "46885019"}, {"__ref": "33186012"}], "attrs": {}, "rs": {"__ref": "3379069"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3379069": {"values": {"max-width": "100%", "width": "stretch"}, "mixins": [], "__type": "RuleSet"}, "3379071": {"element": {"__ref": "3379072"}, "index": {"__ref": "3379073"}, "collection": {"__ref": "3379088"}, "__type": "Rep"}, "3379072": {"name": "currentItem", "uuid": "8sNo36ORm", "__type": "Var"}, "3379073": {"name": "currentIndex", "uuid": "6MfhNCq42d", "__type": "Var"}, "3379081": {"param": {"__ref": "3379082"}, "accessType": "private", "variableType": "array", "onChangeParam": {"__ref": "64439139"}, "tplNode": null, "implicitState": null, "__type": "State"}, "3379082": {"type": {"__ref": "3379085"}, "state": {"__ref": "3379081"}, "variable": {"__ref": "3379083"}, "uuid": "XvsP3e4xrc", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": {"__ref": "60137001"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "3379083": {"name": "people", "uuid": "0wJQxuT9I", "__type": "Var"}, "3379085": {"name": "any", "__type": "AnyType"}, "3379088": {"path": ["$state", "people"], "fallback": {"__ref": "3379089"}, "__type": "ObjectPath"}, "3379089": {"code": "([])", "fallback": null, "__type": "CustomCode"}, "3379090": {"tag": "div", "name": null, "children": [{"__ref": "3379334"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "gvZBHDr5I", "parent": {"__ref": "36693051"}, "locked": null, "vsettings": [{"__ref": "3379091"}], "__type": "TplTag"}, "3379091": {"variants": [{"__ref": "36693052"}], "args": [], "attrs": {}, "rs": {"__ref": "3379092"}, "dataCond": {"__ref": "3379093"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3379092": {"values": {"display": "flex", "flex-direction": "column", "align-items": "center", "justify-content": "center", "plasmic-display-none": "false", "width": "1000px", "flex-row-gap": "8px"}, "mixins": [], "__type": "RuleSet"}, "3379093": {"code": "true", "fallback": null, "__type": "CustomCode"}, "3379120": {"tag": "h2", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "g6Y5icJSU", "parent": {"__ref": "36693051"}, "locked": null, "vsettings": [{"__ref": "3379121"}], "__type": "TplTag"}, "3379121": {"variants": [{"__ref": "36693052"}], "args": [], "attrs": {}, "rs": {"__ref": "3379122"}, "dataCond": null, "dataRep": null, "text": {"__ref": "3379125"}, "columnsConfig": null, "__type": "VariantSetting"}, "3379122": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "3379125": {"markers": [], "text": "People List", "__type": "RawText"}, "3379130": {"tag": "div", "name": null, "children": [{"__ref": "3378921"}, {"__ref": "3378090"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "8lpMmNDgA", "parent": {"__ref": "3378054"}, "locked": null, "vsettings": [{"__ref": "3379135"}], "__type": "TplTag"}, "3379131": {"tpl": [{"__ref": "3379136"}], "__type": "VirtualRenderExpr"}, "3379132": {"tpl": [{"__ref": "3379137"}], "__type": "VirtualRenderExpr"}, "3379133": {"tpl": [{"__ref": "3379138"}], "__type": "VirtualRenderExpr"}, "3379134": {"tpl": [{"__ref": "3379139"}], "__type": "VirtualRenderExpr"}, "3379135": {"variants": [{"__ref": "3378055"}], "args": [], "attrs": {}, "rs": {"__ref": "3379140"}, "dataCond": {"__ref": "3379141"}, "dataRep": {"__ref": "3379398"}, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3379136": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "mBFqVF2aij", "parent": {"__ref": "3378921"}, "locked": null, "vsettings": [{"__ref": "3379142"}], "__type": "TplTag"}, "3379137": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "wAI7-WLZu-", "parent": {"__ref": "3378921"}, "locked": null, "vsettings": [{"__ref": "3379143"}], "__type": "TplTag"}, "3379138": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "nI4XSKorFE", "parent": {"__ref": "3378090"}, "locked": null, "vsettings": [{"__ref": "3379144"}], "__type": "TplTag"}, "3379139": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "UgU5pTQu53", "parent": {"__ref": "3378090"}, "locked": null, "vsettings": [{"__ref": "3379145"}], "__type": "TplTag"}, "3379140": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "plasmic-display-none": "false", "flex-column-gap": "8px"}, "mixins": [], "__type": "RuleSet"}, "3379141": {"code": "true", "fallback": null, "__type": "CustomCode"}, "3379142": {"variants": [{"__ref": "3378055"}], "args": [], "attrs": {"outerHTML": {"__ref": "3379152"}}, "rs": {"__ref": "3379153"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3379143": {"variants": [{"__ref": "3378055"}], "args": [], "attrs": {"outerHTML": {"__ref": "3379154"}}, "rs": {"__ref": "3379155"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3379144": {"variants": [{"__ref": "3378055"}], "args": [], "attrs": {"outerHTML": {"__ref": "3379156"}}, "rs": {"__ref": "3379157"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3379145": {"variants": [{"__ref": "3378055"}], "args": [], "attrs": {"outerHTML": {"__ref": "3379158"}}, "rs": {"__ref": "3379159"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3379152": {"asset": {"__ref": "36693132"}, "__type": "ImageAssetRef"}, "3379153": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "3379154": {"asset": {"__ref": "36693133"}, "__type": "ImageAssetRef"}, "3379155": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "3379156": {"asset": {"__ref": "36693133"}, "__type": "ImageAssetRef"}, "3379157": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "3379158": {"asset": {"__ref": "3378087"}, "__type": "ImageAssetRef"}, "3379159": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "3379253": {"name": null, "component": {"__ref": "3378088"}, "uuid": "YUNfuLJ64", "parent": {"__ref": "3379289"}, "locked": null, "vsettings": [{"__ref": "3379254"}], "__type": "TplComponent"}, "3379254": {"variants": [{"__ref": "36693076"}], "args": [{"__ref": "3379255"}, {"__ref": "3379256"}, {"__ref": "3379257"}, {"__ref": "3379284"}, {"__ref": "60137012"}, {"__ref": "2001408"}], "attrs": {"onClick": {"__ref": "33186004"}}, "rs": {"__ref": "3379258"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3379255": {"param": {"__ref": "3378094"}, "expr": {"__ref": "3379290"}, "__type": "Arg"}, "3379256": {"param": {"__ref": "3378091"}, "expr": {"__ref": "3379287"}, "__type": "Arg"}, "3379257": {"param": {"__ref": "3378095"}, "expr": {"__ref": "3379291"}, "__type": "Arg"}, "3379258": {"values": {"max-width": "200px"}, "mixins": [], "__type": "RuleSet"}, "3379265": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "IyF7-SE2a3", "parent": {"__ref": "3379253"}, "locked": null, "vsettings": [{"__ref": "3379268"}], "__type": "TplTag"}, "3379268": {"variants": [{"__ref": "36693076"}], "args": [], "attrs": {}, "rs": {"__ref": "3379272"}, "dataCond": null, "dataRep": null, "text": {"__ref": "3379286"}, "columnsConfig": null, "__type": "VariantSetting"}, "3379272": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3379284": {"param": {"__ref": "3378098"}, "expr": {"__ref": "3379288"}, "__type": "Arg"}, "3379286": {"markers": [], "text": "Remove", "__type": "RawText"}, "3379287": {"tpl": [{"__ref": "3379265"}], "__type": "RenderExpr"}, "3379288": {"variants": [{"__ref": "3378217"}], "__type": "VariantsRef"}, "3379289": {"tag": "div", "name": null, "children": [{"__ref": "3379253"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "KXKpZ0u47", "parent": {"__ref": "36693075"}, "locked": null, "vsettings": [{"__ref": "3379292"}], "__type": "TplTag"}, "3379290": {"tpl": [{"__ref": "3379293"}], "__type": "VirtualRenderExpr"}, "3379291": {"tpl": [{"__ref": "3379294"}], "__type": "VirtualRenderExpr"}, "3379292": {"variants": [{"__ref": "36693076"}], "args": [], "attrs": {}, "rs": {"__ref": "3379295"}, "dataCond": {"__ref": "3379296"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3379293": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "TGJh5EYhUS", "parent": {"__ref": "3379253"}, "locked": null, "vsettings": [{"__ref": "3379297"}], "__type": "TplTag"}, "3379294": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "mOhSKYxUFw", "parent": {"__ref": "3379253"}, "locked": null, "vsettings": [{"__ref": "3379298"}], "__type": "TplTag"}, "3379295": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "center", "justify-content": "center", "plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "3379296": {"code": "true", "fallback": null, "__type": "CustomCode"}, "3379297": {"variants": [{"__ref": "36693076"}], "args": [], "attrs": {"outerHTML": {"__ref": "3379305"}}, "rs": {"__ref": "3379306"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3379298": {"variants": [{"__ref": "36693076"}], "args": [], "attrs": {"outerHTML": {"__ref": "3379307"}}, "rs": {"__ref": "3379308"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3379305": {"asset": {"__ref": "36693133"}, "__type": "ImageAssetRef"}, "3379306": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "3379307": {"asset": {"__ref": "3378087"}, "__type": "ImageAssetRef"}, "3379308": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "3379334": {"tag": "div", "name": null, "children": [{"__ref": "3379067"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "wZU4lA_ys", "parent": {"__ref": "3379090"}, "locked": null, "vsettings": [{"__ref": "3379335"}], "__type": "TplTag"}, "3379335": {"variants": [{"__ref": "36693052"}], "args": [], "attrs": {}, "rs": {"__ref": "3379336"}, "dataCond": {"__ref": "3379337"}, "dataRep": {"__ref": "3379071"}, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3379336": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "plasmic-display-none": "false", "width": "stretch", "background": "linear-gradient(#DAD9D9, #DAD9D9)", "padding-top": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-left": "8px"}, "mixins": [], "__type": "RuleSet"}, "3379337": {"code": "true", "fallback": null, "__type": "CustomCode"}, "3379354": {"param": {"__ref": "3379355"}, "accessType": "private", "variableType": "object", "onChangeParam": {"__ref": "64439144"}, "tplNode": null, "implicitState": null, "__type": "State"}, "3379355": {"type": {"__ref": "3379358"}, "state": {"__ref": "3379354"}, "variable": {"__ref": "3379356"}, "uuid": "VP9dbi7fgo", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": {"__ref": "17043019"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "3379356": {"name": "person", "uuid": "Hwi1WkQn-", "__type": "Var"}, "3379358": {"name": "any", "__type": "AnyType"}, "3379369": {"path": ["$state", "person", "lastName"], "fallback": null, "__type": "ObjectPath"}, "3379381": {"param": {"__ref": "16883003"}, "expr": {"__ref": "3379382"}, "__type": "Arg"}, "3379382": {"interactions": [{"__ref": "3379383"}, {"__ref": "17043004"}], "__type": "EventHandler"}, "3379383": {"interactionName": "Set person ▸ lastName", "actionName": "updateVariable", "args": [{"__ref": "3379532"}, {"__ref": "3379533"}, {"__ref": "3379534"}], "condExpr": null, "conditionalMode": "always", "uuid": "kCVyD8VCe", "parent": {"__ref": "3379382"}, "__type": "Interaction"}, "3379387": {"code": "0", "fallback": null, "__type": "CustomCode"}, "3379392": {"param": {"__ref": "3379393"}, "accessType": "private", "variableType": "array", "onChangeParam": {"__ref": "64439149"}, "tplNode": null, "implicitState": null, "__type": "State"}, "3379393": {"type": {"__ref": "3379396"}, "state": {"__ref": "3379392"}, "variable": {"__ref": "3379394"}, "uuid": "8q5u_9nkRI", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": {"__ref": "6345103"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "3379394": {"name": "nicknames", "uuid": "B78abKOco", "__type": "Var"}, "3379396": {"name": "any", "__type": "AnyType"}, "3379398": {"element": {"__ref": "3379399"}, "index": {"__ref": "3379400"}, "collection": {"__ref": "3379402"}, "__type": "Rep"}, "3379399": {"name": "currentItem", "uuid": "-95<PERSON><PERSON><PERSON><PERSON>", "__type": "Var"}, "3379400": {"name": "currentIndex", "uuid": "nxLw0irQ6b", "__type": "Var"}, "3379402": {"path": ["$state", "nicknames"], "fallback": {"__ref": "3379403"}, "__type": "ObjectPath"}, "3379403": {"code": "([])", "fallback": null, "__type": "CustomCode"}, "3379406": {"path": ["currentItem"], "fallback": {"__ref": "3379407"}, "__type": "ObjectPath"}, "3379407": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "3379408": {"interactions": [{"__ref": "3379409"}, {"__ref": "6345048"}], "__type": "EventHandler"}, "3379409": {"interactionName": "Set nicknames", "actionName": "updateVariable", "args": [{"__ref": "3379420"}, {"__ref": "3379421"}, {"__ref": "3379422"}], "condExpr": null, "conditionalMode": "always", "uuid": "cUVJaG7R3", "parent": {"__ref": "3379408"}, "__type": "Interaction"}, "3379416": {"path": ["$state", "nicknames"], "fallback": null, "__type": "ObjectPath"}, "3379419": {"code": "5", "fallback": null, "__type": "CustomCode"}, "3379420": {"name": "variable", "expr": {"__ref": "3379416"}, "__type": "NameArg"}, "3379421": {"name": "operation", "expr": {"__ref": "3379419"}, "__type": "NameArg"}, "3379422": {"name": "value", "expr": {"__ref": "3379423"}, "__type": "NameArg"}, "3379423": {"code": "(\"\")", "fallback": null, "__type": "CustomCode"}, "3379424": {"interactions": [{"__ref": "3379425"}, {"__ref": "6345084"}], "__type": "EventHandler"}, "3379425": {"interactionName": "Set nicknames", "actionName": "updateVariable", "args": [{"__ref": "6345080"}, {"__ref": "6345081"}, {"__ref": "6345082"}, {"__ref": "6345083"}], "condExpr": null, "conditionalMode": "always", "uuid": "6jyk7JUML", "parent": {"__ref": "3379424"}, "__type": "Interaction"}, "3379454": {"param": {"__ref": "36693151"}, "expr": {"__ref": "3379455"}, "__type": "Arg"}, "3379455": {"interactions": [{"__ref": "3379456"}, {"__ref": "6345036"}], "__type": "EventHandler"}, "3379456": {"interactionName": "Custom function", "actionName": "customFunction", "args": [{"__ref": "6345023"}], "condExpr": null, "conditionalMode": "always", "uuid": "D_RWxk2_X", "parent": {"__ref": "3379455"}, "__type": "Interaction"}, "3379461": {"uuid": "d7-ZXiSyx9X", "name": "componentData", "op": null, "__type": "ComponentDataQuery"}, "3379531": {"path": ["$state", "person", "lastName"], "fallback": null, "__type": "ObjectPath"}, "3379532": {"name": "variable", "expr": {"__ref": "3379531"}, "__type": "NameArg"}, "3379533": {"name": "operation", "expr": {"__ref": "3379387"}, "__type": "NameArg"}, "3379534": {"name": "value", "expr": {"__ref": "3379535"}, "__type": "NameArg"}, "3379535": {"path": ["val"], "fallback": null, "__type": "ObjectPath"}, "4022701": {"code": "($state.nicknames[currentIndex] = event.target.value)", "fallback": null, "__type": "CustomCode"}, "4022702": {"code": "($state.people[currentIndex] = person)", "fallback": null, "__type": "CustomCode"}, "4022703": {"code": "($state.people.splice(currentIndex, 1))", "fallback": null, "__type": "CustomCode"}, "6345001": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "6345002": {"name": "text", "__type": "Text"}, "6345003": {"name": "func", "params": [{"__ref": "6345004"}], "__type": "FunctionType"}, "6345004": {"name": "arg", "argName": "event", "displayName": null, "type": {"__ref": "6345005"}, "__type": "ArgType"}, "6345005": {"name": "any", "__type": "AnyType"}, "6345006": {"text": ["root"], "__type": "TemplatedString"}, "6345007": {"type": {"__ref": "6345010"}, "variable": {"__ref": "6345009"}, "uuid": "3HA-nmGnKk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "6345008": {"variable": {"__ref": "6345009"}, "__type": "VarRef"}, "6345009": {"name": "data-testid", "uuid": "ahiG0ukI7", "__type": "Var"}, "6345010": {"name": "text", "__type": "Text"}, "6345011": {"type": {"__ref": "6345014"}, "variable": {"__ref": "6345013"}, "uuid": "2mPOJ1PTjb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "6345012": {"param": {"__ref": "6345007"}, "expr": {"__ref": "6345015"}, "__type": "Arg"}, "6345013": {"name": "data-testid", "uuid": "gnQBzcEDa", "__type": "Var"}, "6345014": {"name": "text", "__type": "Text"}, "6345015": {"variable": {"__ref": "6345013"}, "__type": "VarRef"}, "6345016": {"param": {"__ref": "6345011"}, "expr": {"__ref": "6345017"}, "__type": "Arg"}, "6345017": {"text": ["firstName-input"], "__type": "TemplatedString"}, "6345018": {"param": {"__ref": "6345011"}, "expr": {"__ref": "6345019"}, "__type": "Arg"}, "6345019": {"text": ["lastName-input"], "__type": "TemplatedString"}, "6345020": {"param": {"__ref": "6345007"}, "expr": {"__ref": "6345021"}, "__type": "Arg"}, "6345021": {"text": ["nickname-input"], "__type": "TemplatedString"}, "6345022": {"text": ["person-container"], "__type": "TemplatedString"}, "6345023": {"name": "customFunction", "expr": {"__ref": "6345024"}, "__type": "NameArg"}, "6345024": {"argNames": [], "bodyExpr": {"__ref": "4022701"}, "__type": "FunctionExpr"}, "6345026": {"expr": {"__ref": "6345027"}, "html": false, "__type": "ExprText"}, "6345027": {"code": "(JSON.stringify($state.people))", "fallback": {"__ref": "6345028"}, "__type": "CustomCode"}, "6345028": {"code": "\"Enter some text\"", "fallback": null, "__type": "CustomCode"}, "6345029": {"text": ["stringified-state"], "__type": "TemplatedString"}, "6345031": {"type": {"__ref": "6345033"}, "variable": {"__ref": "6345032"}, "uuid": "0Fe4eAUkI1", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "6345032": {"name": "onNicknamesChange", "uuid": "ksM69oZg7", "__type": "Var"}, "6345033": {"name": "func", "params": [{"__ref": "6345034"}], "__type": "FunctionType"}, "6345034": {"name": "arg", "argName": "nicknames", "displayName": null, "type": {"__ref": "6345035"}, "__type": "ArgType"}, "6345035": {"name": "any", "__type": "AnyType"}, "6345036": {"interactionName": "Invoke onNicknamesChange", "actionName": "invokeEventHandler", "args": [{"__ref": "6345043"}, {"__ref": "6345044"}], "condExpr": null, "conditionalMode": "always", "uuid": "ldoGkNSMP", "parent": {"__ref": "3379455"}, "__type": "Interaction"}, "6345042": {"variable": {"__ref": "6345032"}, "__type": "VarRef"}, "6345043": {"name": "eventRef", "expr": {"__ref": "6345042"}, "__type": "NameArg"}, "6345044": {"name": "args", "expr": {"__ref": "6345045"}, "__type": "NameArg"}, "6345045": {"exprs": [{"__ref": "6345046"}], "__type": "CollectionExpr"}, "6345046": {"uuid": "WCKppQI_8", "argType": {"__ref": "6345034"}, "expr": {"__ref": "6345047"}, "__type": "FunctionArg"}, "6345047": {"path": ["$state", "nicknames"], "fallback": null, "__type": "ObjectPath"}, "6345048": {"interactionName": "Invoke onNicknamesChange", "actionName": "invokeEventHandler", "args": [{"__ref": "6345055"}, {"__ref": "6345056"}], "condExpr": null, "conditionalMode": "always", "uuid": "HriANTz6a", "parent": {"__ref": "3379408"}, "__type": "Interaction"}, "6345054": {"variable": {"__ref": "6345032"}, "__type": "VarRef"}, "6345055": {"name": "eventRef", "expr": {"__ref": "6345054"}, "__type": "NameArg"}, "6345056": {"name": "args", "expr": {"__ref": "6345057"}, "__type": "NameArg"}, "6345057": {"exprs": [{"__ref": "6345058"}], "__type": "CollectionExpr"}, "6345058": {"uuid": "Pn7-URvyX", "argType": {"__ref": "6345034"}, "expr": {"__ref": "6345059"}, "__type": "FunctionArg"}, "6345059": {"path": ["$state", "nicknames"], "fallback": null, "__type": "ObjectPath"}, "6345066": {"path": ["$state", "nicknames"], "fallback": null, "__type": "ObjectPath"}, "6345069": {"code": "6", "fallback": null, "__type": "CustomCode"}, "6345078": {"path": ["currentIndex"], "fallback": {"__ref": "6345079"}, "__type": "ObjectPath"}, "6345079": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "6345080": {"name": "variable", "expr": {"__ref": "6345066"}, "__type": "NameArg"}, "6345081": {"name": "operation", "expr": {"__ref": "6345069"}, "__type": "NameArg"}, "6345082": {"name": "startIndex", "expr": {"__ref": "6345078"}, "__type": "NameArg"}, "6345083": {"name": "deleteCount", "expr": {"__ref": "6345085"}, "__type": "NameArg"}, "6345084": {"interactionName": "Invoke onNicknamesChange", "actionName": "invokeEventHandler", "args": [{"__ref": "6345092"}, {"__ref": "6345093"}], "condExpr": null, "conditionalMode": "always", "uuid": "8yEexQ5D0", "parent": {"__ref": "3379424"}, "__type": "Interaction"}, "6345085": {"code": "1", "fallback": null, "__type": "CustomCode"}, "6345091": {"variable": {"__ref": "6345032"}, "__type": "VarRef"}, "6345092": {"name": "eventRef", "expr": {"__ref": "6345091"}, "__type": "NameArg"}, "6345093": {"name": "args", "expr": {"__ref": "6345094"}, "__type": "NameArg"}, "6345094": {"exprs": [{"__ref": "6345095"}], "__type": "CollectionExpr"}, "6345095": {"uuid": "0R0j_3cYl", "argType": {"__ref": "6345034"}, "expr": {"__ref": "6345096"}, "__type": "FunctionArg"}, "6345096": {"path": ["$state", "nicknames"], "fallback": null, "__type": "ObjectPath"}, "6345097": {"type": {"__ref": "6345099"}, "variable": {"__ref": "6345098"}, "uuid": "dh2COGA4MF", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "6345100"}, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "6345098": {"name": "defaultNicknames", "uuid": "tlpSUi5MA", "__type": "Var"}, "6345099": {"name": "any", "__type": "AnyType"}, "6345100": {"code": "[\"A\",\"B\",\"C\"]", "fallback": null, "__type": "CustomCode"}, "6345103": {"path": ["$props", "defaultNicknames"], "fallback": {"__ref": "6345104"}, "__type": "ObjectPath"}, "6345104": {"code": "[\"A\",\"B\",\"C\"]", "fallback": null, "__type": "CustomCode"}, "6345105": {"param": {"__ref": "6345097"}, "expr": {"__ref": "6345108"}, "__type": "Arg"}, "6345108": {"path": ["$state", "person", "nicknames"], "fallback": {"__ref": "6345109"}, "__type": "ObjectPath"}, "6345109": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "16883001": {"param": {"__ref": "16883002"}, "accessType": "private", "variableType": "text", "onChangeParam": {"__ref": "64439154"}, "tplNode": {"__ref": "64439090"}, "implicitState": {"__ref": "64439002"}, "__type": "State"}, "16883002": {"type": {"__ref": "16883005"}, "state": {"__ref": "16883001"}, "variable": {"__ref": "16883004"}, "uuid": "PyBhcwcmXi", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "16883003": {"type": {"__ref": "16883007"}, "state": {"__ref": "64439002"}, "variable": {"__ref": "16883006"}, "uuid": "zCm-_b7Jg-", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "16883004": {"name": "FormField undefined value", "uuid": "RT24TBJCd", "__type": "Var"}, "16883005": {"name": "text", "__type": "Text"}, "16883006": {"name": "onValueChange", "uuid": "oBveFD1snf", "__type": "Var"}, "16883007": {"name": "func", "params": [{"__ref": "16883008"}], "__type": "FunctionType"}, "16883008": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "16883009"}, "__type": "ArgType"}, "16883009": {"name": "text", "__type": "Text"}, "17043001": {"name": "customFunction", "expr": {"__ref": "17043002"}, "__type": "NameArg"}, "17043002": {"argNames": [], "bodyExpr": {"__ref": "4022702"}, "__type": "FunctionExpr"}, "17043004": {"interactionName": "Invoke on<PERSON><PERSON><PERSON><PERSON><PERSON>", "actionName": "invokeEventHandler", "args": [{"__ref": "17043011"}, {"__ref": "17043012"}], "condExpr": null, "conditionalMode": "always", "uuid": "ttIYg5QzV", "parent": {"__ref": "3379382"}, "__type": "Interaction"}, "17043010": {"variable": {"__ref": "46885002"}, "__type": "VarRef"}, "17043011": {"name": "eventRef", "expr": {"__ref": "17043010"}, "__type": "NameArg"}, "17043012": {"name": "args", "expr": {"__ref": "17043013"}, "__type": "NameArg"}, "17043013": {"exprs": [{"__ref": "17043014"}], "__type": "CollectionExpr"}, "17043014": {"uuid": "rQ4JiPO30", "argType": {"__ref": "46885004"}, "expr": {"__ref": "17043015"}, "__type": "FunctionArg"}, "17043015": {"code": "($state.person)", "fallback": null, "__type": "CustomCode"}, "17043019": {"path": ["$props", "initialPerson"], "fallback": null, "__type": "ObjectPath"}, "17043020": {"code": "{\"firstName\":\"First\",\"lastName\":\"Last\",\"nicknames\":[\"A\",\"B\",\"C\"]}", "fallback": null, "__type": "CustomCode"}, "28105002": {"param": {"__ref": "3379393"}, "expr": {"__ref": "28105003"}, "__type": "Arg"}, "28105003": {"path": ["$state", "person", "nicknames"], "fallback": {"__ref": "28105004"}, "__type": "ObjectPath"}, "28105004": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "28678001": {"interactions": [{"__ref": "28678002"}], "__type": "EventHandler"}, "28678002": {"interactionName": "Set people", "actionName": "updateVariable", "args": [{"__ref": "28678010"}, {"__ref": "28678011"}, {"__ref": "28678012"}], "condExpr": null, "conditionalMode": "always", "uuid": "vpb4KpUwY", "parent": {"__ref": "28678001"}, "__type": "Interaction"}, "28678005": {"path": ["$state", "people"], "fallback": null, "__type": "ObjectPath"}, "28678009": {"code": "5", "fallback": null, "__type": "CustomCode"}, "28678010": {"name": "variable", "expr": {"__ref": "28678005"}, "__type": "NameArg"}, "28678011": {"name": "operation", "expr": {"__ref": "28678009"}, "__type": "NameArg"}, "28678012": {"name": "value", "expr": {"__ref": "28678013"}, "__type": "NameArg"}, "28678013": {"code": "(({\n    firstName: \"\",\n    lastName: \"\",\n    nicknames: []\n}))", "fallback": null, "__type": "CustomCode"}, "33186001": {"type": {"__ref": "33186003"}, "variable": {"__ref": "33186002"}, "uuid": "8C7ZxBWMsg", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "33186002": {"name": "onDelete", "uuid": "oLiVqXQHjC", "__type": "Var"}, "33186003": {"name": "func", "params": [], "__type": "FunctionType"}, "33186004": {"interactions": [{"__ref": "33186005"}], "__type": "EventHandler"}, "33186005": {"interactionName": "Invoke onDelete", "actionName": "invokeEventHandler", "args": [{"__ref": "33186010"}], "condExpr": null, "conditionalMode": "always", "uuid": "UZOPbhRPW", "parent": {"__ref": "33186004"}, "__type": "Interaction"}, "33186010": {"name": "eventRef", "expr": {"__ref": "33186011"}, "__type": "NameArg"}, "33186011": {"variable": {"__ref": "33186002"}, "__type": "VarRef"}, "33186012": {"param": {"__ref": "33186001"}, "expr": {"__ref": "33186013"}, "__type": "Arg"}, "33186013": {"interactions": [{"__ref": "33186014"}], "__type": "EventHandler"}, "33186014": {"interactionName": "Custom function", "actionName": "customFunction", "args": [{"__ref": "33186019"}], "condExpr": null, "conditionalMode": "always", "uuid": "SPH5O3EeJ", "parent": {"__ref": "33186013"}, "__type": "Interaction"}, "33186019": {"name": "customFunction", "expr": {"__ref": "33186020"}, "__type": "NameArg"}, "33186020": {"argNames": [], "bodyExpr": {"__ref": "4022703"}, "__type": "FunctionExpr"}, "33859001": {"components": [{"__ref": "36693001"}, {"__ref": "36693002"}, {"__ref": "36693049"}, {"__ref": "36693073"}, {"__ref": "36693106"}, {"__ref": "36693134"}, {"__ref": "3378052"}, {"__ref": "3378088"}], "arenas": [], "pageArenas": [{"__ref": "36693050"}], "componentArenas": [{"__ref": "36693074"}, {"__ref": "36693107"}, {"__ref": "36693135"}, {"__ref": "3378053"}, {"__ref": "3378089"}], "globalVariantGroups": [{"__ref": "33859003"}], "userManagedFonts": [], "globalVariant": {"__ref": "33859007"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "33859008"}], "activeTheme": {"__ref": "33859008"}, "imageAssets": [{"__ref": "36693132"}, {"__ref": "36693133"}, {"__ref": "3378087"}], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "33859003"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {"text-input": {"__ref": "36693134"}, "button": {"__ref": "3378088"}}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "33859003": {"type": "global-screen", "param": {"__ref": "33859004"}, "uuid": "3I_coizMYwk", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "33859004": {"type": {"__ref": "33859006"}, "variable": {"__ref": "33859005"}, "uuid": "ZxSer-oZPja", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "33859005": {"name": "Screen", "uuid": "66BZiGGUTJ", "__type": "Var"}, "33859006": {"name": "text", "__type": "Text"}, "33859007": {"uuid": "VIG_fQbMGfT", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33859008": {"defaultStyle": {"__ref": "33859009"}, "styles": [{"__ref": "33859024"}, {"__ref": "33859033"}, {"__ref": "33859042"}, {"__ref": "33859051"}, {"__ref": "33859060"}, {"__ref": "33859069"}, {"__ref": "33859077"}, {"__ref": "33859081"}, {"__ref": "33859085"}, {"__ref": "33859093"}, {"__ref": "33859118"}, {"__ref": "33859143"}, {"__ref": "33859154"}], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "33859009": {"name": "Default Typography", "rs": {"__ref": "33859010"}, "preview": null, "uuid": "ObHe8zNj77", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "33859010": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "33859024": {"selector": "h1", "style": {"__ref": "33859025"}, "__type": "ThemeStyle"}, "33859025": {"name": "Default \"h1\"", "rs": {"__ref": "33859026"}, "preview": null, "uuid": "NO6DMl1tdw", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "33859026": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "33859033": {"selector": "h2", "style": {"__ref": "33859034"}, "__type": "ThemeStyle"}, "33859034": {"name": "Default \"h2\"", "rs": {"__ref": "33859035"}, "preview": null, "uuid": "teCIvhjXrC", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "33859035": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "33859042": {"selector": "h3", "style": {"__ref": "33859043"}, "__type": "ThemeStyle"}, "33859043": {"name": "Default \"h3\"", "rs": {"__ref": "33859044"}, "preview": null, "uuid": "6D9v8eNOY7", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "33859044": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "33859051": {"selector": "h4", "style": {"__ref": "33859052"}, "__type": "ThemeStyle"}, "33859052": {"name": "Default \"h4\"", "rs": {"__ref": "33859053"}, "preview": null, "uuid": "KL41K8rE7x", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "33859053": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "33859060": {"selector": "h5", "style": {"__ref": "33859061"}, "__type": "ThemeStyle"}, "33859061": {"name": "Default \"h5\"", "rs": {"__ref": "33859062"}, "preview": null, "uuid": "PKjrMI-4rg", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "33859062": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "33859069": {"selector": "h6", "style": {"__ref": "33859070"}, "__type": "ThemeStyle"}, "33859070": {"name": "Default \"h6\"", "rs": {"__ref": "33859071"}, "preview": null, "uuid": "4sgDQSyjHf", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "33859071": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "33859077": {"selector": "a", "style": {"__ref": "33859078"}, "__type": "ThemeStyle"}, "33859078": {"name": "Default \"a\"", "rs": {"__ref": "33859079"}, "preview": null, "uuid": "nUZHoTzWbe", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "33859079": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "33859081": {"selector": "a:hover", "style": {"__ref": "33859082"}, "__type": "ThemeStyle"}, "33859082": {"name": "Default \"a:hover\"", "rs": {"__ref": "33859083"}, "preview": null, "uuid": "Glb0phFtxq", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "33859083": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "33859085": {"selector": "blockquote", "style": {"__ref": "33859086"}, "__type": "ThemeStyle"}, "33859086": {"name": "Default \"blockquote\"", "rs": {"__ref": "33859087"}, "preview": null, "uuid": "H-xl295uwl", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "33859087": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "33859093": {"selector": "code", "style": {"__ref": "33859094"}, "__type": "ThemeStyle"}, "33859094": {"name": "Default \"code\"", "rs": {"__ref": "33859095"}, "preview": null, "uuid": "mXqtW52KoO", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "33859095": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "33859118": {"selector": "pre", "style": {"__ref": "33859119"}, "__type": "ThemeStyle"}, "33859119": {"name": "Default \"pre\"", "rs": {"__ref": "33859120"}, "preview": null, "uuid": "ej9V-QYzbs", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "33859120": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "33859143": {"selector": "ol", "style": {"__ref": "33859144"}, "__type": "ThemeStyle"}, "33859144": {"name": "Default \"ol\"", "rs": {"__ref": "33859145"}, "preview": null, "uuid": "Aps9jvvtuU", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "33859145": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "33859154": {"selector": "ul", "style": {"__ref": "33859155"}, "__type": "ThemeStyle"}, "33859155": {"name": "Default \"ul\"", "rs": {"__ref": "33859156"}, "preview": null, "uuid": "OZmq4kooVr", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "33859156": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "36353001": {"param": {"__ref": "64439003"}, "expr": {"__ref": "36353004"}, "__type": "Arg"}, "36353004": {"path": ["$state", "person", "firstName"], "fallback": {"__ref": "36353005"}, "__type": "ObjectPath"}, "36353005": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "36693001": {"uuid": "eULPXHMVn-", "name": "hostless-plasmic-head", "params": [{"__ref": "36693003"}, {"__ref": "36693004"}, {"__ref": "36693005"}, {"__ref": "36693006"}], "states": [], "tplTree": {"__ref": "36693007"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "36693008"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "36693009"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "36693002": {"uuid": "AHGfSbvG54", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "36693010"}, {"__ref": "36693011"}, {"__ref": "36693012"}, {"__ref": "36693013"}, {"__ref": "36693014"}], "states": [], "tplTree": {"__ref": "36693015"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "36693016"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "36693017"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "36693003": {"type": {"__ref": "36693019"}, "variable": {"__ref": "36693018"}, "uuid": "6dbU_bk3DE", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "36693004": {"type": {"__ref": "36693021"}, "variable": {"__ref": "36693020"}, "uuid": "L1FOcJOxSU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "36693005": {"type": {"__ref": "36693023"}, "variable": {"__ref": "36693022"}, "uuid": "FXI9WBbpI9", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "36693006": {"type": {"__ref": "36693025"}, "variable": {"__ref": "36693024"}, "uuid": "buj4NjLyCQ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "36693007": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "X_NILz-GW3", "parent": null, "locked": null, "vsettings": [{"__ref": "36693026"}], "__type": "TplTag"}, "36693008": {"uuid": "EFi9h7thD", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "36693009": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "Head", "importName": "PlasmicHead", "description": "Used to add page metadata to HTML <head />.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "36693010": {"type": {"__ref": "36693028"}, "variable": {"__ref": "36693027"}, "uuid": "75WBgblJ2J", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "36693011": {"type": {"__ref": "36693030"}, "variable": {"__ref": "36693029"}, "uuid": "CsQXXGMg88h", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "36693012": {"type": {"__ref": "36693032"}, "tplSlot": {"__ref": "36693037"}, "variable": {"__ref": "36693031"}, "uuid": "PuLoT1Jg1ua", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "36693013": {"type": {"__ref": "36693034"}, "variable": {"__ref": "36693033"}, "uuid": "k_nQ8BS0LAQ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "36693014": {"type": {"__ref": "36693036"}, "variable": {"__ref": "36693035"}, "uuid": "_P8x05WekrO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "36693015": {"tag": "div", "name": null, "children": [{"__ref": "36693037"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "p8s7opTnyv", "parent": null, "locked": null, "vsettings": [{"__ref": "36693038"}], "__type": "TplTag"}, "36693016": {"uuid": "Czp42AsHDq", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "36693017": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Source Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "36693018": {"name": "title", "uuid": "nbehY6yq5O", "__type": "Var"}, "36693019": {"name": "text", "__type": "Text"}, "36693020": {"name": "description", "uuid": "DgIt8KY5kp", "__type": "Var"}, "36693021": {"name": "text", "__type": "Text"}, "36693022": {"name": "image", "uuid": "-lCxd9g3f4", "__type": "Var"}, "36693023": {"name": "img", "__type": "Img"}, "36693024": {"name": "canonical", "uuid": "WBEhvHOYmZ", "__type": "Var"}, "36693025": {"name": "text", "__type": "Text"}, "36693026": {"variants": [{"__ref": "36693008"}], "args": [], "attrs": {}, "rs": {"__ref": "36693039"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693027": {"name": "dataOp", "uuid": "8bbVqNmbod", "__type": "Var"}, "36693028": {"name": "any", "__type": "AnyType"}, "36693029": {"name": "name", "uuid": "nq7qEHnUVKM", "__type": "Var"}, "36693030": {"name": "text", "__type": "Text"}, "36693031": {"name": "children", "uuid": "GlEi2z1Y7IX", "__type": "Var"}, "36693032": {"name": "renderFunc", "params": [{"__ref": "36693040"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "36693033": {"name": "pageSize", "uuid": "v5fx8qJ046J", "__type": "Var"}, "36693034": {"name": "num", "__type": "<PERSON><PERSON>"}, "36693035": {"name": "pageIndex", "uuid": "awAq4_eMF7D", "__type": "Var"}, "36693036": {"name": "num", "__type": "<PERSON><PERSON>"}, "36693037": {"param": {"__ref": "36693012"}, "defaultContents": [], "uuid": "FQlOUpikkHA", "parent": {"__ref": "36693015"}, "locked": null, "vsettings": [{"__ref": "36693041"}], "__type": "TplSlot"}, "36693038": {"variants": [{"__ref": "36693016"}], "args": [], "attrs": {}, "rs": {"__ref": "36693042"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693039": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "36693040": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "36693045"}, "__type": "ArgType"}, "36693041": {"variants": [{"__ref": "36693016"}], "args": [], "attrs": {}, "rs": {"__ref": "36693046"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693042": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "36693045": {"name": "any", "__type": "AnyType"}, "36693046": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693049": {"uuid": "Wpe-pe9HHq", "name": "Homepage", "params": [{"__ref": "3379082"}, {"__ref": "64439139"}], "states": [{"__ref": "3379081"}], "tplTree": {"__ref": "36693051"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "36693052"}], "variantGroups": [], "pageMeta": {"__ref": "36693053"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "36693050": {"component": {"__ref": "36693049"}, "matrix": {"__ref": "36693054"}, "customMatrix": {"__ref": "3312801"}, "__type": "PageArena"}, "36693051": {"tag": "div", "name": null, "children": [{"__ref": "3379120"}, {"__ref": "46885033"}, {"__ref": "3379090"}, {"__ref": "50599007"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "rymQIZOcb", "parent": null, "locked": null, "vsettings": [{"__ref": "36693055"}], "__type": "TplTag"}, "36693052": {"uuid": "NA50HgbcLq", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "36693053": {"path": "/", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "36693054": {"rows": [{"__ref": "36693056"}], "__type": "ArenaFrameGrid"}, "36693055": {"variants": [{"__ref": "36693052"}], "args": [], "attrs": {"data-testid": {"__ref": "6345006"}}, "rs": {"__ref": "36693057"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693056": {"cols": [{"__ref": "36693058"}, {"__ref": "36693059"}], "rowKey": {"__ref": "36693052"}, "__type": "ArenaFrameRow"}, "36693057": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "flex-row-gap": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "36693058": {"frame": {"__ref": "36693065"}, "cellKey": null, "__type": "ArenaFrameCell"}, "36693059": {"frame": {"__ref": "36693066"}, "cellKey": null, "__type": "ArenaFrameCell"}, "36693065": {"uuid": "ACf-zObPGK", "width": 1366, "height": 768, "container": {"__ref": "36693067"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "36693052"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "36693066": {"uuid": "loMtijVNWN", "width": 414, "height": 736, "container": {"__ref": "36693068"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "36693052"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "36693067": {"name": null, "component": {"__ref": "36693049"}, "uuid": "vY1on_047r", "parent": null, "locked": null, "vsettings": [{"__ref": "36693069"}], "__type": "TplComponent"}, "36693068": {"name": null, "component": {"__ref": "36693049"}, "uuid": "GJoWBn4EJU", "parent": null, "locked": null, "vsettings": [{"__ref": "36693070"}], "__type": "TplComponent"}, "36693069": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "36693071"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693070": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "36693072"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693071": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693072": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693073": {"uuid": "JyFGsk1rLW", "name": "Person", "params": [{"__ref": "16883002"}, {"__ref": "3378033"}, {"__ref": "3379355"}, {"__ref": "46885001"}, {"__ref": "46885006"}, {"__ref": "33186001"}, {"__ref": "64439099"}, {"__ref": "64439144"}, {"__ref": "64439154"}], "states": [{"__ref": "16883001"}, {"__ref": "3378032"}, {"__ref": "3379354"}], "tplTree": {"__ref": "36693075"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "36693076"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "36693074": {"component": {"__ref": "36693073"}, "matrix": {"__ref": "36693077"}, "customMatrix": {"__ref": "36693078"}, "__type": "ComponentArena"}, "36693075": {"tag": "div", "name": null, "children": [{"__ref": "64439090"}, {"__ref": "3378031"}, {"__ref": "3379050"}, {"__ref": "3379289"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "2dQdU1Dqt", "parent": null, "locked": null, "vsettings": [{"__ref": "36693079"}], "__type": "TplTag"}, "36693076": {"uuid": "h1yplSEjCX", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "36693077": {"rows": [{"__ref": "36693080"}], "__type": "ArenaFrameGrid"}, "36693078": {"rows": [{"__ref": "36693081"}], "__type": "ArenaFrameGrid"}, "36693079": {"variants": [{"__ref": "36693076"}], "args": [], "attrs": {"data-testid": {"__ref": "6345022"}}, "rs": {"__ref": "36693082"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693080": {"cols": [{"__ref": "36693083"}], "rowKey": null, "__type": "ArenaFrameRow"}, "36693081": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "36693082": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "wrap", "height": "wrap", "flex-row-gap": "10px"}, "mixins": [], "__type": "RuleSet"}, "36693083": {"frame": {"__ref": "36693089"}, "cellKey": {"__ref": "36693076"}, "__type": "ArenaFrameCell"}, "36693089": {"uuid": "X447P-Rpro", "width": 800, "height": 600, "container": {"__ref": "36693090"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "36693076"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "36693090": {"name": null, "component": {"__ref": "36693073"}, "uuid": "3PHz0z4cPI", "parent": null, "locked": null, "vsettings": [{"__ref": "36693091"}], "__type": "TplComponent"}, "36693091": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "36693092"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693092": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693106": {"uuid": "7auyJLMfST", "name": "FormField", "params": [{"__ref": "64439003"}, {"__ref": "16883003"}, {"__ref": "3378002"}, {"__ref": "6345011"}], "states": [{"__ref": "64439002"}], "tplTree": {"__ref": "36693108"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "36693109"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "36693107": {"component": {"__ref": "36693106"}, "matrix": {"__ref": "36693110"}, "customMatrix": {"__ref": "36693111"}, "__type": "ComponentArena"}, "36693108": {"tag": "div", "name": null, "children": [{"__ref": "3378020"}, {"__ref": "64439001"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "0_-tUXxaJ", "parent": null, "locked": null, "vsettings": [{"__ref": "36693112"}], "__type": "TplTag"}, "36693109": {"uuid": "lVia9SPXv-", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "36693110": {"rows": [{"__ref": "36693113"}], "__type": "ArenaFrameGrid"}, "36693111": {"rows": [{"__ref": "36693114"}], "__type": "ArenaFrameGrid"}, "36693112": {"variants": [{"__ref": "36693109"}], "args": [], "attrs": {}, "rs": {"__ref": "36693115"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693113": {"cols": [{"__ref": "36693116"}], "rowKey": null, "__type": "ArenaFrameRow"}, "36693114": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "36693115": {"values": {"display": "flex", "flex-direction": "row", "position": "relative", "width": "wrap", "height": "wrap", "align-items": "center", "justify-content": "flex-start", "flex-column-gap": "8px"}, "mixins": [], "__type": "RuleSet"}, "36693116": {"frame": {"__ref": "36693122"}, "cellKey": {"__ref": "36693109"}, "__type": "ArenaFrameCell"}, "36693122": {"uuid": "wvWBK0C56j", "width": 340, "height": 340, "container": {"__ref": "36693123"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "36693109"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "36693123": {"name": null, "component": {"__ref": "36693106"}, "uuid": "IUZqlci0Hg", "parent": null, "locked": null, "vsettings": [{"__ref": "36693124"}], "__type": "TplComponent"}, "36693124": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "36693125"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693125": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693132": {"uuid": "G9nuosa2Upj", "name": "search.svg", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAyNCAyNCIgaGVpZ2h0PSIxZW0iIHdpZHRoPSIxZW0iPgogIDxwYXRoIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHN0cm9rZS13aWR0aD0iMS41IiBkPSJNMTkuMjUgMTkuMjVMMTUuNSAxNS41TTQuNzUgMTFhNi4yNSA2LjI1IDAgMTExMi41IDAgNi4yNSA2LjI1IDAgMDEtMTIuNSAweiIvPgo8L3N2Zz4=", "width": 150, "height": 150, "aspectRatio": null, "__type": "ImageAsset"}, "36693133": {"uuid": "wszEm8Y27Yl", "name": "check.svg", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAyNCAyNCIgaGVpZ2h0PSIxZW0iIHdpZHRoPSIxZW0iIHN0eWxlPSJmaWxsOiBjdXJyZW50Y29sb3I7Ij4KICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTE4LjQxNiA1Ljg3NmEuNzUuNzUgMCAwMS4yMDggMS4wNEwxMS40MiAxNy43MjFhMS43NSAxLjc1IDAgMDEtMi44NzEuMDZsLTMuMTU2LTQuMzRhLjc1Ljc1IDAgMTExLjIxNC0uODgybDMuMTU1IDQuMzM5YS4yNS4yNSAwIDAwLjQxLS4wMDlsNy4yMDQtMTAuODA1YS43NS43NSAwIDAxMS4wNC0uMjA4eiIgZmlsbD0iY3VycmVudENvbG9yIi8+Cjwvc3ZnPg==", "width": 150, "height": 150, "aspectRatio": 1000000, "__type": "ImageAsset"}, "36693134": {"uuid": "a6azg3mj7XT", "name": "TextInput", "params": [{"__ref": "36693139"}, {"__ref": "36693140"}, {"__ref": "36693141"}, {"__ref": "36693142"}, {"__ref": "36693143"}, {"__ref": "36693144"}, {"__ref": "36693145"}, {"__ref": "36693146"}, {"__ref": "36693147"}, {"__ref": "36693148"}, {"__ref": "36693149"}, {"__ref": "36693150"}, {"__ref": "36693151"}, {"__ref": "6345007"}, {"__ref": "64439159"}, {"__ref": "64439164"}, {"__ref": "64439169"}, {"__ref": "64439174"}], "states": [{"__ref": "36693152"}, {"__ref": "36693153"}, {"__ref": "36693154"}, {"__ref": "36693155"}, {"__ref": "36693156"}], "tplTree": {"__ref": "36693157"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "36693158"}, {"__ref": "36693159"}, {"__ref": "36693160"}, {"__ref": "36693161"}, {"__ref": "36693162"}, {"__ref": "36693163"}], "variantGroups": [{"__ref": "36693164"}, {"__ref": "36693165"}, {"__ref": "36693166"}, {"__ref": "36693167"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": {"__ref": "36693168"}, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "36693135": {"component": {"__ref": "36693134"}, "matrix": {"__ref": "36693169"}, "customMatrix": {"__ref": "36693170"}, "__type": "ComponentArena"}, "36693139": {"type": {"__ref": "36693175"}, "variable": {"__ref": "36693174"}, "uuid": "VxDq5gGsjF", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "36693176"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "36693140": {"type": {"__ref": "36693178"}, "tplSlot": {"__ref": "36693245"}, "variable": {"__ref": "36693177"}, "uuid": "5o96Bp4_-V", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "36693141": {"type": {"__ref": "36693180"}, "tplSlot": {"__ref": "36693226"}, "variable": {"__ref": "36693179"}, "uuid": "1BtkOaFROf", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "36693142": {"type": {"__ref": "36693182"}, "state": {"__ref": "36693152"}, "variable": {"__ref": "36693181"}, "uuid": "LJ4G9iF690", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "36693143": {"type": {"__ref": "36693184"}, "state": {"__ref": "36693153"}, "variable": {"__ref": "36693183"}, "uuid": "4lAW-6V_Lb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "36693144": {"type": {"__ref": "36693186"}, "state": {"__ref": "36693154"}, "variable": {"__ref": "36693185"}, "uuid": "fblMRy9mlZ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "36693145": {"type": {"__ref": "36693188"}, "state": {"__ref": "36693155"}, "variable": {"__ref": "36693187"}, "uuid": "Yq-DrDRRZy", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "36693146": {"type": {"__ref": "6345002"}, "state": {"__ref": "36693156"}, "variable": {"__ref": "36693189"}, "uuid": "a21FB3dGeQ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "6345001"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "36693147": {"type": {"__ref": "36693192"}, "variable": {"__ref": "36693191"}, "uuid": "Mo0hvUk9dIj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "36693148": {"type": {"__ref": "36693194"}, "variable": {"__ref": "36693193"}, "uuid": "8xOk5HO2cjS", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "36693149": {"type": {"__ref": "36693196"}, "variable": {"__ref": "36693195"}, "uuid": "gykRdy6M15n", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "36693150": {"type": {"__ref": "36693198"}, "variable": {"__ref": "36693197"}, "uuid": "zv3s4gjFb9f", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "36693151": {"type": {"__ref": "6345003"}, "variable": {"__ref": "36693199"}, "uuid": "KlBPhHHeKi5", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "36693152": {"variantGroup": {"__ref": "36693164"}, "param": {"__ref": "36693142"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64439159"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "36693153": {"variantGroup": {"__ref": "36693165"}, "param": {"__ref": "36693143"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64439164"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "36693154": {"variantGroup": {"__ref": "36693166"}, "param": {"__ref": "36693144"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64439169"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "36693155": {"variantGroup": {"__ref": "36693167"}, "param": {"__ref": "36693145"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64439174"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "36693156": {"param": {"__ref": "36693146"}, "accessType": "writable", "variableType": "text", "onChangeParam": {"__ref": "36693151"}, "tplNode": null, "implicitState": null, "__type": "State"}, "36693157": {"tag": "div", "name": null, "children": [{"__ref": "36693201"}, {"__ref": "36693202"}, {"__ref": "36693203"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "ksVU3Th9Aeu", "parent": null, "locked": null, "vsettings": [{"__ref": "36693204"}, {"__ref": "36693205"}, {"__ref": "36693206"}, {"__ref": "36693207"}, {"__ref": "36693208"}, {"__ref": "36693209"}, {"__ref": "36693210"}], "__type": "TplTag"}, "36693158": {"uuid": "92oplRhI_1m", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "36693159": {"uuid": "cK7WSA0KGZ9", "name": "", "selectors": [":focus"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": {"__ref": "36693202"}, "__type": "<PERSON><PERSON><PERSON>"}, "36693160": {"uuid": "d2et59vOict", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "36693161": {"uuid": "cskTSW_Llxl", "name": "", "selectors": [":focus-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "36693162": {"uuid": "wKOeTVJf_74", "name": "", "selectors": [":focus-visible-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "36693163": {"uuid": "SpM_t9WZcXH", "name": "", "selectors": ["::placeholder"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": {"__ref": "36693202"}, "__type": "<PERSON><PERSON><PERSON>"}, "36693164": {"type": "component", "param": {"__ref": "36693142"}, "linkedState": {"__ref": "36693152"}, "uuid": "9gumajOQpmD", "variants": [{"__ref": "36693211"}], "multi": false, "__type": "ComponentVariantGroup"}, "36693165": {"type": "component", "param": {"__ref": "36693143"}, "linkedState": {"__ref": "36693153"}, "uuid": "dEygNMuPg62", "variants": [{"__ref": "36693212"}], "multi": false, "__type": "ComponentVariantGroup"}, "36693166": {"type": "component", "param": {"__ref": "36693144"}, "linkedState": {"__ref": "36693154"}, "uuid": "ATDy6P5UCyj", "variants": [{"__ref": "36693213"}], "multi": false, "__type": "ComponentVariantGroup"}, "36693167": {"type": "component", "param": {"__ref": "36693145"}, "linkedState": {"__ref": "36693155"}, "uuid": "5VMDzRPmhZy", "variants": [{"__ref": "36693214"}], "multi": false, "__type": "ComponentVariantGroup"}, "36693168": {"type": "text-input", "__type": "PlumeInfo"}, "36693169": {"rows": [{"__ref": "36693215"}, {"__ref": "36693216"}, {"__ref": "36693217"}, {"__ref": "36693218"}, {"__ref": "36693219"}], "__type": "ArenaFrameGrid"}, "36693170": {"rows": [{"__ref": "36693220"}], "__type": "ArenaFrameGrid"}, "36693174": {"name": "placeholder", "uuid": "WGngtYMWi", "__type": "Var"}, "36693175": {"name": "text", "__type": "Text"}, "36693176": {"code": "\"Enter something…\"", "fallback": null, "__type": "CustomCode"}, "36693177": {"name": "end icon", "uuid": "csiDVXi1vk", "__type": "Var"}, "36693178": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "36693179": {"name": "start icon", "uuid": "TEJj1lcjwS", "__type": "Var"}, "36693180": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "36693181": {"name": "Show Start Icon", "uuid": "Z9fkBIzhXy", "__type": "Var"}, "36693182": {"name": "any", "__type": "AnyType"}, "36693183": {"name": "Show End Icon", "uuid": "pM7npmsd3X", "__type": "Var"}, "36693184": {"name": "any", "__type": "AnyType"}, "36693185": {"name": "Is Disabled", "uuid": "umtjTaLWKK", "__type": "Var"}, "36693186": {"name": "any", "__type": "AnyType"}, "36693187": {"name": "Color", "uuid": "NgARfOJ-wi", "__type": "Var"}, "36693188": {"name": "any", "__type": "AnyType"}, "36693189": {"name": "value", "uuid": "V1Ojg9YuKi", "__type": "Var"}, "36693191": {"name": "name", "uuid": "eQib2eDJd_9", "__type": "Var"}, "36693192": {"name": "text", "__type": "Text"}, "36693193": {"name": "required", "uuid": "cdjpiE47Sws", "__type": "Var"}, "36693194": {"name": "bool", "__type": "BoolType"}, "36693195": {"name": "aria-label", "uuid": "yTd7oA8bIhM", "__type": "Var"}, "36693196": {"name": "text", "__type": "Text"}, "36693197": {"name": "aria-<PERSON>by", "uuid": "yNCmHYI-vuu", "__type": "Var"}, "36693198": {"name": "text", "__type": "Text"}, "36693199": {"name": "onChange", "uuid": "75ZZyd_DGNG", "__type": "Var"}, "36693201": {"tag": "div", "name": "start icon container", "children": [{"__ref": "36693226"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "v3peDLNhGAt", "parent": {"__ref": "36693157"}, "locked": null, "vsettings": [{"__ref": "36693227"}, {"__ref": "36693228"}, {"__ref": "36693229"}, {"__ref": "36693230"}, {"__ref": "36693231"}, {"__ref": "36693232"}], "__type": "TplTag"}, "36693202": {"tag": "input", "name": "input", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "7BVDVsX6nY6", "parent": {"__ref": "36693157"}, "locked": null, "vsettings": [{"__ref": "36693233"}, {"__ref": "36693234"}, {"__ref": "36693235"}, {"__ref": "36693236"}, {"__ref": "36693237"}, {"__ref": "36693238"}, {"__ref": "36693239"}, {"__ref": "36693240"}, {"__ref": "36693241"}, {"__ref": "36693242"}, {"__ref": "36693243"}, {"__ref": "36693244"}], "__type": "TplTag"}, "36693203": {"tag": "div", "name": "end icon container", "children": [{"__ref": "36693245"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "05Ub3_NiIlU", "parent": {"__ref": "36693157"}, "locked": null, "vsettings": [{"__ref": "36693246"}, {"__ref": "36693247"}, {"__ref": "36693248"}], "__type": "TplTag"}, "36693204": {"variants": [{"__ref": "36693158"}], "args": [], "attrs": {}, "rs": {"__ref": "36693249"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693205": {"variants": [{"__ref": "36693160"}], "args": [], "attrs": {}, "rs": {"__ref": "36693250"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693206": {"variants": [{"__ref": "36693161"}], "args": [], "attrs": {}, "rs": {"__ref": "36693251"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693207": {"variants": [{"__ref": "36693162"}], "args": [], "attrs": {}, "rs": {"__ref": "36693252"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693208": {"variants": [{"__ref": "36693213"}], "args": [], "attrs": {}, "rs": {"__ref": "36693253"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693209": {"variants": [{"__ref": "36693211"}], "args": [], "attrs": {}, "rs": {"__ref": "36693254"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693210": {"variants": [{"__ref": "36693214"}], "args": [], "attrs": {}, "rs": {"__ref": "36693255"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693211": {"uuid": "fAo7lzi0ppp", "name": "Show Start Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "36693164"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "36693212": {"uuid": "m0APHqx5dNv", "name": "Show End Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "36693165"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "36693213": {"uuid": "DgPIEiVjfSA", "name": "Is Disabled", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "36693166"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "36693214": {"uuid": "RH9qxdho2vC", "name": "Dark", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "36693167"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "36693215": {"cols": [{"__ref": "36693256"}, {"__ref": "36693257"}, {"__ref": "36693258"}, {"__ref": "36693259"}], "rowKey": null, "__type": "ArenaFrameRow"}, "36693216": {"cols": [{"__ref": "36693260"}], "rowKey": {"__ref": "36693164"}, "__type": "ArenaFrameRow"}, "36693217": {"cols": [{"__ref": "36693261"}], "rowKey": {"__ref": "36693165"}, "__type": "ArenaFrameRow"}, "36693218": {"cols": [{"__ref": "36693262"}], "rowKey": {"__ref": "36693166"}, "__type": "ArenaFrameRow"}, "36693219": {"cols": [{"__ref": "36693263"}], "rowKey": {"__ref": "36693167"}, "__type": "ArenaFrameRow"}, "36693220": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "36693226": {"param": {"__ref": "36693141"}, "defaultContents": [{"__ref": "36693270"}], "uuid": "xAE-0UgtBoX", "parent": {"__ref": "36693201"}, "locked": null, "vsettings": [{"__ref": "36693271"}, {"__ref": "36693272"}, {"__ref": "36693273"}], "__type": "TplSlot"}, "36693227": {"variants": [{"__ref": "36693158"}], "args": [], "attrs": {}, "rs": {"__ref": "36693274"}, "dataCond": {"__ref": "36693275"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693228": {"variants": [{"__ref": "36693162"}], "args": [], "attrs": {}, "rs": {"__ref": "36693276"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693229": {"variants": [{"__ref": "36693211"}], "args": [], "attrs": {}, "rs": {"__ref": "36693277"}, "dataCond": {"__ref": "36693278"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693230": {"variants": [{"__ref": "36693160"}], "args": [], "attrs": {}, "rs": {"__ref": "36693279"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693231": {"variants": [{"__ref": "36693213"}], "args": [], "attrs": {}, "rs": {"__ref": "36693280"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693232": {"variants": [{"__ref": "36693214"}], "args": [], "attrs": {}, "rs": {"__ref": "36693281"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693233": {"variants": [{"__ref": "36693158"}], "args": [], "attrs": {"type": {"__ref": "36693282"}, "placeholder": {"__ref": "36693283"}, "value": {"__ref": "36693284"}, "name": {"__ref": "36693285"}, "aria-label": {"__ref": "36693286"}, "aria-labelledby": {"__ref": "36693287"}, "required": {"__ref": "36693288"}, "data-testid": {"__ref": "6345008"}}, "rs": {"__ref": "36693289"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693234": {"variants": [{"__ref": "36693159"}], "args": [], "attrs": {}, "rs": {"__ref": "36693290"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693235": {"variants": [{"__ref": "36693213"}], "args": [], "attrs": {"disabled": {"__ref": "36693291"}}, "rs": {"__ref": "36693292"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693236": {"variants": [{"__ref": "36693163"}], "args": [], "attrs": {}, "rs": {"__ref": "36693293"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693237": {"variants": [{"__ref": "36693211"}], "args": [], "attrs": {}, "rs": {"__ref": "36693294"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693238": {"variants": [{"__ref": "36693160"}], "args": [], "attrs": {}, "rs": {"__ref": "36693295"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693239": {"variants": [{"__ref": "36693162"}], "args": [], "attrs": {}, "rs": {"__ref": "36693296"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693240": {"variants": [{"__ref": "36693161"}], "args": [], "attrs": {}, "rs": {"__ref": "36693297"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693241": {"variants": [{"__ref": "36693161"}, {"__ref": "36693159"}], "args": [], "attrs": {}, "rs": {"__ref": "36693298"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693242": {"variants": [{"__ref": "36693161"}, {"__ref": "36693163"}], "args": [], "attrs": {}, "rs": {"__ref": "36693299"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693243": {"variants": [{"__ref": "36693214"}], "args": [], "attrs": {}, "rs": {"__ref": "36693300"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693244": {"variants": [{"__ref": "36693214"}, {"__ref": "36693163"}], "args": [], "attrs": {}, "rs": {"__ref": "36693301"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693245": {"param": {"__ref": "36693140"}, "defaultContents": [{"__ref": "36693302"}], "uuid": "I1nuDBqajWd", "parent": {"__ref": "36693203"}, "locked": null, "vsettings": [{"__ref": "36693303"}, {"__ref": "36693304"}, {"__ref": "36693305"}], "__type": "TplSlot"}, "36693246": {"variants": [{"__ref": "36693158"}], "args": [], "attrs": {}, "rs": {"__ref": "36693306"}, "dataCond": {"__ref": "36693307"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693247": {"variants": [{"__ref": "36693212"}], "args": [], "attrs": {}, "rs": {"__ref": "36693308"}, "dataCond": {"__ref": "36693309"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693248": {"variants": [{"__ref": "36693214"}], "args": [], "attrs": {}, "rs": {"__ref": "36693310"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693249": {"values": {"display": "flex", "flex-direction": "row", "width": "stretch", "height": "wrap", "align-items": "center", "justify-content": "flex-start", "border-top-color": "#DBDBD7", "border-right-color": "#DBDBD7", "border-bottom-color": "#DBDBD7", "border-left-color": "#DBDBD7", "border-top-style": "solid", "border-right-style": "solid", "border-bottom-style": "solid", "border-left-style": "solid", "border-top-width": "1px", "border-right-width": "1px", "border-bottom-width": "1px", "border-left-width": "1px", "border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px", "background": "linear-gradient(#FFFFFF, #FFFFFF)", "position": "sticky", "padding-top": "7px", "padding-right": "11px", "padding-bottom": "7px", "padding-left": "11px"}, "mixins": [], "__type": "RuleSet"}, "36693250": {"values": {"border-top-color": "#C8C7C1", "border-right-color": "#C8C7C1", "border-bottom-color": "#C8C7C1", "border-left-color": "#C8C7C1"}, "mixins": [], "__type": "RuleSet"}, "36693251": {"values": {"border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px", "box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "36693252": {"values": {"border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px", "box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "36693253": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693254": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693255": {"values": {"background": "linear-gradient(#232320, #232320)", "border-top-color": "#717069", "border-right-color": "#717069", "border-bottom-color": "#717069", "border-left-color": "#717069"}, "mixins": [], "__type": "RuleSet"}, "36693256": {"frame": {"__ref": "36693358"}, "cellKey": {"__ref": "36693158"}, "__type": "ArenaFrameCell"}, "36693257": {"frame": {"__ref": "36693359"}, "cellKey": {"__ref": "36693160"}, "__type": "ArenaFrameCell"}, "36693258": {"frame": {"__ref": "36693360"}, "cellKey": {"__ref": "36693161"}, "__type": "ArenaFrameCell"}, "36693259": {"frame": {"__ref": "36693361"}, "cellKey": {"__ref": "36693162"}, "__type": "ArenaFrameCell"}, "36693260": {"frame": {"__ref": "36693362"}, "cellKey": {"__ref": "36693211"}, "__type": "ArenaFrameCell"}, "36693261": {"frame": {"__ref": "36693363"}, "cellKey": {"__ref": "36693212"}, "__type": "ArenaFrameCell"}, "36693262": {"frame": {"__ref": "36693364"}, "cellKey": {"__ref": "36693213"}, "__type": "ArenaFrameCell"}, "36693263": {"frame": {"__ref": "36693365"}, "cellKey": {"__ref": "36693214"}, "__type": "ArenaFrameCell"}, "36693270": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "0ToxrP9y-xB", "parent": {"__ref": "36693226"}, "locked": null, "vsettings": [{"__ref": "36693368"}], "__type": "TplTag"}, "36693271": {"variants": [{"__ref": "36693158"}], "args": [], "attrs": {}, "rs": {"__ref": "36693369"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693272": {"variants": [{"__ref": "36693211"}], "args": [], "attrs": {}, "rs": {"__ref": "36693370"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693273": {"variants": [{"__ref": "36693214"}], "args": [], "attrs": {}, "rs": {"__ref": "36693371"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693274": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "left": "auto", "top": "auto", "margin-right": "8px", "plasmic-display-none": "true"}, "mixins": [], "__type": "RuleSet"}, "36693275": {"code": "true", "fallback": null, "__type": "CustomCode"}, "36693276": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693277": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "36693278": {"code": "true", "fallback": null, "__type": "CustomCode"}, "36693279": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693280": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693281": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693282": {"code": "\"text\"", "fallback": null, "__type": "CustomCode"}, "36693283": {"variable": {"__ref": "36693174"}, "__type": "VarRef"}, "36693284": {"variable": {"__ref": "36693189"}, "__type": "VarRef"}, "36693285": {"variable": {"__ref": "36693191"}, "__type": "VarRef"}, "36693286": {"variable": {"__ref": "36693195"}, "__type": "VarRef"}, "36693287": {"variable": {"__ref": "36693197"}, "__type": "VarRef"}, "36693288": {"variable": {"__ref": "36693193"}, "__type": "VarRef"}, "36693289": {"values": {"width": "stretch", "left": "auto", "top": "auto", "padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px", "border-top-width": "0px", "border-right-width": "0px", "border-bottom-width": "0px", "border-left-width": "0px", "background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "36693290": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693291": {"code": "true", "fallback": null, "__type": "CustomCode"}, "36693292": {"values": {"cursor": "not-allowed"}, "mixins": [], "__type": "RuleSet"}, "36693293": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "36693294": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693295": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693296": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693297": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693298": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693299": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693300": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "36693301": {"values": {"color": "#C8C7C1"}, "mixins": [], "__type": "RuleSet"}, "36693302": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "jVJ5ooLWVWB", "parent": {"__ref": "36693245"}, "locked": null, "vsettings": [{"__ref": "36693398"}], "__type": "TplTag"}, "36693303": {"variants": [{"__ref": "36693158"}], "args": [], "attrs": {}, "rs": {"__ref": "36693399"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693304": {"variants": [{"__ref": "36693212"}], "args": [], "attrs": {}, "rs": {"__ref": "36693400"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693305": {"variants": [{"__ref": "36693214"}], "args": [], "attrs": {}, "rs": {"__ref": "36693401"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693306": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "left": "auto", "top": "auto", "margin-left": "8px", "plasmic-display-none": "true"}, "mixins": [], "__type": "RuleSet"}, "36693307": {"code": "true", "fallback": null, "__type": "CustomCode"}, "36693308": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "36693309": {"code": "true", "fallback": null, "__type": "CustomCode"}, "36693310": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693358": {"uuid": "70_KElMdbW1", "width": 340, "height": 340, "container": {"__ref": "36693412"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "36693158"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "36693359": {"uuid": "qCu9HDlaXoS", "width": 340, "height": 340, "container": {"__ref": "36693413"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "36693160"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "36693360": {"uuid": "YcaUS8n8f9F", "width": 340, "height": 340, "container": {"__ref": "36693414"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "36693161"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "36693361": {"uuid": "_cFcA0B8smF", "width": 340, "height": 340, "container": {"__ref": "36693415"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "36693162"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "36693362": {"uuid": "QJDKZaUml7w", "width": 340, "height": 340, "container": {"__ref": "36693416"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "36693211"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "36693363": {"uuid": "SpvgcuznYPp", "width": 340, "height": 340, "container": {"__ref": "36693417"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "36693212"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "36693364": {"uuid": "GtY5xqxKRxq", "width": 340, "height": 340, "container": {"__ref": "36693418"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "36693213"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "36693365": {"uuid": "oXduhT_vJ44", "width": 340, "height": 340, "container": {"__ref": "36693419"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "36693214"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "36693368": {"variants": [{"__ref": "36693158"}], "args": [], "attrs": {"outerHTML": {"__ref": "36693422"}}, "rs": {"__ref": "36693423"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693369": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693370": {"values": {"color": "#90908C"}, "mixins": [], "__type": "RuleSet"}, "36693371": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "36693398": {"variants": [{"__ref": "36693158"}], "args": [], "attrs": {"outerHTML": {"__ref": "36693426"}}, "rs": {"__ref": "36693427"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693399": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693400": {"values": {"color": "#90908C"}, "mixins": [], "__type": "RuleSet"}, "36693401": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "36693412": {"name": null, "component": {"__ref": "36693134"}, "uuid": "IpOOxfiI5F2", "parent": null, "locked": null, "vsettings": [{"__ref": "36693430"}], "__type": "TplComponent"}, "36693413": {"name": null, "component": {"__ref": "36693134"}, "uuid": "j0WxAwdHiCe", "parent": null, "locked": null, "vsettings": [{"__ref": "36693431"}], "__type": "TplComponent"}, "36693414": {"name": null, "component": {"__ref": "36693134"}, "uuid": "bjs200X<PERSON><PERSON>ie", "parent": null, "locked": null, "vsettings": [{"__ref": "36693432"}], "__type": "TplComponent"}, "36693415": {"name": null, "component": {"__ref": "36693134"}, "uuid": "s7GhSPo1LMw", "parent": null, "locked": null, "vsettings": [{"__ref": "36693433"}], "__type": "TplComponent"}, "36693416": {"name": null, "component": {"__ref": "36693134"}, "uuid": "gcFKvPK4ggY", "parent": null, "locked": null, "vsettings": [{"__ref": "36693434"}], "__type": "TplComponent"}, "36693417": {"name": null, "component": {"__ref": "36693134"}, "uuid": "KqucTBOhbb0", "parent": null, "locked": null, "vsettings": [{"__ref": "36693435"}], "__type": "TplComponent"}, "36693418": {"name": null, "component": {"__ref": "36693134"}, "uuid": "SxcomZU4Sz_", "parent": null, "locked": null, "vsettings": [{"__ref": "36693436"}], "__type": "TplComponent"}, "36693419": {"name": null, "component": {"__ref": "36693134"}, "uuid": "Daq9JkqxAPB", "parent": null, "locked": null, "vsettings": [{"__ref": "36693437"}], "__type": "TplComponent"}, "36693422": {"asset": {"__ref": "36693132"}, "__type": "ImageAssetRef"}, "36693423": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "36693426": {"asset": {"__ref": "36693133"}, "__type": "ImageAssetRef"}, "36693427": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "36693430": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "36693450"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693431": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "36693451"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693432": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "36693452"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693433": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "36693453"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693434": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "36693454"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693435": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "36693455"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693436": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "36693456"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693437": {"variants": [{"__ref": "33859007"}], "args": [], "attrs": {}, "rs": {"__ref": "36693457"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "36693450": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693451": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693452": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693453": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693454": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693455": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693456": {"values": {}, "mixins": [], "__type": "RuleSet"}, "36693457": {"values": {}, "mixins": [], "__type": "RuleSet"}, "43395001": {"name": "eventRef", "expr": {"__ref": "46885052"}, "__type": "NameArg"}, "43395002": {"name": "args", "expr": {"__ref": "43395003"}, "__type": "NameArg"}, "43395003": {"exprs": [{"__ref": "43395004"}], "__type": "CollectionExpr"}, "43395004": {"uuid": "YrPsRCq4ax", "argType": {"__ref": "46885004"}, "expr": {"__ref": "43395005"}, "__type": "FunctionArg"}, "43395005": {"code": "($state.person)", "fallback": null, "__type": "CustomCode"}, "43395017": {"interactionName": "Set person ▸ firstName", "actionName": "updateVariable", "args": [{"__ref": "43395025"}, {"__ref": "43395026"}, {"__ref": "43395027"}], "condExpr": null, "conditionalMode": "always", "uuid": "zKsXEV1Bt", "parent": {"__ref": "46885045"}, "__type": "Interaction"}, "43395021": {"code": "0", "fallback": null, "__type": "CustomCode"}, "43395024": {"path": ["$state", "person", "firstName"], "fallback": null, "__type": "ObjectPath"}, "43395025": {"name": "variable", "expr": {"__ref": "43395024"}, "__type": "NameArg"}, "43395026": {"name": "operation", "expr": {"__ref": "43395021"}, "__type": "NameArg"}, "43395027": {"name": "value", "expr": {"__ref": "43395028"}, "__type": "NameArg"}, "43395028": {"path": ["$state", "firstName", "value"], "fallback": null, "__type": "ObjectPath"}, "45282001": {"interactionName": "Custom function", "actionName": "customFunction", "args": [{"__ref": "17043001"}], "condExpr": null, "conditionalMode": "always", "uuid": "5PKva0Fyh", "parent": {"__ref": "46885020"}, "__type": "Interaction"}, "46885001": {"type": {"__ref": "46885003"}, "variable": {"__ref": "46885002"}, "uuid": "G3SB_pNvJX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "46885002": {"name": "onPersonChange", "uuid": "sNpl87CtZ", "__type": "Var"}, "46885003": {"name": "func", "params": [{"__ref": "46885004"}], "__type": "FunctionType"}, "46885004": {"name": "arg", "argName": "person", "displayName": null, "type": {"__ref": "46885005"}, "__type": "ArgType"}, "46885005": {"name": "any", "__type": "AnyType"}, "46885006": {"type": {"__ref": "46885008"}, "variable": {"__ref": "46885007"}, "uuid": "dM_JyDrsD1", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "17043020"}, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "46885007": {"name": "initialPerson", "uuid": "GNdP1HsX8", "__type": "Var"}, "46885008": {"name": "any", "__type": "AnyType"}, "46885014": {"param": {"__ref": "46885006"}, "expr": {"__ref": "46885017"}, "__type": "Arg"}, "46885017": {"path": ["currentItem"], "fallback": {"__ref": "46885018"}, "__type": "ObjectPath"}, "46885018": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "46885019": {"param": {"__ref": "46885001"}, "expr": {"__ref": "46885020"}, "__type": "Arg"}, "46885020": {"interactions": [{"__ref": "45282001"}], "__type": "EventHandler"}, "46885033": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "RM00wbjut", "parent": {"__ref": "36693051"}, "locked": null, "vsettings": [{"__ref": "46885034"}], "__type": "TplTag"}, "46885034": {"variants": [{"__ref": "36693052"}], "args": [], "attrs": {"data-testid": {"__ref": "6345029"}}, "rs": {"__ref": "46885035"}, "dataCond": null, "dataRep": null, "text": {"__ref": "6345026"}, "columnsConfig": null, "__type": "VariantSetting"}, "46885035": {"values": {"position": "relative", "text-align": "center"}, "mixins": [], "__type": "RuleSet"}, "46885044": {"param": {"__ref": "16883003"}, "expr": {"__ref": "46885045"}, "__type": "Arg"}, "46885045": {"interactions": [{"__ref": "43395017"}, {"__ref": "46885046"}], "__type": "EventHandler"}, "46885046": {"interactionName": "Invoke on<PERSON><PERSON><PERSON><PERSON><PERSON>", "actionName": "invokeEventHandler", "args": [{"__ref": "43395001"}, {"__ref": "43395002"}], "condExpr": null, "conditionalMode": "always", "uuid": "stmVTJ940", "parent": {"__ref": "46885045"}, "__type": "Interaction"}, "46885052": {"variable": {"__ref": "46885002"}, "__type": "VarRef"}, "50599007": {"name": null, "component": {"__ref": "3378088"}, "uuid": "lBKUfvanR", "parent": {"__ref": "36693051"}, "locked": null, "vsettings": [{"__ref": "50599008"}], "__type": "TplComponent"}, "50599008": {"variants": [{"__ref": "36693052"}], "args": [{"__ref": "50599009"}, {"__ref": "50599010"}, {"__ref": "50599011"}, {"__ref": "50599038"}, {"__ref": "60137006"}, {"__ref": "2001410"}], "attrs": {"onClick": {"__ref": "28678001"}}, "rs": {"__ref": "50599012"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "50599009": {"param": {"__ref": "3378094"}, "expr": {"__ref": "50599013"}, "__type": "Arg"}, "50599010": {"param": {"__ref": "3378091"}, "expr": {"__ref": "50599041"}, "__type": "Arg"}, "50599011": {"param": {"__ref": "3378095"}, "expr": {"__ref": "50599015"}, "__type": "Arg"}, "50599012": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "50599013": {"tpl": [{"__ref": "50599018"}], "__type": "VirtualRenderExpr"}, "50599015": {"tpl": [{"__ref": "50599020"}], "__type": "VirtualRenderExpr"}, "50599018": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "9pZM-v6V6F", "parent": {"__ref": "50599007"}, "locked": null, "vsettings": [{"__ref": "50599021"}], "__type": "TplTag"}, "50599019": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "2P7bV-A_Wq", "parent": {"__ref": "50599007"}, "locked": null, "vsettings": [{"__ref": "50599022"}], "__type": "TplTag"}, "50599020": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "VxQLWttOIW", "parent": {"__ref": "50599007"}, "locked": null, "vsettings": [{"__ref": "50599023"}], "__type": "TplTag"}, "50599021": {"variants": [{"__ref": "36693052"}], "args": [], "attrs": {"outerHTML": {"__ref": "50599024"}}, "rs": {"__ref": "50599025"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "50599022": {"variants": [{"__ref": "36693052"}], "args": [], "attrs": {}, "rs": {"__ref": "50599026"}, "dataCond": null, "dataRep": null, "text": {"__ref": "50599040"}, "columnsConfig": null, "__type": "VariantSetting"}, "50599023": {"variants": [{"__ref": "36693052"}], "args": [], "attrs": {"outerHTML": {"__ref": "50599028"}}, "rs": {"__ref": "50599029"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "50599024": {"asset": {"__ref": "36693133"}, "__type": "ImageAssetRef"}, "50599025": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "50599026": {"values": {}, "mixins": [], "__type": "RuleSet"}, "50599028": {"asset": {"__ref": "3378087"}, "__type": "ImageAssetRef"}, "50599029": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "50599038": {"param": {"__ref": "3378098"}, "expr": {"__ref": "50599039"}, "__type": "Arg"}, "50599039": {"variants": [{"__ref": "3378214"}], "__type": "VariantsRef"}, "50599040": {"markers": [], "text": "Add", "__type": "RawText"}, "50599041": {"tpl": [{"__ref": "50599019"}], "__type": "RenderExpr"}, "60137001": {"code": "[{\"firstName\":\"<PERSON>\",\"lastName\":\"<PERSON><PERSON><PERSON>\",\"nicknames\":[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\"]},{\"firstName\":\"<PERSON>\",\"lastName\":\"<PERSON><PERSON><PERSON>\",\"nicknames\":[]},{\"firstName\":\"<PERSON><PERSON><PERSON>\",\"lastName\":\"<PERSON>\",\"nicknames\":[\"<PERSON><PERSON>\"]}]", "fallback": null, "__type": "CustomCode"}, "60137002": {"type": {"__ref": "60137005"}, "variable": {"__ref": "60137004"}, "uuid": "5buBqAR3IZ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "60137003": {"variable": {"__ref": "60137004"}, "__type": "VarRef"}, "60137004": {"name": "data-testid", "uuid": "66LT6BONb", "__type": "Var"}, "60137005": {"name": "text", "__type": "Text"}, "60137006": {"param": {"__ref": "60137002"}, "expr": {"__ref": "60137041"}, "__type": "Arg"}, "60137008": {"param": {"__ref": "60137002"}, "expr": {"__ref": "60137014"}, "__type": "Arg"}, "60137010": {"param": {"__ref": "60137002"}, "expr": {"__ref": "60137011"}, "__type": "Arg"}, "60137011": {"text": ["remove-nickname"], "__type": "TemplatedString"}, "60137012": {"param": {"__ref": "60137002"}, "expr": {"__ref": "60137013"}, "__type": "Arg"}, "60137013": {"text": ["remove-person"], "__type": "TemplatedString"}, "60137014": {"text": ["add-nickname"], "__type": "TemplatedString"}, "60137015": {"param": {"__ref": "6345031"}, "expr": {"__ref": "60137016"}, "__type": "Arg"}, "60137016": {"interactions": [{"__ref": "60137017"}, {"__ref": "60137029"}], "__type": "EventHandler"}, "60137017": {"interactionName": "Set person ▸ nicknames", "actionName": "updateVariable", "args": [{"__ref": "60137025"}, {"__ref": "60137026"}, {"__ref": "60137027"}], "condExpr": null, "conditionalMode": "always", "uuid": "8MGJgHKtZ", "parent": {"__ref": "60137016"}, "__type": "Interaction"}, "60137021": {"code": "0", "fallback": null, "__type": "CustomCode"}, "60137024": {"path": ["$state", "person", "nicknames"], "fallback": null, "__type": "ObjectPath"}, "60137025": {"name": "variable", "expr": {"__ref": "60137024"}, "__type": "NameArg"}, "60137026": {"name": "operation", "expr": {"__ref": "60137021"}, "__type": "NameArg"}, "60137027": {"name": "value", "expr": {"__ref": "60137028"}, "__type": "NameArg"}, "60137028": {"path": ["nicknames"], "fallback": null, "__type": "ObjectPath"}, "60137029": {"interactionName": "Invoke on<PERSON><PERSON><PERSON><PERSON><PERSON>", "actionName": "invokeEventHandler", "args": [{"__ref": "60137036"}, {"__ref": "60137037"}], "condExpr": null, "conditionalMode": "always", "uuid": "EZE5QOYUI", "parent": {"__ref": "60137016"}, "__type": "Interaction"}, "60137035": {"variable": {"__ref": "46885002"}, "__type": "VarRef"}, "60137036": {"name": "eventRef", "expr": {"__ref": "60137035"}, "__type": "NameArg"}, "60137037": {"name": "args", "expr": {"__ref": "60137038"}, "__type": "NameArg"}, "60137038": {"exprs": [{"__ref": "60137039"}], "__type": "CollectionExpr"}, "60137039": {"uuid": "FeMpUNRXA", "argType": {"__ref": "46885004"}, "expr": {"__ref": "60137040"}, "__type": "FunctionArg"}, "60137040": {"path": ["$state", "person"], "fallback": null, "__type": "ObjectPath"}, "60137041": {"text": ["add-person"], "__type": "TemplatedString"}, "64439001": {"name": "TextInput", "component": {"__ref": "36693134"}, "uuid": "6b6eElhvP", "parent": {"__ref": "36693108"}, "locked": null, "vsettings": [{"__ref": "64439004"}], "__type": "TplComponent"}, "64439002": {"param": {"__ref": "64439003"}, "accessType": "writable", "variableType": "text", "onChangeParam": {"__ref": "16883003"}, "tplNode": {"__ref": "64439001"}, "implicitState": {"__ref": "36693156"}, "__type": "State"}, "64439003": {"type": {"__ref": "64439006"}, "state": {"__ref": "64439002"}, "variable": {"__ref": "64439005"}, "uuid": "XamYBxKRwB", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "64439004": {"variants": [{"__ref": "36693109"}], "args": [{"__ref": "64439007"}, {"__ref": "64439008"}, {"__ref": "64439009"}, {"__ref": "6345012"}], "attrs": {}, "rs": {"__ref": "64439010"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "64439005": {"name": "value", "uuid": "r1HKjVmdF-", "__type": "Var"}, "64439006": {"name": "text", "__type": "Text"}, "64439007": {"param": {"__ref": "36693146"}, "expr": {"__ref": "64439011"}, "__type": "Arg"}, "64439008": {"param": {"__ref": "36693141"}, "expr": {"__ref": "64439012"}, "__type": "Arg"}, "64439009": {"param": {"__ref": "36693140"}, "expr": {"__ref": "64439013"}, "__type": "Arg"}, "64439010": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "64439011": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "64439012": {"tpl": [{"__ref": "64439016"}], "__type": "VirtualRenderExpr"}, "64439013": {"tpl": [{"__ref": "64439017"}], "__type": "VirtualRenderExpr"}, "64439016": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "0rILQ9Uxtg", "parent": {"__ref": "64439001"}, "locked": null, "vsettings": [{"__ref": "64439018"}], "__type": "TplTag"}, "64439017": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "1KJaPciJ65", "parent": {"__ref": "64439001"}, "locked": null, "vsettings": [{"__ref": "64439019"}], "__type": "TplTag"}, "64439018": {"variants": [{"__ref": "36693109"}], "args": [], "attrs": {"outerHTML": {"__ref": "64439020"}}, "rs": {"__ref": "64439021"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "64439019": {"variants": [{"__ref": "36693109"}], "args": [], "attrs": {"outerHTML": {"__ref": "64439022"}}, "rs": {"__ref": "64439023"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "64439020": {"asset": {"__ref": "36693132"}, "__type": "ImageAssetRef"}, "64439021": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "64439022": {"asset": {"__ref": "36693133"}, "__type": "ImageAssetRef"}, "64439023": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "64439090": {"name": "FirstName", "component": {"__ref": "36693106"}, "uuid": "vB8ErW_Tk", "parent": {"__ref": "36693075"}, "locked": null, "vsettings": [{"__ref": "64439095"}], "__type": "TplComponent"}, "64439095": {"variants": [{"__ref": "36693076"}], "args": [{"__ref": "3378004"}, {"__ref": "36353001"}, {"__ref": "46885044"}, {"__ref": "6345016"}], "attrs": {}, "rs": {"__ref": "64439096"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "64439096": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "64439099": {"type": {"__ref": "64439100"}, "state": {"__ref": "3378032"}, "variable": {"__ref": "64439103"}, "uuid": "TX_iveImWKM", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64439100": {"name": "func", "params": [{"__ref": "64439101"}], "__type": "FunctionType"}, "64439101": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64439102"}, "__type": "ArgType"}, "64439102": {"name": "text", "__type": "Text"}, "64439103": {"name": "On FormField value change", "uuid": "BPDeyJKg1he", "__type": "Var"}, "64439104": {"type": {"__ref": "64439105"}, "state": {"__ref": "3378101"}, "variable": {"__ref": "64439108"}, "uuid": "SiWktRnTNrh", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64439105": {"name": "func", "params": [{"__ref": "64439106"}], "__type": "FunctionType"}, "64439106": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64439107"}, "__type": "ArgType"}, "64439107": {"name": "any", "__type": "AnyType"}, "64439108": {"name": "On Show Start Icon change", "uuid": "wKKzQx5xZNE", "__type": "Var"}, "64439109": {"type": {"__ref": "64439110"}, "state": {"__ref": "3378102"}, "variable": {"__ref": "64439113"}, "uuid": "rDnyB4Ry9Uo", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64439110": {"name": "func", "params": [{"__ref": "64439111"}], "__type": "FunctionType"}, "64439111": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64439112"}, "__type": "ArgType"}, "64439112": {"name": "any", "__type": "AnyType"}, "64439113": {"name": "On Show End Icon change", "uuid": "cJvIrxQfDhg", "__type": "Var"}, "64439114": {"type": {"__ref": "64439115"}, "state": {"__ref": "3378103"}, "variable": {"__ref": "64439118"}, "uuid": "Bq2L_LlDgnI", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64439115": {"name": "func", "params": [{"__ref": "64439116"}], "__type": "FunctionType"}, "64439116": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64439117"}, "__type": "ArgType"}, "64439117": {"name": "any", "__type": "AnyType"}, "64439118": {"name": "On Is Disabled change", "uuid": "vwRosnBT_jm", "__type": "Var"}, "64439119": {"type": {"__ref": "64439120"}, "state": {"__ref": "3378104"}, "variable": {"__ref": "64439123"}, "uuid": "t5TLxRuaAHF", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64439120": {"name": "func", "params": [{"__ref": "64439121"}], "__type": "FunctionType"}, "64439121": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64439122"}, "__type": "ArgType"}, "64439122": {"name": "any", "__type": "AnyType"}, "64439123": {"name": "On Shape change", "uuid": "zJ42-4k-aF-", "__type": "Var"}, "64439124": {"type": {"__ref": "64439125"}, "state": {"__ref": "3378105"}, "variable": {"__ref": "64439128"}, "uuid": "d9Jj0jiPKtY", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64439125": {"name": "func", "params": [{"__ref": "64439126"}], "__type": "FunctionType"}, "64439126": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64439127"}, "__type": "ArgType"}, "64439127": {"name": "any", "__type": "AnyType"}, "64439128": {"name": "On Size change", "uuid": "_ZVUTxOWJ-K", "__type": "Var"}, "64439129": {"type": {"__ref": "64439130"}, "state": {"__ref": "3378106"}, "variable": {"__ref": "64439133"}, "uuid": "jNBZ4Ce8oW0", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64439130": {"name": "func", "params": [{"__ref": "64439131"}], "__type": "FunctionType"}, "64439131": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64439132"}, "__type": "ArgType"}, "64439132": {"name": "any", "__type": "AnyType"}, "64439133": {"name": "On Color change", "uuid": "2wc21Mu6FUi", "__type": "Var"}, "64439134": {"type": {"__ref": "64439135"}, "state": {"__ref": "3378922"}, "variable": {"__ref": "64439138"}, "uuid": "4ILAHtDDom-", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64439135": {"name": "func", "params": [{"__ref": "64439136"}], "__type": "FunctionType"}, "64439136": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64439137"}, "__type": "ArgType"}, "64439137": {"name": "text", "__type": "Text"}, "64439138": {"name": "On undefined value change", "uuid": "uBBFSSAZio7", "__type": "Var"}, "64439139": {"type": {"__ref": "64439140"}, "state": {"__ref": "3379081"}, "variable": {"__ref": "64439143"}, "uuid": "x_ui7nzX2v3", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64439140": {"name": "func", "params": [{"__ref": "64439141"}], "__type": "FunctionType"}, "64439141": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64439142"}, "__type": "ArgType"}, "64439142": {"name": "any", "__type": "AnyType"}, "64439143": {"name": "On people change", "uuid": "Nv2ju0VQ8Wc", "__type": "Var"}, "64439144": {"type": {"__ref": "64439145"}, "state": {"__ref": "3379354"}, "variable": {"__ref": "64439148"}, "uuid": "0bNOzBC8ICA", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64439145": {"name": "func", "params": [{"__ref": "64439146"}], "__type": "FunctionType"}, "64439146": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64439147"}, "__type": "ArgType"}, "64439147": {"name": "any", "__type": "AnyType"}, "64439148": {"name": "On person change", "uuid": "RAswSvJC8Wa", "__type": "Var"}, "64439149": {"type": {"__ref": "64439150"}, "state": {"__ref": "3379392"}, "variable": {"__ref": "64439153"}, "uuid": "78SHS5-SHsX", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64439150": {"name": "func", "params": [{"__ref": "64439151"}], "__type": "FunctionType"}, "64439151": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64439152"}, "__type": "ArgType"}, "64439152": {"name": "any", "__type": "AnyType"}, "64439153": {"name": "On nicknames change", "uuid": "JddJik7gMNd", "__type": "Var"}, "64439154": {"type": {"__ref": "64439155"}, "state": {"__ref": "16883001"}, "variable": {"__ref": "64439158"}, "uuid": "YCWe9B4t2z9", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64439155": {"name": "func", "params": [{"__ref": "64439156"}], "__type": "FunctionType"}, "64439156": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64439157"}, "__type": "ArgType"}, "64439157": {"name": "text", "__type": "Text"}, "64439158": {"name": "On FormField undefined value change", "uuid": "MmVaR43EoUE", "__type": "Var"}, "64439159": {"type": {"__ref": "64439160"}, "state": {"__ref": "36693152"}, "variable": {"__ref": "64439163"}, "uuid": "Wai4MtUyzeg", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64439160": {"name": "func", "params": [{"__ref": "64439161"}], "__type": "FunctionType"}, "64439161": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64439162"}, "__type": "ArgType"}, "64439162": {"name": "any", "__type": "AnyType"}, "64439163": {"name": "On Show Start Icon change", "uuid": "KjNROd57gYi", "__type": "Var"}, "64439164": {"type": {"__ref": "64439165"}, "state": {"__ref": "36693153"}, "variable": {"__ref": "64439168"}, "uuid": "4mKl9s9-STK", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64439165": {"name": "func", "params": [{"__ref": "64439166"}], "__type": "FunctionType"}, "64439166": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64439167"}, "__type": "ArgType"}, "64439167": {"name": "any", "__type": "AnyType"}, "64439168": {"name": "On Show End Icon change", "uuid": "iBzVtBHf4a-", "__type": "Var"}, "64439169": {"type": {"__ref": "64439170"}, "state": {"__ref": "36693154"}, "variable": {"__ref": "64439173"}, "uuid": "tDGnJIwgC-Y", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64439170": {"name": "func", "params": [{"__ref": "64439171"}], "__type": "FunctionType"}, "64439171": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64439172"}, "__type": "ArgType"}, "64439172": {"name": "any", "__type": "AnyType"}, "64439173": {"name": "On Is Disabled change", "uuid": "2VbDI-h-Pl_", "__type": "Var"}, "64439174": {"type": {"__ref": "64439175"}, "state": {"__ref": "36693155"}, "variable": {"__ref": "64439178"}, "uuid": "IURXeC3Vd2v", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64439175": {"name": "func", "params": [{"__ref": "64439176"}], "__type": "FunctionType"}, "64439176": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64439177"}, "__type": "ArgType"}, "64439177": {"name": "any", "__type": "AnyType"}, "64439178": {"name": "On Color change", "uuid": "oGq3Y6nqivh", "__type": "Var"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}]]