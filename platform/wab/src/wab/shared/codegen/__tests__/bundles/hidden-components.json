[["rnLKMrCmHMGtwgFRMx1dUo", {"root": "62885001", "map": {"3691501": {"rows": [{"__ref": "3691502"}], "__type": "ArenaFrameGrid"}, "3691502": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "14363001": {"uuid": "g_kAJdKAcN", "name": "hostless-plasmic-head", "params": [{"__ref": "14363003"}, {"__ref": "14363004"}, {"__ref": "14363005"}, {"__ref": "14363006"}], "states": [], "tplTree": {"__ref": "14363007"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "14363008"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "14363009"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "14363002": {"uuid": "sFp5XzlGf-", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "14363010"}, {"__ref": "14363011"}, {"__ref": "14363012"}, {"__ref": "14363013"}, {"__ref": "14363014"}], "states": [], "tplTree": {"__ref": "14363015"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "14363016"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "14363017"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component"}, "14363003": {"type": {"__ref": "14363019"}, "variable": {"__ref": "14363018"}, "uuid": "950Z_ur5Lk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14363004": {"type": {"__ref": "14363021"}, "variable": {"__ref": "14363020"}, "uuid": "BohKJs6_fT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14363005": {"type": {"__ref": "14363023"}, "variable": {"__ref": "14363022"}, "uuid": "Jr4XEl1q4y", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14363006": {"type": {"__ref": "14363025"}, "variable": {"__ref": "14363024"}, "uuid": "CqrWUFW243", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14363007": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "2qPJxfusu4", "parent": null, "locked": null, "vsettings": [{"__ref": "14363026"}], "__type": "TplTag"}, "14363008": {"uuid": "2aL1a7vQo", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363009": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "14363010": {"type": {"__ref": "14363028"}, "variable": {"__ref": "14363027"}, "uuid": "ZWqKK8Ogu7", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14363011": {"type": {"__ref": "14363030"}, "variable": {"__ref": "14363029"}, "uuid": "zl30J74PQPM", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14363012": {"type": {"__ref": "14363032"}, "tplSlot": {"__ref": "14363037"}, "variable": {"__ref": "14363031"}, "uuid": "p5FVs5Gsukd", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "14363013": {"type": {"__ref": "14363034"}, "variable": {"__ref": "14363033"}, "uuid": "3ZTdKL9TCdP", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14363014": {"type": {"__ref": "14363036"}, "variable": {"__ref": "14363035"}, "uuid": "5h0oEbI4coe", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14363015": {"tag": "div", "name": null, "children": [{"__ref": "14363037"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "vRYK6dpo7g", "parent": null, "locked": null, "vsettings": [{"__ref": "14363038"}], "__type": "TplTag"}, "14363016": {"uuid": "MuWeo6EUKH", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363017": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "14363018": {"name": "title", "uuid": "B20rzmZ_36", "__type": "Var"}, "14363019": {"name": "text", "__type": "Text"}, "14363020": {"name": "description", "uuid": "7aNYtFdCcV", "__type": "Var"}, "14363021": {"name": "text", "__type": "Text"}, "14363022": {"name": "image", "uuid": "RsNgFtyxJy", "__type": "Var"}, "14363023": {"name": "img", "__type": "Img"}, "14363024": {"name": "canonical", "uuid": "SmwOmk6lEl", "__type": "Var"}, "14363025": {"name": "text", "__type": "Text"}, "14363026": {"variants": [{"__ref": "14363008"}], "args": [], "attrs": {}, "rs": {"__ref": "14363039"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363027": {"name": "dataOp", "uuid": "wrdCC_EPK-", "__type": "Var"}, "14363028": {"name": "any", "__type": "AnyType"}, "14363029": {"name": "name", "uuid": "WDte5ZupDsQ", "__type": "Var"}, "14363030": {"name": "text", "__type": "Text"}, "14363031": {"name": "children", "uuid": "qdRu96qdNf7", "__type": "Var"}, "14363032": {"name": "renderFunc", "params": [{"__ref": "14363040"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "14363033": {"name": "pageSize", "uuid": "h-kVdoe4Eaf", "__type": "Var"}, "14363034": {"name": "num", "__type": "<PERSON><PERSON>"}, "14363035": {"name": "pageIndex", "uuid": "yq3U8MgnK8W", "__type": "Var"}, "14363036": {"name": "num", "__type": "<PERSON><PERSON>"}, "14363037": {"param": {"__ref": "14363012"}, "defaultContents": [], "uuid": "Q2SI3PiZsJa", "parent": {"__ref": "14363015"}, "locked": null, "vsettings": [{"__ref": "14363041"}], "__type": "TplSlot"}, "14363038": {"variants": [{"__ref": "14363016"}], "args": [], "attrs": {}, "rs": {"__ref": "14363042"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363039": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "14363040": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "14363045"}, "__type": "ArgType"}, "14363041": {"variants": [{"__ref": "14363016"}], "args": [], "attrs": {}, "rs": {"__ref": "14363046"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363042": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "14363045": {"name": "any", "__type": "AnyType"}, "14363046": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363049": {"uuid": "iFqCt8OVI9", "name": "_Card", "params": [{"__ref": "14364338"}, {"__ref": "XfLVmOI9u1V1"}], "states": [], "tplTree": {"__ref": "14363051"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "14363052"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "14363050": {"component": {"__ref": "14363049"}, "matrix": {"__ref": "14363053"}, "customMatrix": {"__ref": "14363054"}, "__type": "ComponentArena"}, "14363051": {"tag": "div", "name": null, "children": [{"__ref": "-Lt<PERSON><PERSON>pe_Wy9"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "T-ytTXQ42", "parent": null, "locked": null, "vsettings": [{"__ref": "14363055"}], "__type": "TplTag"}, "14363052": {"uuid": "3-xWChNvZ_", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363053": {"rows": [{"__ref": "14363056"}], "__type": "ArenaFrameGrid"}, "14363054": {"rows": [{"__ref": "14363057"}], "__type": "ArenaFrameGrid"}, "14363055": {"variants": [{"__ref": "14363052"}], "args": [], "attrs": {"data-testid": {"__ref": "lLqFxdV1xwlX"}}, "rs": {"__ref": "14363058"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363056": {"cols": [{"__ref": "14363059"}], "rowKey": null, "__type": "ArenaFrameRow"}, "14363057": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "14363058": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "wrap", "height": "wrap", "justify-content": "flex-start", "align-items": "stretch", "flex-row-gap": "8px"}, "mixins": [], "__type": "RuleSet"}, "14363059": {"frame": {"__ref": "14363067"}, "cellKey": {"__ref": "14363052"}, "__type": "ArenaFrameCell"}, "14363067": {"uuid": "GvnV4Clmvu", "width": 613, "height": 340, "container": {"__ref": "14363068"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363052"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363068": {"name": null, "component": {"__ref": "14363049"}, "uuid": "aOxL_VqrLX", "parent": null, "locked": null, "vsettings": [{"__ref": "14363069"}], "__type": "TplComponent"}, "14363069": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363070"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363070": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363087": {"uuid": "LZ8ods6C6lA", "name": "check.svg", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAyNCAyNCIgaGVpZ2h0PSIxZW0iIHdpZHRoPSIxZW0iIHN0eWxlPSJmaWxsOiBjdXJyZW50Y29sb3I7Ij4KICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTE4LjQxNiA1Ljg3NmEuNzUuNzUgMCAwMS4yMDggMS4wNEwxMS40MiAxNy43MjFhMS43NSAxLjc1IDAgMDEtMi44NzEuMDZsLTMuMTU2LTQuMzRhLjc1Ljc1IDAgMTExLjIxNC0uODgybDMuMTU1IDQuMzM5YS4yNS4yNSAwIDAwLjQxLS4wMDlsNy4yMDQtMTAuODA1YS43NS43NSAwIDAxMS4wNC0uMjA4eiIgZmlsbD0iY3VycmVudENvbG9yIi8+Cjwvc3ZnPg==", "width": 150, "height": 150, "aspectRatio": 1000000, "__type": "ImageAsset"}, "14363088": {"uuid": "HrhUyOqMxRB", "name": "icon", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHN0cm9rZT0iY3VycmVudENvbG9yIiBmaWxsPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMCIgdmlld0JveD0iMCAwIDE2IDE2IiBoZWlnaHQ9IjFlbSIgd2lkdGg9IjFlbSI+CiAgPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBkPSJNMSA4YS41LjUgMCAwMS41LS41aDExLjc5M2wtMy4xNDctMy4xNDZhLjUuNSAwIDAxLjcwOC0uNzA4bDQgNGEuNS41IDAgMDEwIC43MDhsLTQgNGEuNS41IDAgMDEtLjcwOC0uNzA4TDEzLjI5MyA4LjVIMS41QS41LjUgMCAwMTEgOHoiIHN0cm9rZT0ibm9uZSIvPgo8L3N2Zz4=", "width": 150, "height": 150, "aspectRatio": 1000000, "__type": "ImageAsset"}, "14364338": {"type": {"__ref": "2o0X1c2b3P6s"}, "variable": {"__ref": "14364339"}, "uuid": "DyFOtLqdID", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "CtD4fdLfYXxg"}, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14364339": {"name": "test name", "uuid": "0vB9UMXck", "__type": "Var"}, "62885001": {"components": [{"__ref": "62885002"}, {"__ref": "14363001"}, {"__ref": "14363002"}, {"__ref": "14363049"}, {"__ref": "4K6U0ALP7ugT"}], "arenas": [], "pageArenas": [{"__ref": "62885061"}, {"__ref": "kGtfOdrtj8mX"}], "componentArenas": [{"__ref": "14363050"}], "globalVariantGroups": [{"__ref": "62885055"}], "userManagedFonts": [], "globalVariant": {"__ref": "62885068"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "62885075"}], "activeTheme": {"__ref": "62885075"}, "imageAssets": [{"__ref": "14363087"}, {"__ref": "14363088"}], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "62885055"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "62885002": {"uuid": "BtG1cJsQymJd", "name": "Homepage", "params": [], "states": [], "tplTree": {"__ref": "62885003"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "62885007"}], "variantGroups": [], "pageMeta": {"__ref": "62885060"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "62885003": {"tag": "div", "name": null, "children": [{"__ref": "62885004"}, {"__ref": "xROL1uuYpXGp"}, {"__ref": "1Gt1E59CZYeE"}, {"__ref": "K-_DLw9GUu1c"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "y25OVXFLGLSD", "parent": null, "locked": null, "vsettings": [{"__ref": "62885040"}, {"__ref": "62885053"}], "__type": "TplTag"}, "62885004": {"tag": "section", "name": null, "children": [{"__ref": "62885005"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "1m0ZaovFbAjr", "parent": {"__ref": "62885003"}, "locked": null, "vsettings": [{"__ref": "62885026"}], "__type": "TplTag"}, "62885005": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "kpZWMz5kmzEW", "parent": {"__ref": "62885004"}, "locked": null, "vsettings": [{"__ref": "62885006"}], "__type": "TplTag"}, "62885006": {"variants": [{"__ref": "62885007"}], "args": [], "attrs": {}, "rs": {"__ref": "62885008"}, "dataCond": null, "dataRep": null, "text": {"__ref": "1mbV1Vmyo5t2"}, "columnsConfig": null, "__type": "VariantSetting"}, "62885007": {"uuid": "eXaNvHZOkpNm", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "62885008": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px", "margin-bottom": "32px", "text-align": "center"}, "mixins": [], "__type": "RuleSet"}, "62885026": {"variants": [{"__ref": "62885007"}], "args": [], "attrs": {}, "rs": {"__ref": "62885027"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "62885027": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "padding-left": "24px", "padding-right": "24px", "padding-bottom": "96px", "padding-top": "96px", "flex-row-gap": "16px"}, "mixins": [], "__type": "RuleSet"}, "62885040": {"variants": [{"__ref": "62885007"}], "args": [], "attrs": {}, "rs": {"__ref": "62885041"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "62885041": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "width": "stretch", "height": "stretch", "justify-content": "flex-start", "align-items": "center", "padding-top": "24px", "padding-right": "24px", "padding-bottom": "24px", "padding-left": "24px", "flex-row-gap": "24px"}, "mixins": [], "__type": "RuleSet"}, "62885053": {"variants": [{"__ref": "62885054"}], "args": [], "attrs": {}, "rs": {"__ref": "62885059"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "62885054": {"uuid": "Iw1PegnUDE7A_", "name": "Mobile only", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "62885055"}, "mediaQuery": "(min-width:0px) and (max-width:768px)", "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "62885055": {"type": "global-screen", "param": {"__ref": "62885056"}, "uuid": "-MvgymA1_MPux", "variants": [{"__ref": "62885054"}], "multi": true, "__type": "GlobalVariantGroup"}, "62885056": {"type": {"__ref": "62885058"}, "variable": {"__ref": "62885057"}, "uuid": "6w4Wg_v6xiFpq", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "62885057": {"name": "Screen", "uuid": "ZR6EMjVpcY1gq", "__type": "Var"}, "62885058": {"name": "text", "__type": "Text"}, "62885059": {"values": {}, "mixins": [], "__type": "RuleSet"}, "62885060": {"path": "/", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "62885061": {"component": {"__ref": "62885002"}, "matrix": {"__ref": "62885062"}, "customMatrix": {"__ref": "3691501"}, "__type": "PageArena"}, "62885062": {"rows": [{"__ref": "62885063"}], "__type": "ArenaFrameGrid"}, "62885063": {"cols": [{"__ref": "62885064"}, {"__ref": "62885070"}], "rowKey": {"__ref": "62885007"}, "__type": "ArenaFrameRow"}, "62885064": {"frame": {"__ref": "62885065"}, "cellKey": null, "__type": "ArenaFrameCell"}, "62885065": {"uuid": "hPzw2-RIAfQ9Q", "width": 1440, "height": 768, "container": {"__ref": "62885066"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "62885007"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "62885066": {"name": null, "component": {"__ref": "62885002"}, "uuid": "6Y9ix7r64Qek6", "parent": null, "locked": null, "vsettings": [{"__ref": "62885067"}], "__type": "TplComponent"}, "62885067": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "62885069"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "62885068": {"uuid": "14i4PWoFRuvRm", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "62885069": {"values": {}, "mixins": [], "__type": "RuleSet"}, "62885070": {"frame": {"__ref": "62885071"}, "cellKey": null, "__type": "ArenaFrameCell"}, "62885071": {"uuid": "H9ozxQRjzYdPN", "width": 414, "height": 736, "container": {"__ref": "62885072"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [], "pinnedGlobalVariants": {"Iw1PegnUDE7A_": true}, "targetGlobalVariants": [{"__ref": "62885054"}], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "62885072": {"name": null, "component": {"__ref": "62885002"}, "uuid": "YtlcBrGVXDVHI", "parent": null, "locked": null, "vsettings": [{"__ref": "62885073"}], "__type": "TplComponent"}, "62885073": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "62885074"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "62885074": {"values": {}, "mixins": [], "__type": "RuleSet"}, "62885075": {"defaultStyle": {"__ref": "62885076"}, "styles": [{"__ref": "62885091"}, {"__ref": "62885099"}, {"__ref": "62885107"}, {"__ref": "62885111"}, {"__ref": "62885119"}, {"__ref": "62885127"}, {"__ref": "62885152"}, {"__ref": "62885160"}, {"__ref": "62885185"}, {"__ref": "62885196"}, {"__ref": "62885207"}, {"__ref": "62885215"}, {"__ref": "62885222"}, {"__ref": "62885226"}, {"__ref": "62885229"}], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "62885076": {"name": "Default Typography", "rs": {"__ref": "62885077"}, "preview": null, "uuid": "WrBbwj7AG_dL", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885077": {"values": {"font-family": "Inter", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "62885091": {"selector": "h1", "style": {"__ref": "62885092"}, "__type": "ThemeStyle"}, "62885092": {"name": "Default \"h1\"", "rs": {"__ref": "62885093"}, "preview": null, "uuid": "SOrH2-piLBXf", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885093": {"values": {"color": "#000000", "font-weight": "900", "font-size": "72px", "line-height": "1", "letter-spacing": "-4px"}, "mixins": [], "__type": "RuleSet"}, "62885099": {"selector": "h2", "style": {"__ref": "62885100"}, "__type": "ThemeStyle"}, "62885100": {"name": "Default \"h2\"", "rs": {"__ref": "62885101"}, "preview": null, "uuid": "H7zKtFC98GbJ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885101": {"values": {"color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "62885107": {"selector": "a", "style": {"__ref": "62885108"}, "__type": "ThemeStyle"}, "62885108": {"name": "Default \"a\"", "rs": {"__ref": "62885109"}, "preview": null, "uuid": "EzvC6JLudAJt", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885109": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "62885111": {"selector": "h3", "style": {"__ref": "62885112"}, "__type": "ThemeStyle"}, "62885112": {"name": "Default \"h3\"", "rs": {"__ref": "62885113"}, "preview": null, "uuid": "qloEW3al7ShM", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885113": {"values": {"color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "62885119": {"selector": "h4", "style": {"__ref": "62885120"}, "__type": "ThemeStyle"}, "62885120": {"name": "Default \"h4\"", "rs": {"__ref": "62885121"}, "preview": null, "uuid": "PTgOftsOldbk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885121": {"values": {"color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "62885127": {"selector": "code", "style": {"__ref": "62885128"}, "__type": "ThemeStyle"}, "62885128": {"name": "Default \"code\"", "rs": {"__ref": "62885129"}, "preview": null, "uuid": "Lnva58L1xulY", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885129": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "62885152": {"selector": "blockquote", "style": {"__ref": "62885153"}, "__type": "ThemeStyle"}, "62885153": {"name": "Default \"blockquote\"", "rs": {"__ref": "62885154"}, "preview": null, "uuid": "ZYZaxTU8zo8XP", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885154": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "62885160": {"selector": "pre", "style": {"__ref": "62885161"}, "__type": "ThemeStyle"}, "62885161": {"name": "Default \"pre\"", "rs": {"__ref": "62885162"}, "preview": null, "uuid": "4EHyE6ul8K5zF", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885162": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "62885185": {"selector": "ul", "style": {"__ref": "62885186"}, "__type": "ThemeStyle"}, "62885186": {"name": "Default \"ul\"", "rs": {"__ref": "62885187"}, "preview": null, "uuid": "whHJCzEF4Exdx", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885187": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "62885196": {"selector": "ol", "style": {"__ref": "62885197"}, "__type": "ThemeStyle"}, "62885197": {"name": "Default \"ol\"", "rs": {"__ref": "62885198"}, "preview": null, "uuid": "muDhaFoygqglX", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885198": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "62885207": {"selector": "h5", "style": {"__ref": "62885208"}, "__type": "ThemeStyle"}, "62885208": {"name": "Default \"h5\"", "rs": {"__ref": "62885209"}, "preview": null, "uuid": "w3jaYB6vGoROj", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885209": {"values": {"color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "62885215": {"selector": "h6", "style": {"__ref": "62885216"}, "__type": "ThemeStyle"}, "62885216": {"name": "Default \"h6\"", "rs": {"__ref": "62885217"}, "preview": null, "uuid": "F_qpYVRvn54KX", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885217": {"values": {"color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "62885222": {"selector": "a:hover", "style": {"__ref": "62885223"}, "__type": "ThemeStyle"}, "62885223": {"name": "Default \"a:hover\"", "rs": {"__ref": "62885224"}, "preview": null, "uuid": "FhMazc7GkfuxX", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885224": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "62885226": {"selector": "li", "style": {"__ref": "62885227"}, "__type": "ThemeStyle"}, "62885227": {"name": "Default \"li\"", "rs": {"__ref": "62885228"}, "preview": null, "uuid": "HhZGTBvz3oXeH", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885228": {"values": {}, "mixins": [], "__type": "RuleSet"}, "62885229": {"selector": "p", "style": {"__ref": "62885230"}, "__type": "ThemeStyle"}, "62885230": {"name": "Default \"p\"", "rs": {"__ref": "62885231"}, "preview": null, "uuid": "u07KibhRJ5rmE", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885231": {"values": {}, "mixins": [], "__type": "RuleSet"}, "4K6U0ALP7ugT": {"uuid": "mHW07BKyuq9S", "name": "_Testpage", "params": [], "states": [], "tplTree": {"__ref": "PQhea8FdOh9z"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "rhrbeEltjl8G"}], "variantGroups": [], "pageMeta": {"__ref": "_NHBVXZ3o_Oi"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "kGtfOdrtj8mX": {"component": {"__ref": "4K6U0ALP7ugT"}, "matrix": {"__ref": "SrBF31vnlg78"}, "customMatrix": {"__ref": "BVF6DkeDxD-q"}, "__type": "PageArena"}, "PQhea8FdOh9z": {"tag": "div", "name": null, "children": [{"__ref": "bJazvDBpS1PF"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "QxDsEn-IdHHR", "parent": null, "locked": null, "vsettings": [{"__ref": "t6wf2IxGB-Z8"}, {"__ref": "Qg8Ata_gLTK2"}], "__type": "TplTag"}, "rhrbeEltjl8G": {"uuid": "uIcbL6-P<PERSON>jym", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "_NHBVXZ3o_Oi": {"path": "/test-page", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "SrBF31vnlg78": {"rows": [{"__ref": "8DlJlo5jjjKO"}], "__type": "ArenaFrameGrid"}, "BVF6DkeDxD-q": {"rows": [{"__ref": "C4uCaOeCJtjQ"}], "__type": "ArenaFrameGrid"}, "bJazvDBpS1PF": {"tag": "section", "name": null, "children": [{"__ref": "MYkoxtUY1oeS"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "V7pu4ciNPYkx", "parent": {"__ref": "PQhea8FdOh9z"}, "locked": null, "vsettings": [{"__ref": "rS8LzMHq9MhY"}], "__type": "TplTag"}, "t6wf2IxGB-Z8": {"variants": [{"__ref": "rhrbeEltjl8G"}], "args": [], "attrs": {}, "rs": {"__ref": "QwlrXj9bgNQ2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Qg8Ata_gLTK2": {"variants": [{"__ref": "62885054"}], "args": [], "attrs": {}, "rs": {"__ref": "vq57P4RNqCT8"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "8DlJlo5jjjKO": {"cols": [{"__ref": "U93znYoRRk_u"}, {"__ref": "M3e7h9fwGMQa"}], "rowKey": {"__ref": "rhrbeEltjl8G"}, "__type": "ArenaFrameRow"}, "C4uCaOeCJtjQ": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "MYkoxtUY1oeS": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "9HjpGVkqNF4t", "parent": {"__ref": "bJazvDBpS1PF"}, "locked": null, "vsettings": [{"__ref": "zvla8CF3NjZG"}], "__type": "TplTag"}, "rS8LzMHq9MhY": {"variants": [{"__ref": "rhrbeEltjl8G"}], "args": [], "attrs": {}, "rs": {"__ref": "lXkPEnqwADdn"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "QwlrXj9bgNQ2": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "width": "stretch", "height": "stretch", "justify-content": "flex-start", "align-items": "flex-start", "padding-top": "24px", "padding-right": "24px", "padding-bottom": "24px", "padding-left": "24px", "flex-row-gap": "24px"}, "mixins": [], "__type": "RuleSet"}, "vq57P4RNqCT8": {"values": {}, "mixins": [], "__type": "RuleSet"}, "U93znYoRRk_u": {"frame": {"__ref": "DeoQftZYWYBW"}, "cellKey": null, "__type": "ArenaFrameCell"}, "M3e7h9fwGMQa": {"frame": {"__ref": "fqj4YTzb4xOz"}, "cellKey": null, "__type": "ArenaFrameCell"}, "zvla8CF3NjZG": {"variants": [{"__ref": "rhrbeEltjl8G"}], "args": [], "attrs": {}, "rs": {"__ref": "96kAysLKpI6n"}, "dataCond": null, "dataRep": null, "text": {"__ref": "IZu2VRIW9aaW"}, "columnsConfig": null, "__type": "VariantSetting"}, "lXkPEnqwADdn": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "padding-left": "24px", "padding-right": "24px", "padding-bottom": "96px", "padding-top": "96px", "flex-row-gap": "16px"}, "mixins": [], "__type": "RuleSet"}, "DeoQftZYWYBW": {"uuid": "feb9FJu-HzK9", "width": 1440, "height": 768, "container": {"__ref": "9D3gz7Rawdsz"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "rhrbeEltjl8G"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "fqj4YTzb4xOz": {"uuid": "B5CbGpz17vnV", "width": 414, "height": 736, "container": {"__ref": "dCgrM8ZqFtUW"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "rhrbeEltjl8G"}], "pinnedGlobalVariants": {"Iw1PegnUDE7A_": true}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "96kAysLKpI6n": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px", "margin-bottom": "32px", "text-align": "center"}, "mixins": [], "__type": "RuleSet"}, "IZu2VRIW9aaW": {"markers": [], "text": "This is a hidden page", "__type": "RawText"}, "9D3gz7Rawdsz": {"name": null, "component": {"__ref": "4K6U0ALP7ugT"}, "uuid": "EVMEk3NNl8pe", "parent": null, "locked": null, "vsettings": [{"__ref": "P0LBVYPatfMn"}], "__type": "TplComponent"}, "dCgrM8ZqFtUW": {"name": null, "component": {"__ref": "4K6U0ALP7ugT"}, "uuid": "v-R8Rjt8tZVT", "parent": null, "locked": null, "vsettings": [{"__ref": "aHOOLGOO7J8q"}], "__type": "TplComponent"}, "P0LBVYPatfMn": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "ESfYGg1jo_5S"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "aHOOLGOO7J8q": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "Qx9cdaw6Dg-Q"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ESfYGg1jo_5S": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Qx9cdaw6Dg-Q": {"values": {}, "mixins": [], "__type": "RuleSet"}, "-LtQwlpe_Wy9": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "URaT0VgY76b2", "parent": {"__ref": "14363051"}, "locked": null, "vsettings": [{"__ref": "AEADCFEcuiwJ"}], "__type": "TplTag"}, "AEADCFEcuiwJ": {"variants": [{"__ref": "14363052"}], "args": [], "attrs": {}, "rs": {"__ref": "8lGG5-DxuYUC"}, "dataCond": null, "dataRep": null, "text": {"__ref": "spSEgdHgJRz6"}, "columnsConfig": null, "__type": "VariantSetting"}, "8lGG5-DxuYUC": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "text-align": "center"}, "mixins": [], "__type": "RuleSet"}, "1mbV1Vmyo5t2": {"markers": [], "text": "Homepage", "__type": "RawText"}, "xROL1uuYpXGp": {"name": "_Card", "component": {"__ref": "14363049"}, "uuid": "HSrehVx-WSR3", "parent": {"__ref": "62885003"}, "locked": null, "vsettings": [{"__ref": "sVBxkH8bHzQH"}], "__type": "TplComponent"}, "sVBxkH8bHzQH": {"variants": [{"__ref": "62885007"}], "args": [{"__ref": "icxADAwFS20j"}, {"__ref": "eyTwF0u6nc93"}], "attrs": {}, "rs": {"__ref": "Z7VAg6sGbaAx"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Z7VAg6sGbaAx": {"values": {"max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "1Gt1E59CZYeE": {"name": "_Card 2", "component": {"__ref": "14363049"}, "uuid": "I-dCG81e5mfJ", "parent": {"__ref": "62885003"}, "locked": null, "vsettings": [{"__ref": "AYmL5ubEv6Hw"}], "__type": "TplComponent"}, "K-_DLw9GUu1c": {"name": "_Card 3", "component": {"__ref": "14363049"}, "uuid": "PHK-8XSWdft7", "parent": {"__ref": "62885003"}, "locked": null, "vsettings": [{"__ref": "kPd5x89RttlI"}], "__type": "TplComponent"}, "AYmL5ubEv6Hw": {"variants": [{"__ref": "62885007"}], "args": [{"__ref": "GKUYNrgIEjwM"}, {"__ref": "ct5Jo61S9P8t"}], "attrs": {}, "rs": {"__ref": "3lSsGv_NMpFZ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kPd5x89RttlI": {"variants": [{"__ref": "62885007"}], "args": [{"__ref": "4ObMZw1SxXQW"}, {"__ref": "B9zYr3pbJggq"}], "attrs": {}, "rs": {"__ref": "EvvV6dl1SXmy"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3lSsGv_NMpFZ": {"values": {"max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "EvvV6dl1SXmy": {"values": {"max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "icxADAwFS20j": {"param": {"__ref": "14364338"}, "expr": {"__ref": "NRpCnaO_mVDM"}, "__type": "Arg"}, "NRpCnaO_mVDM": {"text": ["card-1"], "__type": "TemplatedString"}, "GKUYNrgIEjwM": {"param": {"__ref": "14364338"}, "expr": {"__ref": "ezpyIA-9h2Pr"}, "__type": "Arg"}, "ezpyIA-9h2Pr": {"text": ["card-2"], "__type": "TemplatedString"}, "4ObMZw1SxXQW": {"param": {"__ref": "14364338"}, "expr": {"__ref": "dr-17nsydOsX"}, "__type": "Arg"}, "dr-17nsydOsX": {"text": ["card-3"], "__type": "TemplatedString"}, "XfLVmOI9u1V1": {"type": {"__ref": "jlOzOu13taVE"}, "variable": {"__ref": "z8RVmgxd-SVY"}, "uuid": "8UT-wKJWRrRy", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "YGx15qY8dI1I"}, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "z8RVmgxd-SVY": {"name": "text", "uuid": "eGfzTZuG7qmx", "__type": "Var"}, "spSEgdHgJRz6": {"expr": {"__ref": "RYU3hSxGlSyL"}, "html": false, "__type": "ExprText"}, "RYU3hSxGlSyL": {"path": ["$props", "text"], "fallback": null, "__type": "ObjectPath"}, "eyTwF0u6nc93": {"param": {"__ref": "XfLVmOI9u1V1"}, "expr": {"__ref": "bCJngeTQ-dUP"}, "__type": "Arg"}, "bCJngeTQ-dUP": {"text": ["Card 1"], "__type": "TemplatedString"}, "ct5Jo61S9P8t": {"param": {"__ref": "XfLVmOI9u1V1"}, "expr": {"__ref": "nEt3A6_8_6Is"}, "__type": "Arg"}, "nEt3A6_8_6Is": {"text": ["Card 2"], "__type": "TemplatedString"}, "B9zYr3pbJggq": {"param": {"__ref": "XfLVmOI9u1V1"}, "expr": {"__ref": "b2Nr7wG9tofr"}, "__type": "Arg"}, "b2Nr7wG9tofr": {"text": ["Card 3"], "__type": "TemplatedString"}, "2o0X1c2b3P6s": {"name": "text", "__type": "Text"}, "CtD4fdLfYXxg": {"code": "\"default-id\"", "fallback": null, "__type": "CustomCode"}, "lLqFxdV1xwlX": {"code": "($props.testName)", "fallback": null, "__type": "CustomCode"}, "jlOzOu13taVE": {"name": "text", "__type": "Text"}, "YGx15qY8dI1I": {"code": "\"Default Card\"", "fallback": null, "__type": "CustomCode"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}]]