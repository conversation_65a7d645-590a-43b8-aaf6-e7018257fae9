{"root": "BCSapDYpwip1", "map": {"x3CbC05YZ4rs": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "f0uiPN_hubOo": {"name": "Default Typography", "rs": {"__ref": "x3CbC05YZ4rs"}, "preview": null, "uuid": "5A_eJeslAgci", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "BIeI4OEXuzM3": {"values": {}, "mixins": [], "__type": "RuleSet"}, "nag9JKhmzm5B": {"rs": {"__ref": "BIeI4OEXuzM3"}, "__type": "ThemeLayoutSettings"}, "v7iJ5xH6miFk": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "58WAP7My6cmY": {"name": "Default \"h1\"", "rs": {"__ref": "v7iJ5xH6miFk"}, "preview": null, "uuid": "8W3cR9w1WXR9", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "yQNTF4flwpKq": {"selector": "h1", "style": {"__ref": "58WAP7My6cmY"}, "__type": "ThemeStyle"}, "xqeHqNHz1m6A": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "04_E87MPBTMX": {"name": "Default \"h2\"", "rs": {"__ref": "xqeHqNHz1m6A"}, "preview": null, "uuid": "PAxOnVBVEYvJ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "DoyUO87f96eZ": {"selector": "h2", "style": {"__ref": "04_E87MPBTMX"}, "__type": "ThemeStyle"}, "s2aCQMgqDo5K": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "6UqpOVh20ONR": {"name": "Default \"h3\"", "rs": {"__ref": "s2aCQMgqDo5K"}, "preview": null, "uuid": "S7UA_8TQnp8E", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "LGoTOkCVlvwO": {"selector": "h3", "style": {"__ref": "6UqpOVh20ONR"}, "__type": "ThemeStyle"}, "QzumKUresZX0": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "v6qhFZ_tKeBh": {"name": "Default \"h4\"", "rs": {"__ref": "QzumKUresZX0"}, "preview": null, "uuid": "v38yUHwULjLC", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ngMTi8x_1L1N": {"selector": "h4", "style": {"__ref": "v6qhFZ_tKeBh"}, "__type": "ThemeStyle"}, "SqthEmCrOWxT": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "YbEs7D-e95dI": {"name": "Default \"h5\"", "rs": {"__ref": "SqthEmCrOWxT"}, "preview": null, "uuid": "MyAMkn3L_PAb", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "XkFmZtqcw1W2": {"selector": "h5", "style": {"__ref": "YbEs7D-e95dI"}, "__type": "ThemeStyle"}, "CwPWwzs0jdpF": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "HI2V9py2Vhcr": {"name": "Default \"h6\"", "rs": {"__ref": "CwPWwzs0jdpF"}, "preview": null, "uuid": "Bfsdhgp-Nk27", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "87YaCmcj1cDQ": {"selector": "h6", "style": {"__ref": "HI2V9py2Vhcr"}, "__type": "ThemeStyle"}, "nZpKiuMXwoGc": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "KK-RZNXFRlM1": {"name": "Default \"a\"", "rs": {"__ref": "nZpKiuMXwoGc"}, "preview": null, "uuid": "Rv14hqLmdJQf", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ktOb-ofZrp9c": {"selector": "a", "style": {"__ref": "KK-RZNXFRlM1"}, "__type": "ThemeStyle"}, "TURMvy01iy9x": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "3puqCaoODZeZ": {"name": "Default \"a:hover\"", "rs": {"__ref": "TURMvy01iy9x"}, "preview": null, "uuid": "ZYK9p_UPJgZZ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "jcI80JEvQr1I": {"selector": "a:hover", "style": {"__ref": "3puqCaoODZeZ"}, "__type": "ThemeStyle"}, "kaDtvd0DtQsy": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "cAAfMwon90gH": {"name": "Default \"blockquote\"", "rs": {"__ref": "kaDtvd0DtQsy"}, "preview": null, "uuid": "VQ3aY0tQnWa5", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "f1oJZ-uAonPP": {"selector": "blockquote", "style": {"__ref": "cAAfMwon90gH"}, "__type": "ThemeStyle"}, "Wovlu5LKAM7G": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "QeOj-T8yIFO8": {"name": "Default \"code\"", "rs": {"__ref": "Wovlu5LKAM7G"}, "preview": null, "uuid": "gCfypXntPjRM", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Gs5ONakUMTRR": {"selector": "code", "style": {"__ref": "QeOj-T8yIFO8"}, "__type": "ThemeStyle"}, "M_d05s8dqhQE": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "3onzIWOh7WTW": {"name": "Default \"pre\"", "rs": {"__ref": "M_d05s8dqhQE"}, "preview": null, "uuid": "Sa7qec9CMkgQ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "MIGCw5xqCfBe": {"selector": "pre", "style": {"__ref": "3onzIWOh7WTW"}, "__type": "ThemeStyle"}, "YGKNE4qT0NiG": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "XMY6ZgECgfUM": {"name": "Default \"ol\"", "rs": {"__ref": "YGKNE4qT0NiG"}, "preview": null, "uuid": "Elxr7T-SoVc8", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "FK01T0vtS6vS": {"selector": "ol", "style": {"__ref": "XMY6ZgECgfUM"}, "__type": "ThemeStyle"}, "_DMksa6329aw": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "arE2NkyjTYgR": {"name": "Default \"ul\"", "rs": {"__ref": "_DMksa6329aw"}, "preview": null, "uuid": "FqyDKz2-SnjF", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "gUKRNoO9t7s7": {"selector": "ul", "style": {"__ref": "arE2NkyjTYgR"}, "__type": "ThemeStyle"}, "0r9m3ln4WnGp": {"defaultStyle": {"__ref": "f0uiPN_hubOo"}, "styles": [{"__ref": "yQNTF4flwpKq"}, {"__ref": "DoyUO87f96eZ"}, {"__ref": "LGoTOkCVlvwO"}, {"__ref": "ngMTi8x_1L1N"}, {"__ref": "XkFmZtqcw1W2"}, {"__ref": "87YaCmcj1cDQ"}, {"__ref": "ktOb-ofZrp9c"}, {"__ref": "jcI80JEvQr1I"}, {"__ref": "f1oJZ-uAonPP"}, {"__ref": "Gs5ONakUMTRR"}, {"__ref": "MIGCw5xqCfBe"}, {"__ref": "FK01T0vtS6vS"}, {"__ref": "gUKRNoO9t7s7"}], "layout": {"__ref": "nag9JKhmzm5B"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "hy12GL06h6cT": {"name": "text", "__type": "Text"}, "o58SjX_i5Ywn": {"name": "Screen", "uuid": "q2EKzRgIAYWo", "__type": "Var"}, "_31RHkkjlbr4": {"type": {"__ref": "hy12GL06h6cT"}, "variable": {"__ref": "o58SjX_i5Ywn"}, "uuid": "BCl15Q1I0flL", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "spSoBfkNC9IQ": {"type": "global-screen", "param": {"__ref": "_31RHkkjlbr4"}, "uuid": "zAl1UAuj6E9X", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "UIvZWnR-Ngmy": {"uuid": "MtO-Q2f106hd", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "BCSapDYpwip1": {"components": [{"__ref": "V226OBkzRSPU"}, {"__ref": "Jiyf66GfEFDm"}, {"__ref": "ql4VCaCOvNP1"}, {"__ref": "dWca18snGeYG"}, {"__ref": "JG9woUtjoule"}, {"__ref": "Ne3xLd3E1rq_"}, {"__ref": "OWhx_xnNF2ZI"}, {"__ref": "-4cjfY6bsQHp"}, {"__ref": "rUc8yskSOz3s"}, {"__ref": "yZxg7fe0Y2fQ"}, {"__ref": "ogA3Ba-Zhixt"}, {"__ref": "pFuAS-_hAHrA"}, {"__ref": "S04ZmdyZZkRx"}, {"__ref": "KMhpYZizzUGY"}, {"__ref": "6H76z8FLjkh-"}, {"__ref": "KLle8ITw6_ca"}, {"__ref": "epCmh4z3q9bT"}, {"__ref": "t39BHTFuEs1Y"}, {"__ref": "FwlPNg1fz1xs"}, {"__ref": "h5wKzN5TMGNh"}, {"__ref": "K2hN6Iwwezwk"}, {"__ref": "DVlROOjD0ll5"}, {"__ref": "IsmFrKljt21B"}, {"__ref": "m-IdAog6cDbw"}, {"__ref": "Qxg9i8zj5Hh1"}, {"__ref": "fweZU7Fp0T3e"}, {"__ref": "yRjzyv_9KYhm"}, {"__ref": "yDeafJvod_P_"}, {"__ref": "zSvSCDUhsjJz"}, {"__ref": "RAQ3LEziHrsY"}, {"__ref": "3LPogKhlFgO4"}, {"__ref": "twknkdPnuVXE"}, {"__ref": "rzhFuVDaPvrN"}, {"__ref": "RJ9Iqajlsc3E"}], "arenas": [{"__ref": "gI_OZ4OAsbcR"}], "pageArenas": [{"__ref": "4SrwZ0MOWvO3"}], "componentArenas": [], "globalVariantGroups": [{"__ref": "spSoBfkNC9IQ"}], "userManagedFonts": [], "globalVariant": {"__ref": "UIvZWnR-Ngmy"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "0r9m3ln4WnGp"}], "activeTheme": {"__ref": "0r9m3ln4WnGp"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "spSoBfkNC9IQ"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "gI_OZ4OAsbcR": {"name": "Custom arena 1", "children": [], "__type": "Arena"}, "V226OBkzRSPU": {"__type": "Component", "uuid": "RT4ChbTE-C16", "name": "hostless-plasmic-head", "params": [{"__ref": "xISSb0qlo9Js"}, {"__ref": "hkiF8Jb7I5SR"}, {"__ref": "P1wI1Bv2HapA"}, {"__ref": "DDRWTAdTi4a6"}], "states": [], "tplTree": {"__ref": "LXmWCOpRhJFu"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "JgQSXZvIjAJ5"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "WgU3meY99gME"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false}, "Jiyf66GfEFDm": {"__type": "Component", "uuid": "OeDtIayeiEPJ", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "OPdOEodOGulq"}, {"__ref": "kKtcGB0jd5u7"}, {"__ref": "4pfyCISbDIch"}, {"__ref": "26m5uivF0Btb"}, {"__ref": "vj2zMyHlSyY6"}], "states": [], "tplTree": {"__ref": "SRmTfgIqIcEp"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "-BDqGwkwvfpp"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "P2lbI_6s0w3j"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false}, "xISSb0qlo9Js": {"__type": "PropParam", "type": {"__ref": "HSKQiG40NpD3"}, "variable": {"__ref": "vXNDxr27bd8C"}, "uuid": "0AV9UJkiLg-2", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "hkiF8Jb7I5SR": {"__type": "PropParam", "type": {"__ref": "KoeOC-gzsbVT"}, "variable": {"__ref": "VWEDF8qNTjQO"}, "uuid": "5U3hYfS4_dCs", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "P1wI1Bv2HapA": {"__type": "PropParam", "type": {"__ref": "RFLryRPBgYT0"}, "variable": {"__ref": "kFtexDPrrtLJ"}, "uuid": "fgXlQ8QApW0N", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "DDRWTAdTi4a6": {"__type": "PropParam", "type": {"__ref": "0IuaB3ncCnXY"}, "variable": {"__ref": "ziJePH3XmW77"}, "uuid": "MFCX7f33QD3o", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "LXmWCOpRhJFu": {"__type": "TplTag", "tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "3VtwbHzE05tN", "parent": null, "locked": null, "vsettings": [{"__ref": "wdhyIkyFPzWf"}]}, "JgQSXZvIjAJ5": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "nxaA7C3lKMwB", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "WgU3meY99gME": {"__type": "CodeComponentMeta", "importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": []}, "OPdOEodOGulq": {"__type": "PropParam", "type": {"__ref": "8_8vTGFWBq7B"}, "variable": {"__ref": "m97e7j55ep9f"}, "uuid": "OQz7RseOZAP9", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "kKtcGB0jd5u7": {"__type": "PropParam", "type": {"__ref": "94E1Z0EWJMOw"}, "variable": {"__ref": "_GtqnaS1d69h"}, "uuid": "WWIV6YPOp-y-", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "4pfyCISbDIch": {"__type": "SlotParam", "type": {"__ref": "YfEfGha2DWc3"}, "tplSlot": {"__ref": "zvdE70UhEI4e"}, "variable": {"__ref": "XoC86lY2qone"}, "uuid": "CL8BXyLNVg-I", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "26m5uivF0Btb": {"__type": "PropParam", "type": {"__ref": "DIuaRhgXid9q"}, "variable": {"__ref": "lVRv6ez77kAA"}, "uuid": "14rRNh2FlSGW", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Only fetch in batches of this size; for pagination", "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "vj2zMyHlSyY6": {"__type": "PropParam", "type": {"__ref": "HrM4lq2YnB1e"}, "variable": {"__ref": "bD2slj_Fqlor"}, "uuid": "opKWUAxoCx6k", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "0-based index of the paginated page to fetch", "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "SRmTfgIqIcEp": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "zvdE70UhEI4e"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Mu16Oj6YoAjS", "parent": null, "locked": null, "vsettings": [{"__ref": "-sbKI79RaCrp"}]}, "-BDqGwkwvfpp": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "YcaTJwB5QmFI", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "P2lbI_6s0w3j": {"__type": "CodeComponentMeta", "importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": []}, "HSKQiG40NpD3": {"__type": "Text", "name": "text"}, "vXNDxr27bd8C": {"__type": "Var", "name": "title", "uuid": "dPRMYmd2U1ct"}, "KoeOC-gzsbVT": {"__type": "Text", "name": "text"}, "VWEDF8qNTjQO": {"__type": "Var", "name": "description", "uuid": "INL4O1gsDgpi"}, "RFLryRPBgYT0": {"__type": "Img", "name": "img"}, "kFtexDPrrtLJ": {"__type": "Var", "name": "image", "uuid": "xqzF9Q6sfKGu"}, "0IuaB3ncCnXY": {"__type": "Text", "name": "text"}, "ziJePH3XmW77": {"__type": "Var", "name": "canonical", "uuid": "Hlu_gmzyfnT8"}, "wdhyIkyFPzWf": {"__type": "VariantSetting", "variants": [{"__ref": "JgQSXZvIjAJ5"}], "args": [], "attrs": {}, "rs": {"__ref": "z0htJeoAraQF"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "8_8vTGFWBq7B": {"__type": "AnyType", "name": "any"}, "m97e7j55ep9f": {"__type": "Var", "name": "dataOp", "uuid": "l5IFcONJs0_f"}, "94E1Z0EWJMOw": {"__type": "Text", "name": "text"}, "_GtqnaS1d69h": {"__type": "Var", "name": "name", "uuid": "GLlYx-YbWxyz"}, "YfEfGha2DWc3": {"__type": "RenderFuncType", "name": "renderFunc", "params": [{"__ref": "X_BNQPogTgrR"}], "allowed": [], "allowRootWrapper": null}, "XoC86lY2qone": {"__type": "Var", "name": "children", "uuid": "sJd7wNRf_0to"}, "DIuaRhgXid9q": {"__type": "<PERSON><PERSON>", "name": "num"}, "lVRv6ez77kAA": {"__type": "Var", "name": "pageSize", "uuid": "5DTIhGbSqd1W"}, "HrM4lq2YnB1e": {"__type": "<PERSON><PERSON>", "name": "num"}, "bD2slj_Fqlor": {"__type": "Var", "name": "pageIndex", "uuid": "pyyA921rJ5kK"}, "zvdE70UhEI4e": {"__type": "TplSlot", "param": {"__ref": "4pfyCISbDIch"}, "defaultContents": [], "uuid": "PvT8MG8wJQeG", "parent": {"__ref": "SRmTfgIqIcEp"}, "locked": null, "vsettings": [{"__ref": "aozYXKjudB44"}]}, "-sbKI79RaCrp": {"__type": "VariantSetting", "variants": [{"__ref": "-BDqGwkwvfpp"}], "args": [], "attrs": {}, "rs": {"__ref": "UxYZ5EgGwDjt"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "z0htJeoAraQF": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "X_BNQPogTgrR": {"__type": "ArgType", "name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "2NN4_ctkPL0n"}}, "aozYXKjudB44": {"__type": "VariantSetting", "variants": [{"__ref": "-BDqGwkwvfpp"}], "args": [], "attrs": {}, "rs": {"__ref": "Q3f1c1BFa9ND"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "UxYZ5EgGwDjt": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "2NN4_ctkPL0n": {"__type": "AnyType", "name": "any"}, "Q3f1c1BFa9ND": {"__type": "RuleSet", "values": {}, "mixins": []}, "ql4VCaCOvNP1": {"__type": "Component", "uuid": "9ej4TQgl_axp", "name": "plasmic-react-aria-text", "params": [{"__ref": "xzhIZOtTVlLY"}, {"__ref": "SrYX6L9WDtv7"}], "states": [], "tplTree": {"__ref": "Gd8vUVqlHwMz"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "dGaJ3dAGvUvJ"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "lveHFbw0XCG6"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "dWca18snGeYG": {"__type": "Component", "uuid": "8rktScmVZp6d", "name": "plasmic-react-aria-heading", "params": [{"__ref": "v7hIyuJ0crPn"}, {"__ref": "AfaqJxb1k0iT"}], "states": [], "tplTree": {"__ref": "fVleBC21Wxfj"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "RXqLI_eLgPt7"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "Y-L8JH_X_Ll2"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "JG9woUtjoule": {"__type": "Component", "uuid": "2zAnC1otOUFI", "name": "plasmic-react-aria-description", "params": [{"__ref": "KduweZF6b2Au"}, {"__ref": "5EFISrd9FfPR"}], "states": [], "tplTree": {"__ref": "rkLub4IQr-KE"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "7Lovrtb2Lat1"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "57xxq6HY-lTF"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "Ne3xLd3E1rq_": {"__type": "Component", "uuid": "xBiN-Gt3Vo0k", "name": "plasmic-react-aria-dialog", "params": [{"__ref": "jNXtIh6qmpD5"}], "states": [], "tplTree": {"__ref": "eZbZlxF59e-R"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "PzHz0FefVgTI"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "zpu8_W9Rhq4-"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "OWhx_xnNF2ZI": {"__type": "Component", "uuid": "m2XI1shAsSpD", "name": "plasmic-react-aria-overlayArrow", "params": [{"__ref": "i2MY6DAp4qsC"}], "states": [], "tplTree": {"__ref": "7ZfHZKMOlE1H"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "n5BD8rXzj5QR"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "GNYC0HkAlGGS"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false}, "-4cjfY6bsQHp": {"__type": "Component", "uuid": "fV94Y_tQrCaU", "name": "plasmic-react-aria-select-value", "params": [{"__ref": "2w9C0kUtmU20"}, {"__ref": "2DFskbdQy0aO"}], "states": [], "tplTree": {"__ref": "BnJMQqnk_JxB"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "EqMkE8AI3N4F"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "4CItPRfcJg4W"}, "type": "code", "subComps": [], "superComp": {"__ref": "rUc8yskSOz3s"}, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "rUc8yskSOz3s": {"__type": "Component", "uuid": "xriU8f31h76a", "name": "plasmic-react-aria-select", "params": [{"__ref": "flcO9oZUpHaU"}, {"__ref": "koIckaExUvZ0"}, {"__ref": "W3KoQaieCC0E"}, {"__ref": "B9RWpFNN1Lmd"}, {"__ref": "d6eSeuJg2IES"}, {"__ref": "xdWAvPbbqfWd"}, {"__ref": "P7nDx-eRHj6d"}, {"__ref": "NArm2BuWXFW3"}, {"__ref": "M8PnNezjD3OS"}, {"__ref": "QuQZ-9LzrRUU"}], "states": [{"__ref": "KiJ1O9zM1NoI"}, {"__ref": "R7CFG6EdJkK5"}], "tplTree": {"__ref": "oeevVi3DMLg5"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "r6qvgw_GHIEb"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "N9tgm1Kw7zDq"}, "type": "code", "subComps": [{"__ref": "-4cjfY6bsQHp"}], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "yZxg7fe0Y2fQ": {"__type": "Component", "uuid": "lI4B--QQ3jvW", "name": "plasmic-react-aria-combobox", "params": [{"__ref": "JllGFy83iZuQ"}, {"__ref": "nl6DdTqEFQN_"}, {"__ref": "ujbcOAldw3-W"}, {"__ref": "tvyo5c11TT73"}, {"__ref": "7sECEaSa_P4y"}, {"__ref": "Lsp0ODt6IlbD"}, {"__ref": "dPLgOXO2z4HT"}, {"__ref": "EzVFsfJLdZw7"}, {"__ref": "D5LSBZeo8HJO"}], "states": [{"__ref": "oVbn9afLUJZY"}, {"__ref": "qVBkslDcvSJA"}], "tplTree": {"__ref": "l6uA9OWQ8e_M"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "m-y17Tx0tv99"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "SSTvWu6L1Lw8"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "ogA3Ba-Zhixt": {"__type": "Component", "uuid": "d5DQ41EG-zFP", "name": "plasmic-react-aria-button", "params": [{"__ref": "_EJTkOV3SVHe"}, {"__ref": "nPt0c3qswA8t"}, {"__ref": "Rnc2bxi_s68a"}, {"__ref": "Eqwt1eEARK-2"}, {"__ref": "4TdlypS-qnre"}, {"__ref": "CJSt1MB1F66u"}, {"__ref": "hRYKplYzHupO"}, {"__ref": "MeSgJVppu2YC"}, {"__ref": "kOgJLcGycRd4"}, {"__ref": "RsQ9C4UNlyHf"}], "states": [], "tplTree": {"__ref": "J7fi3lvAutOZ"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "3tpDmYiHYGZj"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "sp-J6ms8TxeJ"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "pFuAS-_hAHrA": {"__type": "Component", "uuid": "BNJFQbZylLri", "name": "plasmic-react-aria-label", "params": [{"__ref": "ZLVfAvxq6BGG"}], "states": [], "tplTree": {"__ref": "b0Gt2-3jAvHh"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "mEnjH3DU2iQz"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "fWcvQB583adQ"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "S04ZmdyZZkRx": {"__type": "Component", "uuid": "9qjaHUa3f5cJ", "name": "plasmic-react-aria-listbox-item", "params": [{"__ref": "1qhxjkmS6wT-"}, {"__ref": "aSQEGQyc_wqb"}, {"__ref": "YnGWnntf4Vaw"}], "states": [], "tplTree": {"__ref": "HKJt9kjEpdBs"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "GWQygEnv9jqh"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "IlRBWmhKBNmZ"}, "type": "code", "subComps": [], "superComp": {"__ref": "6H76z8FLjkh-"}, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "KMhpYZizzUGY": {"__type": "Component", "uuid": "75qiu_HNqA89", "name": "plasmic-react-aria-listbox-section", "params": [{"__ref": "wwe8bzYv_tiK"}, {"__ref": "JxtNbtwVRIwW"}], "states": [], "tplTree": {"__ref": "USpndbark-aW"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "fcl12scH4tr-"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "XDI6o67o2rDR"}, "type": "code", "subComps": [], "superComp": {"__ref": "6H76z8FLjkh-"}, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "6H76z8FLjkh-": {"__type": "Component", "uuid": "OTp76L9oHwBf", "name": "plasmic-react-aria-listbox", "params": [{"__ref": "hHX7e_Totljv"}, {"__ref": "e5WjIV2Kts1c"}, {"__ref": "LXRAp2ZnhyCi"}, {"__ref": "29XxctBhZdNU"}], "states": [{"__ref": "c_U9gB6Te7eC"}], "tplTree": {"__ref": "AkUg3LwwB-b3"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "PKdRLBegukyT"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "uQkXVzJ9PAhl"}, "type": "code", "subComps": [{"__ref": "S04ZmdyZZkRx"}, {"__ref": "KMhpYZizzUGY"}], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "KLle8ITw6_ca": {"__type": "Component", "uuid": "MjvyVDVMfVFB", "name": "plasmic-react-aria-popover", "params": [{"__ref": "W2VqdwCldUR2"}, {"__ref": "XDrbvQ_E0-Qu"}, {"__ref": "rUXghXflxifq"}, {"__ref": "CmHySRIEsb9j"}, {"__ref": "1J-Cff-UO8YS"}, {"__ref": "gQsssmQydNgp"}, {"__ref": "GQcv2QMIdfbm"}, {"__ref": "pIWPVcmPcemS"}], "states": [], "tplTree": {"__ref": "Zv9NGOY8_8Z0"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "7Ew-QWB5H_rA"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "sdGCUnAeMbXr"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "epCmh4z3q9bT": {"__type": "Component", "uuid": "nQJw_pz_Iapu", "name": "plasmic-react-aria-textarea", "params": [{"__ref": "OccMVjgrLJrv"}, {"__ref": "agkuYjilMD9_"}, {"__ref": "zoN0xy7wgYVY"}, {"__ref": "cIv5_vsEQ5s8"}, {"__ref": "e7mO0z-GNtWe"}, {"__ref": "TE-6Y8F5JoST"}, {"__ref": "PpcH5JpsxIt0"}, {"__ref": "zSBZO5twJjK6"}, {"__ref": "9JA9Ewfxxg4n"}, {"__ref": "JYfMrGmYEMjP"}, {"__ref": "XTo7wOWnZGVM"}, {"__ref": "YaBfTq70T71w"}, {"__ref": "Svz7oOlWpFo3"}, {"__ref": "8FA6CvvqWIY7"}, {"__ref": "Ir9D2hSZuuj6"}, {"__ref": "fUizWZCzf_t2"}, {"__ref": "n7E2LOE1aP3h"}, {"__ref": "xAmAn0gXvxTU"}, {"__ref": "t02_jTuxeW2c"}, {"__ref": "b9rnOuNqE3BZ"}, {"__ref": "wA1qRxJYgs8-"}, {"__ref": "O5KDjpp3_rAF"}, {"__ref": "VeOLd70Vn9Wu"}, {"__ref": "OnD9AhpTnGbc"}, {"__ref": "3N1swebQrYlu"}], "states": [{"__ref": "m-zzkGrd-GW-"}], "tplTree": {"__ref": "XSxVDR9n35R2"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "OzWC1l3fsP7x"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "aSUqnk1kATda"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false}, "t39BHTFuEs1Y": {"__type": "Component", "uuid": "-ZsWhlYnF1-K", "name": "plasmic-react-aria-input", "params": [{"__ref": "iAlWTnlXMdv9"}, {"__ref": "TcGBpDMlABHm"}, {"__ref": "2relo7pFHlY5"}, {"__ref": "z7dnVPDG2jKm"}, {"__ref": "2xUV6QK7K8x1"}, {"__ref": "H_kSEz1WOy-C"}, {"__ref": "GGY5nhRPiuxu"}, {"__ref": "5hLdadQWlzPV"}, {"__ref": "z-oW4wHJqgrb"}, {"__ref": "6tEZmAMtUxiJ"}, {"__ref": "wIJt2jklofK-"}, {"__ref": "tOFu45W6bu3l"}, {"__ref": "k-GBcot4bNLD"}, {"__ref": "1v6RJJ4x01sd"}, {"__ref": "a0Vs-kcZVKgG"}, {"__ref": "vww4BYM_6rKW"}, {"__ref": "st__fW3j3Vwp"}, {"__ref": "FFB8NRpIvpIt"}, {"__ref": "qEJGR-oC280Z"}, {"__ref": "Jx_0B1Kli1b1"}, {"__ref": "A-YdeODbX_JS"}, {"__ref": "7kv4sHQtNcog"}, {"__ref": "eQoQB0N2fyqJ"}, {"__ref": "Ui7QzKBwDMv4"}, {"__ref": "CudZBSxvkR_U"}, {"__ref": "vp1pTcztcj1U"}, {"__ref": "jTEXJixjciol"}, {"__ref": "D4tZMXSAG1cy"}], "states": [{"__ref": "OQK-TD2Ocubt"}], "tplTree": {"__ref": "zbJaafCZtLIU"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "JSuP08XEi3TQ"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "QZ-mpbhSeMox"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false}, "FwlPNg1fz1xs": {"__type": "Component", "uuid": "lODdzkuVrMth", "name": "plasmic-react-aria-switch", "params": [{"__ref": "jFE8qckWy91-"}, {"__ref": "z0LYF2iQxt0L"}, {"__ref": "VwH-ooz9kSTW"}, {"__ref": "VDxA8w8Fjgpa"}, {"__ref": "JliaW-VVrJJd"}, {"__ref": "8tvWcD6ObNC9"}, {"__ref": "f0WhPUsJ_awQ"}, {"__ref": "DNCrPrvZsT2o"}, {"__ref": "WEUsZg2J6xCZ"}], "states": [{"__ref": "ohEZFDMRM2cP"}], "tplTree": {"__ref": "kxL1LrvPWSs8"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "5_LRVlcQFHM_"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "uYiRorQdHntW"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "h5wKzN5TMGNh": {"__type": "Component", "uuid": "DcgilofAvsyy", "name": "plasmic-react-aria-checkbox", "params": [{"__ref": "U8hPV1X1OY6Y"}, {"__ref": "k87TaYEsRjIC"}, {"__ref": "Sy8V6sUg09eF"}, {"__ref": "C6gSEJd2l5t5"}, {"__ref": "gnaeT42GMoWM"}, {"__ref": "Y1sJfWYMCLhv"}, {"__ref": "lsC4m80mrUAb"}, {"__ref": "69BKTLgAukRe"}, {"__ref": "1THh-AWJLQC_"}, {"__ref": "0O9ijXMm6-AK"}, {"__ref": "xIJLBXp5k59y"}, {"__ref": "CzYEciJKuzcM"}, {"__ref": "l-1xdxeRXseP"}], "states": [{"__ref": "C3Jau6PbDAHv"}], "tplTree": {"__ref": "v3W21WJ1-t0E"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "kQh9Iwf1iIq7"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "noNWmxjevr7x"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "K2hN6Iwwezwk": {"__type": "Component", "uuid": "G4nl5hMlCiNd", "name": "plasmic-react-aria-checkboxGroup", "params": [{"__ref": "Bo7PXWPCLBcd"}, {"__ref": "cGqRGewWgSuP"}, {"__ref": "vlJemucVYdqB"}, {"__ref": "oqPOjmwCBUz8"}, {"__ref": "J5g_U87q43-Q"}, {"__ref": "Jf_AyPbp3wPQ"}, {"__ref": "7eHHVQndMGBB"}, {"__ref": "tAqUYf3AXAd7"}, {"__ref": "0agE3xW6H-Eq"}, {"__ref": "g4s579GmTreB"}], "states": [{"__ref": "3gRGw3SaFZCz"}], "tplTree": {"__ref": "WrstRlbE0oMM"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "ukASI-FmmI6H"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "eAMvIpWXffUb"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "DVlROOjD0ll5": {"__type": "Component", "uuid": "WDMCSxIDCknt", "name": "plasmic-react-aria-radioGroup-radio", "params": [{"__ref": "1JE2OsoUyP-A"}, {"__ref": "LuxVn-AsxE6y"}, {"__ref": "_62dv5OKznRE"}, {"__ref": "o-HJRYy9ZdDf"}, {"__ref": "QqqRWhhzlV5U"}], "states": [], "tplTree": {"__ref": "n18rgy53oZC-"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "C__sH9cs49R0"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "9LIbd0y0nVET"}, "type": "code", "subComps": [], "superComp": {"__ref": "IsmFrKljt21B"}, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "IsmFrKljt21B": {"__type": "Component", "uuid": "oyJ8ojnCWU42", "name": "plasmic-react-aria-radioGroup", "params": [{"__ref": "noEPu1hz-AGU"}, {"__ref": "3ObckD7k7wWb"}, {"__ref": "KYXG0xWiBhyM"}, {"__ref": "wKszUesMeMhb"}, {"__ref": "R8EZq7VnorZh"}, {"__ref": "JvK8uD4Yn-1i"}, {"__ref": "oyGh8ArLt5pI"}, {"__ref": "LzaVBIZ5ZcMy"}, {"__ref": "s762wGZP-37x"}, {"__ref": "hOpRRCsNjCKj"}], "states": [{"__ref": "_LhVlOQ40K7l"}], "tplTree": {"__ref": "6YziQWGvbttf"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "VB5YF0sw6gSV"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "JmI113krTe1V"}, "type": "code", "subComps": [{"__ref": "DVlROOjD0ll5"}], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "m-IdAog6cDbw": {"__type": "Component", "uuid": "ISh2o7EzU-WC", "name": "plasmic-react-aria-textField", "params": [{"__ref": "1_6EKRiLknji"}, {"__ref": "a44KecQLrdLi"}, {"__ref": "eQarnEg7Itmg"}, {"__ref": "JMRPjFYs_KHq"}, {"__ref": "RguA2GhodHBc"}, {"__ref": "fj54d47nNhc_"}, {"__ref": "ksOo_v_gVFw-"}, {"__ref": "g7E02OPbOGvS"}, {"__ref": "kw7XxFuXi2bn"}, {"__ref": "44f4W_CHLAAb"}, {"__ref": "I9JhLhnNpWHa"}, {"__ref": "gaMVb7Kg2Bcv"}, {"__ref": "XQIF0uc8H5yQ"}, {"__ref": "V6m8AbI4CXeG"}, {"__ref": "0jiDRhvy8Tj1"}, {"__ref": "hArsGir2njqa"}, {"__ref": "tA-fXcTnnUEy"}, {"__ref": "LLQ9YDZXBDnC"}, {"__ref": "IbPXeC7UusRI"}, {"__ref": "nBME0I8-RKdu"}, {"__ref": "RwPD3dPWmSo3"}, {"__ref": "s_JRsXJIfEtz"}, {"__ref": "8jYgpV4uAKHT"}, {"__ref": "Wlfkpj2bA1JC"}, {"__ref": "-i7-W1rtWkUt"}, {"__ref": "EyVLrbkz2huj"}, {"__ref": "Ys7JPbKbsfPc"}, {"__ref": "k_0lADVc1q8i"}, {"__ref": "zYztCTglA05U"}, {"__ref": "HImE9S8NWqDd"}, {"__ref": "KCMt8FhBdKf2"}, {"__ref": "1magH2O-EAbn"}], "states": [{"__ref": "tVPmrzOjoZGk"}], "tplTree": {"__ref": "KIeELUFBH5Hj"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "IdcdHSnSjaIA"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "8oL8yW0h89QN"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "Qxg9i8zj5Hh1": {"__type": "Component", "uuid": "m5gsdSYJlMzz", "name": "plasmic-react-aria-modal", "params": [{"__ref": "1br4K9-A-4KI"}, {"__ref": "e9FQDILaYbT0"}, {"__ref": "ew_18CMPUBat"}, {"__ref": "z4OvkRJ1viw3"}, {"__ref": "GE-5s3Oqsous"}, {"__ref": "Cx-p60wNsUOX"}, {"__ref": "UBvedGHK9mBP"}], "states": [{"__ref": "fcfA-yA3-bCY"}], "tplTree": {"__ref": "Nd-LOkzWtFKY"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "Bjzxxbl0bfKM"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "5QfVILvE7Is_"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "fweZU7Fp0T3e": {"__type": "Component", "uuid": "Kqn3uwBAQFZ6", "name": "plasmic-react-aria-tooltip", "params": [{"__ref": "lS-Qj3908pj8"}, {"__ref": "5C_9gv8wP8K8"}, {"__ref": "9cakiqAuJ1u-"}, {"__ref": "IKJJOW69Bee7"}, {"__ref": "rx7kSvIBSdSf"}, {"__ref": "wFClJulX6dq-"}, {"__ref": "pvZe4FnLtgY6"}, {"__ref": "GopMTiyps_RM"}, {"__ref": "y6yHoorvkTgq"}, {"__ref": "AGaoy-y<PERSON><PERSON><PERSON>"}, {"__ref": "SMz0E91xfeA9"}, {"__ref": "Q1k-9-wI5uAX"}, {"__ref": "Mguzf6FO62RQ"}], "states": [{"__ref": "q5qdGHZlv36r"}], "tplTree": {"__ref": "GOLxeZCIJoXF"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "fTwOQEtzhW4M"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "NTYpn3pxiEuz"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "yRjzyv_9KYhm": {"__type": "Component", "uuid": "nuADx-tphLW_", "name": "plasmic-react-aria-dialogTrigger", "params": [{"__ref": "TpGiUPZY8zXM"}, {"__ref": "7UYhJ8OM4Jyn"}, {"__ref": "6s1FyXsphxs6"}, {"__ref": "8xEmkimGBoJg"}], "states": [{"__ref": "ahHO-fTK7Bae"}], "tplTree": {"__ref": "b8CA_B1dUdHr"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "Qdams5aqVXe_"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "P4qvCGrY8at5"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "yDeafJvod_P_": {"__type": "Component", "uuid": "13bBz_sSmYyi", "name": "plasmic-react-aria-slider-sliderOutput", "params": [{"__ref": "bY3H2VzK-zzh"}], "states": [], "tplTree": {"__ref": "55Pzq4Iq4kTE"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "z77jXHviIbWx"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "SZ_Pl6qWZJ_E"}, "type": "code", "subComps": [], "superComp": {"__ref": "twknkdPnuVXE"}, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "zSvSCDUhsjJz": {"__type": "Component", "uuid": "B-YNPsRf5D66", "name": "plasmic-react-aria-slider-sliderThumb", "params": [{"__ref": "bGRZWJztFeCk"}, {"__ref": "ztWdfzxSKDJu"}, {"__ref": "cYi6TixAJpii"}, {"__ref": "YnkBxEAOed6b"}, {"__ref": "66iDNAtM0XDs"}], "states": [], "tplTree": {"__ref": "1VHPDxXudeyv"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "ezWNyNKnPxW2"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "--jFi7qwrwBW"}, "type": "code", "subComps": [], "superComp": {"__ref": "twknkdPnuVXE"}, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "RAQ3LEziHrsY": {"__type": "Component", "uuid": "uDwBhNaYhq9A", "name": "plasmic-react-aria-slider-sliderTrack", "params": [{"__ref": "FloImOO5Mh19"}, {"__ref": "BUAMaP0Ml2L8"}, {"__ref": "3iF95mjXxoTt"}, {"__ref": "FuoTpciDNPnZ"}, {"__ref": "rQ6WSwx-zI2P"}], "states": [], "tplTree": {"__ref": "ZKByXjXlu_do"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "k3bYp7zKIlx4"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "PGyYJ8l05Oie"}, "type": "code", "subComps": [], "superComp": {"__ref": "twknkdPnuVXE"}, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false}, "3LPogKhlFgO4": {"__type": "Component", "uuid": "dLoLyT8HGpHV", "name": "plasmic-react-aria-slider-range-slider", "params": [{"__ref": "J5vy-5dmOvWs"}, {"__ref": "AYRzuYV179-a"}, {"__ref": "INX7lV-KHiUW"}, {"__ref": "60uGR06bTBgb"}, {"__ref": "CIqvFRE2qnoI"}, {"__ref": "U5nh5L2ES_sU"}, {"__ref": "CHEQyE7rTj-y"}, {"__ref": "kuKtXBRtcaKS"}, {"__ref": "jwri07I7H9s2"}, {"__ref": "9hmzkyQVtQtJ"}], "states": [{"__ref": "FeJdXgPcaOX3"}], "tplTree": {"__ref": "Vut3qb93ALXn"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "b8RqNI6qtM1s"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "nv8RZuTXE0mu"}, "type": "code", "subComps": [], "superComp": {"__ref": "twknkdPnuVXE"}, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "twknkdPnuVXE": {"__type": "Component", "uuid": "AQPWirVF47Af", "name": "plasmic-react-aria-slider", "params": [{"__ref": "UVE3lCFVz7Q9"}, {"__ref": "CiGQk81yZJZu"}, {"__ref": "R_14QGonhwXE"}, {"__ref": "Gl71sJP137Ku"}, {"__ref": "nopbxlBfRiyW"}, {"__ref": "lSgzothCesfj"}, {"__ref": "YKmpJEhO_UIG"}, {"__ref": "xV9gFrR76sAK"}, {"__ref": "ungSok76jtwV"}, {"__ref": "dnkyMl2yYr7G"}], "states": [{"__ref": "AJwxak35fqGJ"}], "tplTree": {"__ref": "fqFF4RipQaX4"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "Uuaz9SnkNpB0"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "TSj4hE747s6j"}, "type": "code", "subComps": [{"__ref": "yDeafJvod_P_"}, {"__ref": "zSvSCDUhsjJz"}, {"__ref": "RAQ3LEziHrsY"}, {"__ref": "3LPogKhlFgO4"}], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true}, "rzhFuVDaPvrN": {"__type": "Component", "uuid": "5zvGpfLLf0Cr", "name": "Counter", "params": [], "states": [], "tplTree": {"__ref": "7eWz-PjYEr8F"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "7BOkqiB7Zne7"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "Q97QBhR4qzQ5"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false}, "xzhIZOtTVlLY": {"__type": "SlotParam", "type": {"__ref": "gFsR5kx2FRUy"}, "tplSlot": {"__ref": "U3FV0e9uZjxy"}, "variable": {"__ref": "EuMrphw5O8RY"}, "uuid": "UuPrx7p5HYjj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "SrYX6L9WDtv7": {"__type": "PropParam", "type": {"__ref": "XWWg-oC-tfWS"}, "variable": {"__ref": "Movlw3_hea0J"}, "uuid": "yLpp7xSE_UfD", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "dxDiej9oxSFJ"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "Gd8vUVqlHwMz": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "U3FV0e9uZjxy"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "-sXus3DEFBtg", "parent": null, "locked": null, "vsettings": [{"__ref": "tFCS_PKlU0lT"}]}, "dGaJ3dAGvUvJ": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "UNlw3ZdkVQCC", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "lveHFbw0XCG6": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerText", "defaultExport": false, "displayName": "Aria Text", "importName": "BaseText", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"children": {"type": "text", "value": "Some text..."}}, "variants": {}, "refActions": []}, "v7hIyuJ0crPn": {"__type": "SlotParam", "type": {"__ref": "0S0RSrYtx8Bp"}, "tplSlot": {"__ref": "uEtAK9VLi0mn"}, "variable": {"__ref": "WxY_NznGwG7i"}, "uuid": "zsAWZq5CXgzU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "AfaqJxb1k0iT": {"__type": "PropParam", "type": {"__ref": "lMJsosksYcF7"}, "variable": {"__ref": "5hptDbtVcEle"}, "uuid": "5jD8SRAyTCKX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "KavzKlYgKFpl"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "fVleBC21Wxfj": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "uEtAK9VLi0mn"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "lnsxlR2vZQ2l", "parent": null, "locked": null, "vsettings": [{"__ref": "3jPCsFfU4Vc-"}]}, "RXqLI_eLgPt7": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "QUB0pJ5DDBED", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "Y-L8JH_X_Ll2": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerHeading", "defaultExport": false, "displayName": "<PERSON>ing", "importName": "BaseHeading", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "107k7j7RG3cK"}, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"children": {"type": "text", "value": "Heading"}}, "variants": {}, "refActions": []}, "KduweZF6b2Au": {"__type": "SlotParam", "type": {"__ref": "w02JB_4gwzXh"}, "tplSlot": {"__ref": "cthBJY7tIEiq"}, "variable": {"__ref": "keA-DcmTwzAQ"}, "uuid": "kUdfUqByiGaK", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "5EFISrd9FfPR": {"__type": "PropParam", "type": {"__ref": "NpnnbFDGDYWy"}, "variable": {"__ref": "lD8DZxqCwvoq"}, "uuid": "nGRFF0omRe29", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "PBmfChuoyaNN"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "rkLub4IQr-KE": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "cthBJY7tIEiq"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "IlkKm1UAi0CH", "parent": null, "locked": null, "vsettings": [{"__ref": "87AlTZtsW3KZ"}]}, "7Lovrtb2Lat1": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "A3IuBkGzh-qc", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "57xxq6HY-lTF": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerText", "defaultExport": false, "displayName": "Aria Description", "importName": "BaseText", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"children": {"type": "text", "value": "Some text..."}}, "variants": {}, "refActions": []}, "jNXtIh6qmpD5": {"__type": "SlotParam", "type": {"__ref": "owCaAL_tscsG"}, "tplSlot": {"__ref": "Wl2GxtXfb_lv"}, "variable": {"__ref": "IZhfXHGU5qAI"}, "uuid": "WrjOskVz9V4Z", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "eZbZlxF59e-R": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "Wl2GxtXfb_lv"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "p_lejXooUL-y", "parent": null, "locked": null, "vsettings": [{"__ref": "UhtBKl7MYy7Y"}]}, "PzHz0FefVgTI": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "NVBx-lBFrANH", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "zpu8_W9Rhq4-": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerDialog", "defaultExport": false, "displayName": "Aria Dialog", "importName": "BaseDialog", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": []}, "i2MY6DAp4qsC": {"__type": "SlotParam", "type": {"__ref": "iPXAC3gT-_tJ"}, "tplSlot": {"__ref": "pw-RwkS5GAZl"}, "variable": {"__ref": "5AuajdEq3_yu"}, "uuid": "w9MWSCj2GmGr", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "7ZfHZKMOlE1H": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "pw-RwkS5GAZl"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "iFL9wi_bT6BO", "parent": null, "locked": null, "vsettings": [{"__ref": "pV2lINbcDVbz"}]}, "n5BD8rXzj5QR": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "hW1WvMKrlMoD", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "GNYC0HkAlGGS": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerOverlayArrow", "defaultExport": false, "displayName": "Aria Overlay Arrow", "importName": "BaseOverlayArrow", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {"children": {"type": "hbox", "children": [], "styles": {"width": 0, "height": 0, "padding": 0, "borderLeftWidth": "5px", "borderRightWidth": "5px", "borderTopWidth": "5px", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderTopStyle": "solid", "borderLeftColor": "transparent", "borderRightColor": "transparent", "borderTopColor": "black"}}}, "variants": {"placementTop": {"__ref": "aGfVd0s0Yi9Q"}, "placementLeft": {"__ref": "m-h1_w93yZ6O"}, "placementRight": {"__ref": "CIXFrc4Ijr6h"}}, "refActions": []}, "2w9C0kUtmU20": {"__type": "PropParam", "type": {"__ref": "BBG1BRtqkVB3"}, "variable": {"__ref": "Tqs1FSK3-o4Y"}, "uuid": "9FqVdjbvJbwI", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "jc6wymUKJetp"}, "previewExpr": null, "propEffect": null, "description": "Customize the placeholder text and styles", "displayName": "Customize placeholder", "about": "Customize the placeholder text and styles", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "2DFskbdQy0aO": {"__type": "SlotParam", "type": {"__ref": "PnT91rP9FC07"}, "tplSlot": {"__ref": "wdnip2BhEImR"}, "variable": {"__ref": "hb0R18Z1Y7Sh"}, "uuid": "LX0GNLKt950R", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Placeholder", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "BnJMQqnk_JxB": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "wdnip2BhEImR"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "nOQKU_uy8BQo", "parent": null, "locked": null, "vsettings": [{"__ref": "CuGDTvOwflQV"}]}, "EqMkE8AI3N4F": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "HHxksYbGNIbB", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "4CItPRfcJg4W": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerSelect", "defaultExport": false, "displayName": "Aria Selected Value", "importName": "BaseSelectValue", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"children": [{"type": "text", "value": "Select an item"}]}, "variants": {}, "refActions": []}, "flcO9oZUpHaU": {"__type": "PropParam", "type": {"__ref": "1v6mxW_yftjZ"}, "variable": {"__ref": "rsJMm3aI1wkb"}, "uuid": "Mv79FA9-78kW", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Name for this field if it is part of a form", "displayName": "Form field key", "about": "Name for this field if it is part of a form", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "koIckaExUvZ0": {"__type": "PropParam", "type": {"__ref": "0FsiBOwcU3pp"}, "variable": {"__ref": "I-kZWEWBURiv"}, "uuid": "xO0xCNvwJjaZ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Assistive technology uses this if there is no visible label for this Select", "displayName": "ARIA label", "about": "Assistive technology uses this if there is no visible label for this Select", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "W3KoQaieCC0E": {"__type": "PropParam", "type": {"__ref": "CU22cC8o4MTi"}, "variable": {"__ref": "ggrMiDQpOPMQ"}, "uuid": "a3GkBNGmYu7k", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the Select is read-only and unfocusable", "displayName": "Disabled", "about": "Whether the Select is read-only and unfocusable", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "B9RWpFNN1Lmd": {"__type": "PropParam", "type": {"__ref": "IjtpsdBDzpt5"}, "variable": {"__ref": "oonJKJfAecGr"}, "uuid": "_8Fed-MgEdkX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the Select should be focused when rendered", "displayName": null, "about": "Whether the Select should be focused when rendered", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "d6eSeuJg2IES": {"__type": "StateParam", "type": {"__ref": "An_dMBZTUhIA"}, "state": {"__ref": "KiJ1O9zM1NoI"}, "variable": {"__ref": "yjCF6tPVM6U8"}, "uuid": "Er8_jyGWB7J4", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": "defaultSelectedKey", "description": null, "displayName": "Initial selected item", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "xdWAvPbbqfWd": {"__type": "PropParam", "type": {"__ref": "bFhz2h3VloKW"}, "variable": {"__ref": "jmjDtb1pdDzV"}, "uuid": "F8lkFYJoUSMg", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "P7nDx-eRHj6d": {"__type": "PropParam", "type": {"__ref": "apsYnD89D1iU"}, "variable": {"__ref": "zvaqMVfiz0gh"}, "uuid": "C3ocbGSPHjZw", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The items that are disabled. These items cannot be selected, focused, or otherwise interacted with.", "displayName": "Disabled values", "about": "The items that are disabled. These items cannot be selected, focused, or otherwise interacted with.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "NArm2BuWXFW3": {"__type": "StateParam", "type": {"__ref": "4woNmID5nfEV"}, "state": {"__ref": "R7CFG6EdJkK5"}, "variable": {"__ref": "c8i_jVr53eyw"}, "uuid": "IGgkgbObW1H9", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "vvZ8rh5VQBQk"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "M8PnNezjD3OS": {"__type": "PropParam", "type": {"__ref": "nlA8kVM9fnN9"}, "variable": {"__ref": "CILxfk4Ae6o_"}, "uuid": "wiUyccMvSyc0", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "QuQZ-9LzrRUU": {"__type": "SlotParam", "type": {"__ref": "jhFNWeC72DWj"}, "tplSlot": {"__ref": "l0wUnAegff5S"}, "variable": {"__ref": "N6jyRAkZ0Q-I"}, "uuid": "RVlbOvEtcgmu", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "KiJ1O9zM1NoI": {"__type": "NamedState", "variableType": "text", "name": "selected<PERSON><PERSON><PERSON>", "param": {"__ref": "d6eSeuJg2IES"}, "accessType": "writable", "onChangeParam": {"__ref": "xdWAvPbbqfWd"}, "tplNode": null, "implicitState": null}, "R7CFG6EdJkK5": {"__type": "NamedState", "variableType": "boolean", "name": "isOpen", "param": {"__ref": "NArm2BuWXFW3"}, "accessType": "writable", "onChangeParam": {"__ref": "M8PnNezjD3OS"}, "tplNode": null, "implicitState": null}, "oeevVi3DMLg5": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "l0wUnAegff5S"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "88RN8Uq4EMPu", "parent": null, "locked": null, "vsettings": [{"__ref": "CN3Rg-pvyufh"}]}, "r6qvgw_GHIEb": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "C5QwV7par4hm", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "N9tgm1Kw7zDq": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerSelect", "defaultExport": false, "displayName": "Aria Select", "importName": "BaseSelect", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"children": [{"type": "vbox", "styles": {"justifyContent": "flex-start", "alignItems": "flex-start", "width": "300px", "padding": 0}, "children": [{"type": "component", "name": "plasmic-react-aria-label", "props": {"children": {"type": "text", "value": "Label"}}}, {"type": "component", "name": "plasmic-react-aria-button", "styles": {"width": "100%", "padding": "4px 10px", "background": "white"}, "props": {"children": {"type": "hbox", "styles": {"width": "stretch", "justifyContent": "space-between", "alignItems": "center", "padding": 0}, "children": [{"type": "component", "name": "plasmic-react-aria-select-value"}, {"type": "hbox", "children": [], "styles": {"width": 0, "height": 0, "padding": 0, "borderLeftWidth": "5px", "borderRightWidth": "5px", "borderTopWidth": "5px", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderTopStyle": "solid", "borderLeftColor": "transparent", "borderRightColor": "transparent", "borderTopColor": "black"}}]}}}, {"type": "component", "name": "plasmic-react-aria-popover", "styles": {"backgroundColor": "white", "padding": "10px", "overflow": "scroll", "width": "unset"}, "props": {"children": [{"type": "component", "name": "plasmic-react-aria-listbox", "props": {"selectionMode": "single"}, "styles": {"borderWidth": 0, "width": "stretch"}}]}}]}]}, "variants": {"focused": {"__ref": "BordBQlJxaZr"}, "focusVisible": {"__ref": "oWem6E5kLLWM"}, "disabled": {"__ref": "I-M4xgdFSr4p"}}, "refActions": []}, "JllGFy83iZuQ": {"__type": "PropParam", "type": {"__ref": "mK4y1Dsbrm_I"}, "variable": {"__ref": "IOiu09f7yG5F"}, "uuid": "pLYwAgXMWlzV", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Name for this field if it is part of a form", "displayName": "Form field key", "about": "Name for this field if it is part of a form", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "nl6DdTqEFQN_": {"__type": "PropParam", "type": {"__ref": "19U4cEeLv-uO"}, "variable": {"__ref": "uy3tsih2MsEy"}, "uuid": "4OhjboMhlJ7B", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Assistive technology uses this if there is no visible label for this ComboBox", "displayName": "ARIA label", "about": "Assistive technology uses this if there is no visible label for this ComboBox", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "ujbcOAldw3-W": {"__type": "PropParam", "type": {"__ref": "4iJHi0qEhfpO"}, "variable": {"__ref": "TPitDcWVAhrz"}, "uuid": "nOE5nAq1IxAE", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the ComboBox is read-only and unfocusable", "displayName": "Disabled", "about": "Whether the ComboBox is read-only and unfocusable", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "tvyo5c11TT73": {"__type": "StateParam", "type": {"__ref": "gibM3ypmOOJ0"}, "state": {"__ref": "oVbn9afLUJZY"}, "variable": {"__ref": "EFR4NtkWeTyY"}, "uuid": "ppZsDAVpw48B", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": "defaultSelectedKey", "description": null, "displayName": "Initial selected item", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "7sECEaSa_P4y": {"__type": "PropParam", "type": {"__ref": "EkdF0O_J9Aj6"}, "variable": {"__ref": "zzux5vPQja2C"}, "uuid": "zAGTEAtQORy-", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The items that are disabled. These items cannot be selected, focused, or otherwise interacted with.", "displayName": "Disabled values", "about": "The items that are disabled. These items cannot be selected, focused, or otherwise interacted with.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "Lsp0ODt6IlbD": {"__type": "StateParam", "type": {"__ref": "EkT6DMWWzNTg"}, "state": {"__ref": "qVBkslDcvSJA"}, "variable": {"__ref": "bk02pXKvUW7X"}, "uuid": "oDzpiXg-ILxH", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "BUpJZdh5CxKR"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "dPLgOXO2z4HT": {"__type": "PropParam", "type": {"__ref": "mCnYnvIHeGYY"}, "variable": {"__ref": "o0y3guzs5cUU"}, "uuid": "Zq0E-s7tXgbO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "EzVFsfJLdZw7": {"__type": "PropParam", "type": {"__ref": "7NLP25QnrQi5"}, "variable": {"__ref": "v6LpNWFaqEr-"}, "uuid": "9381MqLNfIct", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "D5LSBZeo8HJO": {"__type": "SlotParam", "type": {"__ref": "107UDlzfQsTB"}, "tplSlot": {"__ref": "yCYQDwxq1xng"}, "variable": {"__ref": "a8xxyrLBaq_x"}, "uuid": "oA2kL1OR0sL9", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "oVbn9afLUJZY": {"__type": "NamedState", "variableType": "text", "name": "selected<PERSON><PERSON><PERSON>", "param": {"__ref": "tvyo5c11TT73"}, "accessType": "writable", "onChangeParam": {"__ref": "dPLgOXO2z4HT"}, "tplNode": null, "implicitState": null}, "qVBkslDcvSJA": {"__type": "NamedState", "variableType": "boolean", "name": "isOpen", "param": {"__ref": "Lsp0ODt6IlbD"}, "accessType": "writable", "onChangeParam": {"__ref": "EzVFsfJLdZw7"}, "tplNode": null, "implicitState": null}, "l6uA9OWQ8e_M": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "yCYQDwxq1xng"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "rHph90f5sAKs", "parent": null, "locked": null, "vsettings": [{"__ref": "7vXpMbdB0LD_"}]}, "m-y17Tx0tv99": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "TRF6OOxJkvyq", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "SSTvWu6L1Lw8": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerComboBox", "defaultExport": false, "displayName": "Aria ComboBox", "importName": "BaseComboBox", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"children": [{"type": "vbox", "styles": {"justifyContent": "flex-start", "alignItems": "flex-start", "width": "300px", "padding": 0}, "children": [{"type": "component", "name": "plasmic-react-aria-label", "props": {"children": {"type": "text", "value": "Label"}}}, {"type": "hbox", "styles": {"padding": 0}, "children": [{"type": "component", "name": "plasmic-react-aria-input", "styles": {"width": "100%", "borderRightWidth": 0}}, {"type": "component", "name": "plasmic-react-aria-button", "props": {"children": {"type": "hbox", "children": [], "styles": {"width": 0, "height": 0, "padding": 0, "borderLeftWidth": "5px", "borderRightWidth": "5px", "borderTopWidth": "5px", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderTopStyle": "solid", "borderLeftColor": "transparent", "borderRightColor": "transparent", "borderTopColor": "black"}}}}]}, {"type": "component", "name": "plasmic-react-aria-popover", "styles": {"backgroundColor": "white", "padding": "10px", "overflow": "scroll", "width": "unset"}, "props": {"offset": 0, "children": [{"type": "component", "name": "plasmic-react-aria-listbox", "props": {"selectionMode": "single"}, "styles": {"borderWidth": 0, "width": "stretch"}}]}}]}]}, "variants": {"disabled": {"__ref": "LGvg8H14akbp"}}, "refActions": []}, "_EJTkOV3SVHe": {"__type": "PropParam", "type": {"__ref": "h-NciQLfOP0Y"}, "variable": {"__ref": "XBWylTcS3qjI"}, "uuid": "OhG3FASl26yf", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the button should be focused when rendered", "displayName": null, "about": "Whether the button should be focused when rendered", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "nPt0c3qswA8t": {"__type": "PropParam", "type": {"__ref": "ngTTMrCmylQ5"}, "variable": {"__ref": "FlBVoapURd7i"}, "uuid": "keY_1di46qtA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the button is read-only and unfocusable", "displayName": "Disabled", "about": "Whether the button is read-only and unfocusable", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "Rnc2bxi_s68a": {"__type": "PropParam", "type": {"__ref": "nOV4UTWbz62s"}, "variable": {"__ref": "w4JSwXCRg4Wm"}, "uuid": "-rdtpyNf5Psj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Assistive technology uses this if there is no visible label for this button", "displayName": "ARIA label", "about": "Assistive technology uses this if there is no visible label for this button", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "Eqwt1eEARK-2": {"__type": "SlotParam", "type": {"__ref": "b7lIEBDjgH9M"}, "tplSlot": {"__ref": "Ia26f5w32gPM"}, "variable": {"__ref": "f1cr2I_RYc4X"}, "uuid": "y9XuXyCCpW05", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "4TdlypS-qnre": {"__type": "PropParam", "type": {"__ref": "OWQsL007i7WF"}, "variable": {"__ref": "0acZJXfUQQw6"}, "uuid": "HB6Q5mqeRukO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The URL this button navigates to. If present, this button is an <a> element.", "displayName": null, "about": "The URL this button navigates to. If present, this button is an <a> element.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "CJSt1MB1F66u": {"__type": "PropParam", "type": {"__ref": "LxhjEUGMTbl6"}, "variable": {"__ref": "y6bwksY8B8Ez"}, "uuid": "SLUoaymkxNPK", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Same as target attribute of <a> element. Only applies when the href prop is present.", "displayName": null, "about": "Same as target attribute of <a> element. Only applies when the href prop is present.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "hRYKplYzHupO": {"__type": "PropParam", "type": {"__ref": "T7R5L3IROLaR"}, "variable": {"__ref": "SAk-sCFKzuDm"}, "uuid": "w4YLFOqp824f", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether clicking this button should submit the enclosing form.", "displayName": "Submits form?", "about": "Whether clicking this button should submit the enclosing form.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "MeSgJVppu2YC": {"__type": "PropParam", "type": {"__ref": "XaWoKKFErIZ7"}, "variable": {"__ref": "H11yU8RkV_w-"}, "uuid": "KuKt2AjQzd2c", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether clicking this button should reset the enclosing form.", "displayName": "Resets form?", "about": "Whether clicking this button should reset the enclosing form.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "kOgJLcGycRd4": {"__type": "PropParam", "type": {"__ref": "dMo3cWtKus74"}, "variable": {"__ref": "bUoX6mPnWShW"}, "uuid": "bnt3hPefjkkW", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "RsQ9C4UNlyHf": {"__type": "PropParam", "type": {"__ref": "LNJN3o-Y3NYs"}, "variable": {"__ref": "Rn-35nq6z4GS"}, "uuid": "k1tVdVa33gS3", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "J7fi3lvAutOZ": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "Ia26f5w32gPM"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "uOPXZ01L37d9", "parent": null, "locked": null, "vsettings": [{"__ref": "jsFtIgzzG0eA"}]}, "3tpDmYiHYGZj": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "jBUn3ZUad4Zn", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "sp-J6ms8TxeJ": {"__type": "CodeComponentMeta", "importPath": "./components/react-aria/src/registerButton", "defaultExport": false, "displayName": "<PERSON>", "importName": "BaseButton", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "l18hNHPc4XYm"}, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"children": {"type": "text", "value": "<PERSON><PERSON>"}}, "variants": {"hovered": {"__ref": "uEQ57zNciURJ"}, "pressed": {"__ref": "iCaekTQa19yE"}, "focused": {"__ref": "qv69q2pH2gw_"}, "focusVisible": {"__ref": "NJix-KjiMoz2"}, "disabled": {"__ref": "jHYKLL7P7J3K"}}, "refActions": []}, "ZLVfAvxq6BGG": {"__type": "SlotParam", "type": {"__ref": "RY8GVB_1cjpp"}, "tplSlot": {"__ref": "Shn5gE9_ESPv"}, "variable": {"__ref": "RXmtXkYyAmwB"}, "uuid": "P8sSNlCmwn1y", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "b0Gt2-3jAvHh": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "Shn5gE9_ESPv"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "58KmOa7g1OZX", "parent": null, "locked": null, "vsettings": [{"__ref": "7N8EPc3eWqyH"}]}, "mEnjH3DU2iQz": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "q9tmcJmrw8di", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "fWcvQB583adQ": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerLabel", "defaultExport": false, "displayName": "Aria Label", "importName": "BaseLabel", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "R_Bsfrl7F21a"}, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"children": {"type": "text", "value": "Label"}}, "variants": {}, "refActions": []}, "1qhxjkmS6wT-": {"__type": "PropParam", "type": {"__ref": "DcAjXUJfmngF"}, "variable": {"__ref": "aWx8ROAYrFhD"}, "uuid": "P4rVg79F0SPX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "A unique value for tracking the selected item in state", "displayName": "Value", "about": "A unique value for tracking the selected item in state", "isRepeated": null, "isMainContentSlot": false, "required": true, "mergeWithParent": false, "isLocalizable": false}, "aSQEGQyc_wqb": {"__type": "PropParam", "type": {"__ref": "-O4kqVKNNRu6"}, "variable": {"__ref": "M70ruus8uZwK"}, "uuid": "V3eyi25BPkdk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "A user-friendly text representation of the item's contents, used for features like typeahead.", "displayName": "Label", "about": "A user-friendly text representation of the item's contents, used for features like typeahead.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "YnGWnntf4Vaw": {"__type": "SlotParam", "type": {"__ref": "LRH_H8Jbf4nO"}, "tplSlot": {"__ref": "aD6WoIRO3mRm"}, "variable": {"__ref": "1BefZagFU6FD"}, "uuid": "5wAvBvmW4XgD", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "HKJt9kjEpdBs": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "aD6WoIRO3mRm"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "efU5Ocz0sEVh", "parent": null, "locked": null, "vsettings": [{"__ref": "Q4BZSoVEieqH"}]}, "GWQygEnv9jqh": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "eyO2C4XkTOam", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "IlRBWmhKBNmZ": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerListBoxItem", "defaultExport": false, "displayName": "Aria ListBoxItem", "importName": "BaseListBoxItem", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"children": {"type": "vbox", "styles": {"display": "flex", "alignItems": "flex-start", "gap": "2px"}, "children": [{"type": "component", "name": "plasmic-react-aria-text", "props": {"slot": "label", "children": {"type": "text", "styles": {"fontWeight": 500}, "value": "<PERSON><PERSON>"}}}, {"type": "component", "name": "plasmic-react-aria-description", "props": {"children": {"type": "text", "styles": {"color": "#838383"}, "value": "Some description for Item..."}}}]}}, "variants": {"hovered": {"__ref": "2Ww-GmZbt1MZ"}, "pressed": {"__ref": "TY6-qFFK9N-j"}, "focused": {"__ref": "esesoc6E11ka"}, "focusVisible": {"__ref": "zhS_4mn6s2gW"}, "selected": {"__ref": "-pWt-ZBaR4-v"}, "disabled": {"__ref": "dXnr82cDHpVp"}}, "refActions": []}, "wwe8bzYv_tiK": {"__type": "SlotParam", "type": {"__ref": "vIx0WW6-1IuE"}, "tplSlot": {"__ref": "8iLi_hPVMzaZ"}, "variable": {"__ref": "2ruFtl0qRfPP"}, "uuid": "6_R2DElk9j7l", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "JxtNbtwVRIwW": {"__type": "SlotParam", "type": {"__ref": "ypuzcUYkIGKa"}, "tplSlot": {"__ref": "kdMN63ovNKJS"}, "variable": {"__ref": "F2IG4lJ7jMzq"}, "uuid": "nhIx8lYgcunU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "USpndbark-aW": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "8iLi_hPVMzaZ"}, {"__ref": "kdMN63ovNKJS"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "pKgUrzS7l_gO", "parent": null, "locked": null, "vsettings": [{"__ref": "vLuJUw4BqyHO"}]}, "fcl12scH4tr-": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "rco9EoePA-P4", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "XDI6o67o2rDR": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerSection", "defaultExport": false, "displayName": "Aria Section", "importName": "BaseSection", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "UvazGFJ8tyCV"}, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"header": [{"type": "text", "value": "Section Header."}], "items": [{"type": "component", "name": "plasmic-react-aria-listbox-item", "props": {"id": "section-1-1", "textValue": "Section1-Item 1", "children": [{"type": "vbox", "styles": {"display": "flex", "alignItems": "flex-start", "gap": "2px"}, "children": [{"type": "component", "name": "plasmic-react-aria-text", "props": {"slot": "label", "children": {"type": "text", "styles": {"fontWeight": 500}, "value": "Item 1"}}}, {"type": "component", "name": "plasmic-react-aria-description", "props": {"children": {"type": "text", "styles": {"color": "#838383"}, "value": "Add dynamic values to make it more interesting"}}}]}]}}, {"type": "component", "name": "plasmic-react-aria-listbox-item", "props": {"id": "section-1-2", "textValue": "Section1-Item 2", "children": [{"type": "vbox", "styles": {"display": "flex", "alignItems": "flex-start", "gap": "2px"}, "children": [{"type": "component", "name": "plasmic-react-aria-text", "props": {"slot": "label", "children": {"type": "text", "styles": {"fontWeight": 500}, "value": "Item 2"}}}, {"type": "component", "name": "plasmic-react-aria-description", "props": {"children": {"type": "text", "styles": {"color": "#838383"}, "value": "Add dynamic values to make it more interesting"}}}]}]}}, {"type": "component", "name": "plasmic-react-aria-listbox-item", "props": {"id": "section-1-3", "textValue": "Section1-Item 3", "children": [{"type": "vbox", "styles": {"display": "flex", "alignItems": "flex-start", "gap": "2px"}, "children": [{"type": "component", "name": "plasmic-react-aria-text", "props": {"slot": "label", "children": {"type": "text", "styles": {"fontWeight": 500}, "value": "Item 3"}}}, {"type": "component", "name": "plasmic-react-aria-description", "props": {"children": {"type": "text", "styles": {"color": "#838383"}, "value": "Add dynamic values to make it more interesting"}}}]}]}}]}, "variants": {}, "refActions": []}, "hHX7e_Totljv": {"__type": "SlotParam", "type": {"__ref": "6Bz25Iv7Fiut"}, "tplSlot": {"__ref": "AOi_mhKj56cA"}, "variable": {"__ref": "-PJd6qbT9KR5"}, "uuid": "9-3sDT24WUNu", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "List Items", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "e5WjIV2Kts1c": {"__type": "PropParam", "type": {"__ref": "tiCqnWSm0qXE"}, "variable": {"__ref": "axH05nmP42PG"}, "uuid": "aY8_Iu6TQJja", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "MQjq1Vkw9urQ"}, "previewExpr": null, "propEffect": null, "description": "The selection mode of the listbox", "displayName": null, "about": "The selection mode of the listbox", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "LXRAp2ZnhyCi": {"__type": "StateParam", "type": {"__ref": "Ocn76EGt99da"}, "state": {"__ref": "c_U9gB6Te7eC"}, "variable": {"__ref": "PUkEG4zs-L9I"}, "uuid": "TF59vxYWZqve", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": "defaultSelectedKeys", "description": null, "displayName": "Initial selected item", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "29XxctBhZdNU": {"__type": "PropParam", "type": {"__ref": "Fr9zCpFBsHfM"}, "variable": {"__ref": "ddCYHw05amhZ"}, "uuid": "0clwXzNlpw8s", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "c_U9gB6Te7eC": {"__type": "NamedState", "variableType": "text", "name": "selected<PERSON><PERSON><PERSON>", "param": {"__ref": "LXRAp2ZnhyCi"}, "accessType": "writable", "onChangeParam": {"__ref": "29XxctBhZdNU"}, "tplNode": null, "implicitState": null}, "AkUg3LwwB-b3": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "AOi_mhKj56cA"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "RjB5XafRELbf", "parent": null, "locked": null, "vsettings": [{"__ref": "0l6hjI2J_PRO"}]}, "PKdRLBegukyT": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "XyAjwU2y3Y0k", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "uQkXVzJ9PAhl": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerListBox", "defaultExport": false, "displayName": "Aria ListBox", "importName": "BaseListBox", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "UdShSLkh2xok"}, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": {"__ref": "qW25QwYUPo0n"}, "defaultSlotContents": {"children": [{"type": "component", "name": "plasmic-react-aria-listbox-item", "props": {"id": "1", "textValue": "Item 1", "children": [{"type": "vbox", "styles": {"display": "flex", "alignItems": "flex-start", "gap": "2px"}, "children": [{"type": "component", "name": "plasmic-react-aria-text", "props": {"slot": "label", "children": {"type": "text", "styles": {"fontWeight": 500}, "value": "Item 1"}}}, {"type": "component", "name": "plasmic-react-aria-description", "props": {"children": {"type": "text", "styles": {"color": "#838383"}, "value": "Add dynamic values to make it more interesting"}}}]}]}}, {"type": "component", "name": "plasmic-react-aria-listbox-item", "props": {"id": "2", "textValue": "Item 2", "children": [{"type": "vbox", "styles": {"display": "flex", "alignItems": "flex-start", "gap": "2px"}, "children": [{"type": "component", "name": "plasmic-react-aria-text", "props": {"slot": "label", "children": {"type": "text", "styles": {"fontWeight": 500}, "value": "Item 2"}}}, {"type": "component", "name": "plasmic-react-aria-description", "props": {"children": {"type": "text", "styles": {"color": "#838383"}, "value": "Add dynamic values to make it more interesting"}}}]}]}}, {"type": "component", "name": "plasmic-react-aria-listbox-item", "props": {"id": "3", "textValue": "Item 3", "children": [{"type": "vbox", "styles": {"display": "flex", "alignItems": "flex-start", "gap": "2px"}, "children": [{"type": "component", "name": "plasmic-react-aria-text", "props": {"slot": "label", "children": {"type": "text", "styles": {"fontWeight": 500}, "value": "Item 3"}}}, {"type": "component", "name": "plasmic-react-aria-description", "props": {"children": {"type": "text", "styles": {"color": "#838383"}, "value": "Add dynamic values to make it more interesting"}}}]}]}}, {"type": "component", "name": "plasmic-react-aria-listbox-section"}]}, "variants": {"focused": {"__ref": "8FICfkTkGTH8"}, "focusVisible": {"__ref": "XirKfoe6MmkW"}}, "refActions": []}, "W2VqdwCldUR2": {"__type": "SlotParam", "type": {"__ref": "AtJBasd67zTq"}, "tplSlot": {"__ref": "s-vWYlBySosa"}, "variable": {"__ref": "lR5WszAYFS9j"}, "uuid": "-yqrcpDPkauk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "XDrbvQ_E0-Qu": {"__type": "PropParam", "type": {"__ref": "ocxIXVYUb8QQ"}, "variable": {"__ref": "dpHkeRl3NtZX"}, "uuid": "54otU8b8kVkY", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the element should flip its orientation (e.g. top to bottom or left to right) when there is insufficient room for it to render completely.", "displayName": null, "about": "Whether the element should flip its orientation (e.g. top to bottom or left to right) when there is insufficient room for it to render completely.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "rUXghXflxifq": {"__type": "PropParam", "type": {"__ref": "-PiBNnnYWjWn"}, "variable": {"__ref": "9eFDnm0zFgOH"}, "uuid": "oPrOQUMkH1g4", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "CmHySRIEsb9j": {"__type": "PropParam", "type": {"__ref": "Qu44Krt-5kjz"}, "variable": {"__ref": "ay0MrEAfW2G8"}, "uuid": "0c_Gu89zziO3", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "pcljy3LFVVGK"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "1J-Cff-UO8YS": {"__type": "PropParam", "type": {"__ref": "3rjpFCnAFf4a"}, "variable": {"__ref": "l8o2z-FBr3Bv"}, "uuid": "595EB7bchHbm", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Default placement of the popover relative to the trigger, if there is enough space", "displayName": null, "about": "Default placement of the popover relative to the trigger, if there is enough space", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "gQsssmQydNgp": {"__type": "PropParam", "type": {"__ref": "ouHMyS8DbgrS"}, "variable": {"__ref": "FR0UkGrz4320"}, "uuid": "u_JNuKsBWLBk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Additional offset applied along the main axis between the popover and its trigger", "displayName": "Offset", "about": "Additional offset applied along the main axis between the popover and its trigger", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "GQcv2QMIdfbm": {"__type": "PropParam", "type": {"__ref": "_5xHy-P8f_C4"}, "variable": {"__ref": "t7wJxz9dGEst"}, "uuid": "FI0HeoBOaVGq", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The padding that should be applied between the popover and its surrounding container. This affects the positioning breakpoints that determine when it will attempt to flip.", "displayName": null, "about": "The padding that should be applied between the popover and its surrounding container. This affects the positioning breakpoints that determine when it will attempt to flip.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "pIWPVcmPcemS": {"__type": "PropParam", "type": {"__ref": "SC2k1MQwSWzY"}, "variable": {"__ref": "mmft_3LUJ81m"}, "uuid": "wHEtGe5yI_1_", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The additional offset applied along the cross axis between the popover and its anchor element.", "displayName": null, "about": "The additional offset applied along the cross axis between the popover and its anchor element.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "Zv9NGOY8_8Z0": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "s-vWYlBySosa"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Fop6QEkwrmex", "parent": null, "locked": null, "vsettings": [{"__ref": "pav1_4-ZtR5T"}]}, "7Ew-QWB5H_rA": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "6uewCA7D4FhV", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "sdGCUnAeMbXr": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerPopover", "defaultExport": false, "displayName": "<PERSON> Pop<PERSON>", "importName": "BasePopover", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "4WVyIQCSY8S-"}, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": true, "helpers": null, "defaultSlotContents": {"children": [{"type": "vbox", "styles": {"width": "stretch", "padding": "20px", "rowGap": "10px"}, "children": [{"type": "text", "value": "This is a Popover!"}, {"type": "text", "value": "You can put anything you can imagine here!", "styles": {"fontWeight": 500}}, {"type": "text", "value": "Use it in a `Aria Dialog Trigger` component to trigger it on a button click!"}]}]}, "variants": {"placementTop": {"__ref": "HlHweaJXssVD"}, "placementBottom": {"__ref": "KJCv5bZiUWhZ"}, "placementLeft": {"__ref": "OJy-92lAzTUN"}, "placementRight": {"__ref": "erJfZ29H7FMv"}}, "refActions": []}, "OccMVjgrLJrv": {"__type": "PropParam", "type": {"__ref": "L4DvI6ATpd4y"}, "variable": {"__ref": "Hyyx4zqy4dwz"}, "uuid": "VmpFVs5CBZjM", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Name for this field if it is part of a form", "displayName": "Form field key", "about": "Name for this field if it is part of a form", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "agkuYjilMD9_": {"__type": "PropParam", "type": {"__ref": "ob6jxt07cS5G"}, "variable": {"__ref": "5FuK9qIohnUo"}, "uuid": "OTB1lKnuONWH", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the Text Area is read-only and unfocusable", "displayName": "Disabled", "about": "Whether the Text Area is read-only and unfocusable", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "zoN0xy7wgYVY": {"__type": "PropParam", "type": {"__ref": "0Lrw-jHcEvw0"}, "variable": {"__ref": "CqTJ2pLipl4S"}, "uuid": "7Ab00sWuf6jm", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the value of this Text Area can be changed by the user. Unlike disabled, read-only does not prevent the user from interacting with the component (such as focus).", "displayName": "Read only", "about": "Whether the value of this Text Area can be changed by the user. Unlike disabled, read-only does not prevent the user from interacting with the component (such as focus).", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "cIv5_vsEQ5s8": {"__type": "PropParam", "type": {"__ref": "NlYpJ8E9dZna"}, "variable": {"__ref": "8iardJmfNt_W"}, "uuid": "95vlapphA4pf", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the Text Area should be focused when rendered", "displayName": null, "about": "Whether the Text Area should be focused when rendered", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "e7mO0z-GNtWe": {"__type": "PropParam", "type": {"__ref": "vkqgmk9rwFix"}, "variable": {"__ref": "rZbH8-vCJIXQ"}, "uuid": "nSKEsP-pkIjb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Assistive technology uses this if there is no visible label for this Text Area", "displayName": "ARIA label", "about": "Assistive technology uses this if there is no visible label for this Text Area", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "TE-6Y8F5JoST": {"__type": "PropParam", "type": {"__ref": "YSeqAIRgpVgc"}, "variable": {"__ref": "1J3f8zQiQJSe"}, "uuid": "MVptfjZfETPp", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether user input is required on the Text Area before form submission.", "displayName": "Required", "about": "Whether user input is required on the Text Area before form submission.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "PpcH5JpsxIt0": {"__type": "PropParam", "type": {"__ref": "9NYpPBudi_Qh"}, "variable": {"__ref": "xfKQo9ZLFkQ2"}, "uuid": "VBugaFwKV7aG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "zSBZO5twJjK6": {"__type": "StateParam", "type": {"__ref": "KuDHWoD4rYnE"}, "state": {"__ref": "m-zzkGrd-GW-"}, "variable": {"__ref": "pxOqvUrIa0EW"}, "uuid": "7Pg-rVbM_G2z", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": "defaultValue", "description": "The default value of the Text Area", "displayName": "Initial value", "about": "The default value of the Text Area", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "9JA9Ewfxxg4n": {"__type": "PropParam", "type": {"__ref": "0R5cJFyzTFO9"}, "variable": {"__ref": "EDCpCuDeyccs"}, "uuid": "Kbb6nHsWfCUw", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The maximum number of characters supported by the input", "displayName": null, "about": "The maximum number of characters supported by the input", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "JYfMrGmYEMjP": {"__type": "PropParam", "type": {"__ref": "U_mi0-ITp4s-"}, "variable": {"__ref": "fEtBWTNJlsum"}, "uuid": "z1bOkgQFXmqn", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The minimum number of characters supported by the input", "displayName": null, "about": "The minimum number of characters supported by the input", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "XTo7wOWnZGVM": {"__type": "PropParam", "type": {"__ref": "_EtnhA-jbIiX"}, "variable": {"__ref": "YOCba2k0_1yV"}, "uuid": "HzUv_lbkqudA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "hint to browsers as to the type of virtual keyboard configuration to use when editing this element or its contents.", "displayName": null, "about": "hint to browsers as to the type of virtual keyboard configuration to use when editing this element or its contents.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "YaBfTq70T71w": {"__type": "PropParam", "type": {"__ref": "MQPbY_QdKm4y"}, "variable": {"__ref": "SyyGNF30VrAe"}, "uuid": "BRTbIhrtBtEL", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "Svz7oOlWpFo3": {"__type": "PropParam", "type": {"__ref": "eYStq3MPBpvb"}, "variable": {"__ref": "6ndHrGUZf3hY"}, "uuid": "o-dW4mOcHOUM", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "8FA6CvvqWIY7": {"__type": "PropParam", "type": {"__ref": "Si9Jr-CvFoZP"}, "variable": {"__ref": "LFDQsv8f0i_n"}, "uuid": "WrLG1J8APQIT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "Ir9D2hSZuuj6": {"__type": "PropParam", "type": {"__ref": "zJUQoAyPly8r"}, "variable": {"__ref": "Yua6WWXBTTgf"}, "uuid": "9b0Xz9EEJGFK", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "fUizWZCzf_t2": {"__type": "PropParam", "type": {"__ref": "D-15jUvzGYkU"}, "variable": {"__ref": "3gmKR1ZwhFsw"}, "uuid": "dW41_9Dav4QL", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "n7E2LOE1aP3h": {"__type": "PropParam", "type": {"__ref": "rS-05zsKcfkT"}, "variable": {"__ref": "sTClV6rWn_rL"}, "uuid": "XwQo4YQKXx7X", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "xAmAn0gXvxTU": {"__type": "PropParam", "type": {"__ref": "Fe6qkLW0AdWv"}, "variable": {"__ref": "5zsJvhYgslt3"}, "uuid": "O5E5cAedADup", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "t02_jTuxeW2c": {"__type": "PropParam", "type": {"__ref": "yltQqZZW2Tzs"}, "variable": {"__ref": "acQiIBI2Eg3A"}, "uuid": "1hgOLequFg2p", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "b9rnOuNqE3BZ": {"__type": "PropParam", "type": {"__ref": "QIcxAml0lSwf"}, "variable": {"__ref": "LfX3a4lIW0zE"}, "uuid": "BbgLO-kV3s3-", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "wA1qRxJYgs8-": {"__type": "PropParam", "type": {"__ref": "NOz0mEgDDss-"}, "variable": {"__ref": "x94h4koZbG3x"}, "uuid": "LCsLdwerM3Ve", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "O5KDjpp3_rAF": {"__type": "PropParam", "type": {"__ref": "_9zoQ_CczKQx"}, "variable": {"__ref": "BFtVQY-HeV2R"}, "uuid": "Wh_AcRmmJgKq", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "VeOLd70Vn9Wu": {"__type": "PropParam", "type": {"__ref": "mEwMcl875c7T"}, "variable": {"__ref": "Fu9kRrjLuvrg"}, "uuid": "DkdKqkMrCB43", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "OnD9AhpTnGbc": {"__type": "PropParam", "type": {"__ref": "gmEAsMBoxROp"}, "variable": {"__ref": "0wBtIE3xnvgW"}, "uuid": "LT8GBR1qfY4M", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "3N1swebQrYlu": {"__type": "PropParam", "type": {"__ref": "cyGf5nPEfzV-"}, "variable": {"__ref": "O4B-r3kpeQs-"}, "uuid": "ilMScKlFddiF", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "m-zzkGrd-GW-": {"__type": "NamedState", "variableType": "text", "name": "value", "param": {"__ref": "zSBZO5twJjK6"}, "accessType": "writable", "onChangeParam": {"__ref": "YaBfTq70T71w"}, "tplNode": null, "implicitState": null}, "XSxVDR9n35R2": {"__type": "TplTag", "tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "9WcTbkuXcY6h", "parent": null, "locked": null, "vsettings": [{"__ref": "foBYqTf2I7tH"}]}, "OzWC1l3fsP7x": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "V3fJtX2kygUZ", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "aSUqnk1kATda": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerTextArea", "defaultExport": false, "displayName": "Aria TextArea", "importName": "BaseTextArea", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": {"__ref": "jdBIxxMPlkVU"}, "defaultSlotContents": {}, "variants": {"focused": {"__ref": "7r3WrUHWZ0XV"}, "focusVisible": {"__ref": "9bYRqh0h7Pzw"}, "hovered": {"__ref": "06YAvkUrUTqK"}, "disabled": {"__ref": "a6i_MKuzAyM3"}}, "refActions": []}, "iAlWTnlXMdv9": {"__type": "PropParam", "type": {"__ref": "G-xGo1M6BUYk"}, "variable": {"__ref": "IpwYRfDvF8Yb"}, "uuid": "cWdnPqaqVHbX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Name for this field if it is part of a form", "displayName": "Form field key", "about": "Name for this field if it is part of a form", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "TcGBpDMlABHm": {"__type": "PropParam", "type": {"__ref": "t8OlVzu8Q23P"}, "variable": {"__ref": "K_AfbBuyF-FQ"}, "uuid": "xmmvAJI5TjEi", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the Input is read-only and unfocusable", "displayName": "Disabled", "about": "Whether the Input is read-only and unfocusable", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "2relo7pFHlY5": {"__type": "PropParam", "type": {"__ref": "1yYFgmM7wL2M"}, "variable": {"__ref": "Gt9n4wr5YId4"}, "uuid": "IzcRvN4QKJIk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the value of this Input can be changed by the user. Unlike disabled, read-only does not prevent the user from interacting with the component (such as focus).", "displayName": "Read only", "about": "Whether the value of this Input can be changed by the user. Unlike disabled, read-only does not prevent the user from interacting with the component (such as focus).", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "z7dnVPDG2jKm": {"__type": "PropParam", "type": {"__ref": "-4vALmVwryAD"}, "variable": {"__ref": "n6RM_6E1VQNJ"}, "uuid": "4WLTShSE_s88", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the Input should be focused when rendered", "displayName": null, "about": "Whether the Input should be focused when rendered", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "2xUV6QK7K8x1": {"__type": "PropParam", "type": {"__ref": "cOwzWrEF6bjr"}, "variable": {"__ref": "tUgV4rb7dIxh"}, "uuid": "7eTubQoAjvBc", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Assistive technology uses this if there is no visible label for this Input", "displayName": "ARIA label", "about": "Assistive technology uses this if there is no visible label for this Input", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "H_kSEz1WOy-C": {"__type": "PropParam", "type": {"__ref": "ilpO8rd5Zjfm"}, "variable": {"__ref": "mEedLlHjUgDI"}, "uuid": "VBmbMYkpH6WV", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether user input is required on the Input before form submission.", "displayName": "Required", "about": "Whether user input is required on the Input before form submission.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "GGY5nhRPiuxu": {"__type": "PropParam", "type": {"__ref": "81lBpKKXvJvU"}, "variable": {"__ref": "0Gq1K3h17BOZ"}, "uuid": "KiR309lbq4fh", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "5hLdadQWlzPV": {"__type": "StateParam", "type": {"__ref": "gk4IK9eiVI4S"}, "state": {"__ref": "OQK-TD2Ocubt"}, "variable": {"__ref": "XN33DL9YFMd9"}, "uuid": "fyxbO1EPxj46", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": "defaultValue", "description": "The default value of the Input", "displayName": "Initial value", "about": "The default value of the Input", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "z-oW4wHJqgrb": {"__type": "PropParam", "type": {"__ref": "RzDIN25oqtxL"}, "variable": {"__ref": "yAcwc5RaOgt5"}, "uuid": "YZmvRzmXsxLc", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The maximum number of characters supported by the input", "displayName": null, "about": "The maximum number of characters supported by the input", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "6tEZmAMtUxiJ": {"__type": "PropParam", "type": {"__ref": "fPofvXDoC2QD"}, "variable": {"__ref": "hn-1IQIYVJ8s"}, "uuid": "ZeO6LYXcjavk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The minimum number of characters supported by the input", "displayName": null, "about": "The minimum number of characters supported by the input", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "wIJt2jklofK-": {"__type": "PropParam", "type": {"__ref": "glbs3c4MaSaa"}, "variable": {"__ref": "qmqazPW5eaUJ"}, "uuid": "5tzg0WKRXpAi", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Regex pattern that the value of the input must match to be valid", "displayName": null, "about": "Regex pattern that the value of the input must match to be valid", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "tOFu45W6bu3l": {"__type": "PropParam", "type": {"__ref": "ojI-UItn-Yjy"}, "variable": {"__ref": "4oH-WmXh_PFD"}, "uuid": "su3MaX-Cjogh", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The type of data that an input field is expected to handle. It influences the input's behavior, validation, and the kind of interface provided to the user.", "displayName": null, "about": "The type of data that an input field is expected to handle. It influences the input's behavior, validation, and the kind of interface provided to the user.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "k-GBcot4bNLD": {"__type": "PropParam", "type": {"__ref": "kP8RkRS-AlLQ"}, "variable": {"__ref": "Sh_vMfvfTcZT"}, "uuid": "8UaAD3uypePP", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "hint to browsers as to the type of virtual keyboard configuration to use when editing this element or its contents.", "displayName": null, "about": "hint to browsers as to the type of virtual keyboard configuration to use when editing this element or its contents.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "1v6RJJ4x01sd": {"__type": "PropParam", "type": {"__ref": "aOwLz00rom2w"}, "variable": {"__ref": "cOYbdrj8mtMk"}, "uuid": "lc7UkSZY80bj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Guidance as to the type of data expected in the field", "displayName": null, "about": "Guidance as to the type of data expected in the field", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "a0Vs-kcZVKgG": {"__type": "PropParam", "type": {"__ref": "dNnoG4vhW8RV"}, "variable": {"__ref": "NdIKsBUlgnUG"}, "uuid": "xMOKDzXXzj3B", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "vww4BYM_6rKW": {"__type": "PropParam", "type": {"__ref": "-AOSS5QtimRU"}, "variable": {"__ref": "d8gVxToFAtFE"}, "uuid": "Xa1lO_jcGope", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "st__fW3j3Vwp": {"__type": "PropParam", "type": {"__ref": "RCbp52uYzmJ_"}, "variable": {"__ref": "GlH_ieDBXkr6"}, "uuid": "xLZB9wvjZo21", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "FFB8NRpIvpIt": {"__type": "PropParam", "type": {"__ref": "fFu6wjab0sfk"}, "variable": {"__ref": "GKNbX3-4Obrd"}, "uuid": "BFr6wbYgrqCR", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "qEJGR-oC280Z": {"__type": "PropParam", "type": {"__ref": "QRgXGImaca8Z"}, "variable": {"__ref": "8qPA7-02aSgw"}, "uuid": "6VjMOUVzDVVz", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "Jx_0B1Kli1b1": {"__type": "PropParam", "type": {"__ref": "xA4rHK7fwa9-"}, "variable": {"__ref": "qP84a8q263eC"}, "uuid": "qlv7xpQGeqVk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "A-YdeODbX_JS": {"__type": "PropParam", "type": {"__ref": "8eyudggKbGCU"}, "variable": {"__ref": "aa1kQN4F8hau"}, "uuid": "l9kVggFnBDDq", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "7kv4sHQtNcog": {"__type": "PropParam", "type": {"__ref": "CYU6VsDzm8UE"}, "variable": {"__ref": "pibAyUkwF5WO"}, "uuid": "yWqxgYWyqfhU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "eQoQB0N2fyqJ": {"__type": "PropParam", "type": {"__ref": "HKHuvUmn0UIP"}, "variable": {"__ref": "4hgNy0mjOFGg"}, "uuid": "5VzUQfS46YO1", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "Ui7QzKBwDMv4": {"__type": "PropParam", "type": {"__ref": "Z6Er7SZppbAR"}, "variable": {"__ref": "3IncsEJJ2Olp"}, "uuid": "ZyQi_8AVzRJR", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "CudZBSxvkR_U": {"__type": "PropParam", "type": {"__ref": "SF4IPZOlA3WP"}, "variable": {"__ref": "4_eHcCqyp2CF"}, "uuid": "KezUN7flndY1", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "vp1pTcztcj1U": {"__type": "PropParam", "type": {"__ref": "RPcQ5b-su6tP"}, "variable": {"__ref": "WUaPiMHz5TL2"}, "uuid": "9t8ei8u4ImHi", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "jTEXJixjciol": {"__type": "PropParam", "type": {"__ref": "pcsLQPEmfPm9"}, "variable": {"__ref": "1YTcLyh61tz5"}, "uuid": "BNVp6ts3BtZv", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "D4tZMXSAG1cy": {"__type": "PropParam", "type": {"__ref": "EcqIz50JmpfG"}, "variable": {"__ref": "B7-TGNZtSY_d"}, "uuid": "yBuxfSPgYCcV", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "OQK-TD2Ocubt": {"__type": "NamedState", "variableType": "text", "name": "value", "param": {"__ref": "5hLdadQWlzPV"}, "accessType": "writable", "onChangeParam": {"__ref": "a0Vs-kcZVKgG"}, "tplNode": null, "implicitState": null}, "zbJaafCZtLIU": {"__type": "TplTag", "tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "DD-jwEftkVeJ", "parent": null, "locked": null, "vsettings": [{"__ref": "FP6emYbGjemD"}]}, "JSuP08XEi3TQ": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "bQUfWWpgxI_N", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "QZ-mpbhSeMox": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerInput", "defaultExport": false, "displayName": "Aria Input", "importName": "BaseInput", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "c3774Xx5iX5r"}, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": {"__ref": "W0Y6ItoFC5oj"}, "defaultSlotContents": {}, "variants": {"focused": {"__ref": "cYUNMH632k-r"}, "focusVisible": {"__ref": "PaDt2wwFxwhF"}, "hovered": {"__ref": "M6u19u8i55K_"}, "disabled": {"__ref": "hlL9gnqnqYfl"}}, "refActions": []}, "jFE8qckWy91-": {"__type": "PropParam", "type": {"__ref": "xngPheiwGhDV"}, "variable": {"__ref": "KDkmYRQf1F3x"}, "uuid": "UbUIK_XJRA3V", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Name for this field if it is part of a form", "displayName": "Form field key", "about": "Name for this field if it is part of a form", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "z0LYF2iQxt0L": {"__type": "PropParam", "type": {"__ref": "2HPKSxxVSktb"}, "variable": {"__ref": "uJKsGGKWKNLM"}, "uuid": "Wgs42dPBhSqo", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the switch is read-only and unfocusable", "displayName": "Disabled", "about": "Whether the switch is read-only and unfocusable", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "VwH-ooz9kSTW": {"__type": "PropParam", "type": {"__ref": "D6Y_BPD1mkae"}, "variable": {"__ref": "axZob0foZ34W"}, "uuid": "o9Hw7klP4jcE", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the value of this switch can be changed by the user. Unlike disabled, read-only does not prevent the user from interacting with the component (such as focus).", "displayName": "Read only", "about": "Whether the value of this switch can be changed by the user. Unlike disabled, read-only does not prevent the user from interacting with the component (such as focus).", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "VDxA8w8Fjgpa": {"__type": "PropParam", "type": {"__ref": "F_V4Ylok7Q_a"}, "variable": {"__ref": "wiJW-zBY-KA1"}, "uuid": "FPaL1AldPPCl", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the switch should be focused when rendered", "displayName": null, "about": "Whether the switch should be focused when rendered", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "JliaW-VVrJJd": {"__type": "PropParam", "type": {"__ref": "RzWAtD_l5W_Q"}, "variable": {"__ref": "UsIbf2Hz86yt"}, "uuid": "Zs6o4-Zn186F", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Assistive technology uses this if there is no visible label for this switch", "displayName": "ARIA label", "about": "Assistive technology uses this if there is no visible label for this switch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "8tvWcD6ObNC9": {"__type": "SlotParam", "type": {"__ref": "WF1gvR4hX2ND"}, "tplSlot": {"__ref": "OFn2PnBpO9nH"}, "variable": {"__ref": "SN1ybtrWiLBX"}, "uuid": "EbHWHt_L5w_G", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "f0WhPUsJ_awQ": {"__type": "PropParam", "type": {"__ref": "-f4Gw0z4dYpt"}, "variable": {"__ref": "tj9JgFuhof2r"}, "uuid": "IQfYHeoXnbXw", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The value of the switch in \"selected\" state, used when submitting an HTML form.", "displayName": null, "about": "The value of the switch in \"selected\" state, used when submitting an HTML form.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "DNCrPrvZsT2o": {"__type": "StateParam", "type": {"__ref": "K2MnTYcIM74j"}, "state": {"__ref": "ohEZFDMRM2cP"}, "variable": {"__ref": "6C6BMqLLXMfD"}, "uuid": "BPgAstmCBuct", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": "defaultSelected", "description": "Whether the switch should be selected by default", "displayName": "De<PERSON><PERSON> Selected", "about": "Whether the switch should be selected by default", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "WEUsZg2J6xCZ": {"__type": "PropParam", "type": {"__ref": "LceYW_ztWYdj"}, "variable": {"__ref": "DL61YwIrkplm"}, "uuid": "heMnxnjLSvEU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "ohEZFDMRM2cP": {"__type": "NamedState", "variableType": "boolean", "name": "isSelected", "param": {"__ref": "DNCrPrvZsT2o"}, "accessType": "writable", "onChangeParam": {"__ref": "WEUsZg2J6xCZ"}, "tplNode": null, "implicitState": null}, "kxL1LrvPWSs8": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "OFn2PnBpO9nH"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "49499rL-<PERSON><PERSON>", "parent": null, "locked": null, "vsettings": [{"__ref": "5mfTBoJgnWsP"}]}, "5_LRVlcQFHM_": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "Cv9S1bnfb-yB", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "uYiRorQdHntW": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerSwitch", "defaultExport": false, "displayName": "Aria Switch", "importName": "BaseSwitch", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "6_vF-hFsFpdP"}, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"children": [{"type": "hbox", "styles": {"display": "flex", "alignItems": "center", "justifyContent": "center", "gap": "10px", "padding": 0}, "children": [{"type": "hbox", "styles": {"width": "30px", "height": "16px", "padding": 0, "backgroundColor": "#D5D5D5", "cursor": "pointer", "borderRadius": "999px"}, "children": {"type": "hbox", "styles": {"width": "12px", "height": "12px", "position": "absolute", "top": "2px", "left": "2px", "borderRadius": "100%", "backgroundColor": "#fff", "padding": 0, "transitionProperty": "all", "transitionDuration": "0.5s", "transitionTimingFunction": "ease-in-out"}}}, {"type": "component", "name": "plasmic-react-aria-label", "props": {"children": {"type": "text", "value": "Label"}}}]}, {"type": "component", "name": "plasmic-react-aria-description", "styles": {"fontSize": "12px"}, "props": {"children": {"type": "text", "value": "Use the registered variants to see it in action..."}}}]}, "variants": {"hovered": {"__ref": "vZkVvy5pjdmt"}, "pressed": {"__ref": "XAw8TOegMMPd"}, "focused": {"__ref": "CYBigR2-YotR"}, "focusVisible": {"__ref": "Rj6EBO_aZA2_"}, "selected": {"__ref": "GoCG6LaRdbXz"}, "disabled": {"__ref": "8TgpZFZ9Q5aV"}, "readonly": {"__ref": "_IW7bH_v-ztZ"}}, "refActions": []}, "U8hPV1X1OY6Y": {"__type": "PropParam", "type": {"__ref": "dUNCWrSUow9Y"}, "variable": {"__ref": "bTpZeSHgKy5n"}, "uuid": "MiTeze074qvR", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Name for this field if it is part of a form", "displayName": "Form field key", "about": "Name for this field if it is part of a form", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "k87TaYEsRjIC": {"__type": "PropParam", "type": {"__ref": "9F6dhzSwkS5Y"}, "variable": {"__ref": "CLyEiAIt0sJD"}, "uuid": "oXwngQcgBYHG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the checkbox is read-only and unfocusable", "displayName": "Disabled", "about": "Whether the checkbox is read-only and unfocusable", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "Sy8V6sUg09eF": {"__type": "PropParam", "type": {"__ref": "uPGPq_Ab-VoL"}, "variable": {"__ref": "BBo6wjBN5ksD"}, "uuid": "-dNy1WteWEsX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the value of this checkbox can be changed by the user. Unlike disabled, read-only does not prevent the user from interacting with the component (such as focus).", "displayName": "Read only", "about": "Whether the value of this checkbox can be changed by the user. Unlike disabled, read-only does not prevent the user from interacting with the component (such as focus).", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "C6gSEJd2l5t5": {"__type": "PropParam", "type": {"__ref": "5thYpzj6VgLx"}, "variable": {"__ref": "zHtK3RO12MjM"}, "uuid": "oAfAEuwH3QWQ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Assistive technology uses this if there is no visible label for this checkbox", "displayName": "ARIA label", "about": "Assistive technology uses this if there is no visible label for this checkbox", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "gnaeT42GMoWM": {"__type": "PropParam", "type": {"__ref": "L32Rr3QaBN2t"}, "variable": {"__ref": "OXYoeoLBeoI9"}, "uuid": "vVH-TAuTULVU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether user input is required on the checkbox before form submission.", "displayName": "Required", "about": "Whether user input is required on the checkbox before form submission.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "Y1sJfWYMCLhv": {"__type": "PropParam", "type": {"__ref": "91yuLmaDcfIL"}, "variable": {"__ref": "UOhd6b47Dgz2"}, "uuid": "6QcRN0j9W4Do", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the checkbox should be focused when rendered", "displayName": null, "about": "Whether the checkbox should be focused when rendered", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "lsC4m80mrUAb": {"__type": "SlotParam", "type": {"__ref": "YQGIK4vSOuBt"}, "tplSlot": {"__ref": "_MIz55uks9YL"}, "variable": {"__ref": "S5ebJU82zFch"}, "uuid": "1eCKF9kHHfsL", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "69BKTLgAukRe": {"__type": "PropParam", "type": {"__ref": "iurmlo_wx3af"}, "variable": {"__ref": "wiyCkhMwtDO1"}, "uuid": "b11OTMHjrxnh", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The value of the checkbox in \"selected\" state, used when submitting an HTML form.", "displayName": null, "about": "The value of the checkbox in \"selected\" state, used when submitting an HTML form.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "1THh-AWJLQC_": {"__type": "StateParam", "type": {"__ref": "jjoljbdvmXlN"}, "state": {"__ref": "C3Jau6PbDAHv"}, "variable": {"__ref": "hD_HpaANQx-h"}, "uuid": "a14hscx5VxSZ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "RgKwRfAPc62_"}, "previewExpr": null, "propEffect": "defaultSelected", "description": "Whether the checkbox should be selected by default", "displayName": "De<PERSON><PERSON> Selected", "about": "Whether the checkbox should be selected by default", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "0O9ijXMm6-AK": {"__type": "PropParam", "type": {"__ref": "8S5mSipR2sL1"}, "variable": {"__ref": "xFsgXK0g1wbc"}, "uuid": "g-Yf9zuM8W0J", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "This state indicates that the checkbox is neither fully checked nor unchecked. It typically represents a partial selection when dealing with groups of options. Some but not all items in the group are selected, resulting in an indeterminate state for the checkbox.", "displayName": "Indeterminate", "about": "This state indicates that the checkbox is neither fully checked nor unchecked. It typically represents a partial selection when dealing with groups of options. Some but not all items in the group are selected, resulting in an indeterminate state for the checkbox.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "xIJLBXp5k59y": {"__type": "PropParam", "type": {"__ref": "eqKRw4-2tXQO"}, "variable": {"__ref": "KA5YVXRQ9Bg3"}, "uuid": "cduPFn1Kq2EL", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the input value is invalid", "displayName": "Invalid", "about": "Whether the input value is invalid", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "CzYEciJKuzcM": {"__type": "PropParam", "type": {"__ref": "09pvEZwIR6Vv"}, "variable": {"__ref": "WVTdd7NzLVkw"}, "uuid": "PpdOtlOWZQfw", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether to use native HTML form validation to prevent form submission when the value is missing or invalid, or mark the field as required or invalid via ARIA.", "displayName": null, "about": "Whether to use native HTML form validation to prevent form submission when the value is missing or invalid, or mark the field as required or invalid via ARIA.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "l-1xdxeRXseP": {"__type": "PropParam", "type": {"__ref": "7NFkf0PkvPwZ"}, "variable": {"__ref": "Zv-hBK6Zo3ya"}, "uuid": "oK7Am64q5ReS", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "C3Jau6PbDAHv": {"__type": "NamedState", "variableType": "boolean", "name": "isSelected", "param": {"__ref": "1THh-AWJLQC_"}, "accessType": "writable", "onChangeParam": {"__ref": "l-1xdxeRXseP"}, "tplNode": null, "implicitState": null}, "v3W21WJ1-t0E": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "_MIz55uks9YL"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "HosMb0C89PnB", "parent": null, "locked": null, "vsettings": [{"__ref": "BoHrHTJ0ONJ4"}]}, "kQh9Iwf1iIq7": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "6CO4wyfdAu3t", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "noNWmxjevr7x": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerCheckbox", "defaultExport": false, "displayName": "Aria Checkbox", "importName": "BaseCheckbox", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"children": {"type": "vbox", "styles": {"display": "flex", "alignItems": "center", "gap": "2px", "padding": 0}, "children": [{"type": "hbox", "styles": {"display": "flex", "alignItems": "center", "gap": "10px", "padding": 0}, "children": [{"type": "box", "styles": {"width": "7px", "height": "7px", "borderRadius": "3px", "borderWidth": "1px", "borderStyle": "solid", "borderColor": "black"}}, {"type": "text", "value": "Label"}]}, {"type": "text", "value": "Use the registered variants to see it in action..."}]}}, "variants": {"hovered": {"__ref": "--9qVSVEGGS1"}, "pressed": {"__ref": "xOFDRSMiAVr9"}, "focused": {"__ref": "TjvRbkOlQlv7"}, "focusVisible": {"__ref": "GJlvf5UbeHUB"}, "indeterminate": {"__ref": "JMsn0girLWos"}, "disabled": {"__ref": "0GlWZ0BeiHvW"}, "selected": {"__ref": "TaNKKXke4RlR"}, "readonly": {"__ref": "LhuVuPXTSBhr"}}, "refActions": []}, "Bo7PXWPCLBcd": {"__type": "PropParam", "type": {"__ref": "RbuYAt7PFInk"}, "variable": {"__ref": "F6diT1sda7hS"}, "uuid": "OaJ_vJLbysd-", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Name for this field if it is part of a form", "displayName": "Form field key", "about": "Name for this field if it is part of a form", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "cGqRGewWgSuP": {"__type": "PropParam", "type": {"__ref": "c2Cx_O7Zh0f0"}, "variable": {"__ref": "z1StCxkuWVkB"}, "uuid": "MbXpPLOUVvpV", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the checkbox group is read-only and unfocusable", "displayName": "Disabled", "about": "Whether the checkbox group is read-only and unfocusable", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "vlJemucVYdqB": {"__type": "PropParam", "type": {"__ref": "yvtHWFKWSt-w"}, "variable": {"__ref": "GtntqeoUw3iS"}, "uuid": "M0kK9dbwj48E", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the value of this checkbox group can be changed by the user. Unlike disabled, read-only does not prevent the user from interacting with the component (such as focus).", "displayName": "Read only", "about": "Whether the value of this checkbox group can be changed by the user. Unlike disabled, read-only does not prevent the user from interacting with the component (such as focus).", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "oqPOjmwCBUz8": {"__type": "PropParam", "type": {"__ref": "otHNppLg7x13"}, "variable": {"__ref": "fNa3fN81DS9c"}, "uuid": "CwtEJDbspazl", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Assistive technology uses this if there is no visible label for this checkbox group", "displayName": "ARIA label", "about": "Assistive technology uses this if there is no visible label for this checkbox group", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "J5g_U87q43-Q": {"__type": "PropParam", "type": {"__ref": "pXMp30od_Vvk"}, "variable": {"__ref": "EQ4ODapkLU0v"}, "uuid": "BA0ibEwapzGk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether user input is required on the checkbox group before form submission.", "displayName": "Required", "about": "Whether user input is required on the checkbox group before form submission.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "Jf_AyPbp3wPQ": {"__type": "SlotParam", "type": {"__ref": "xdSugX3u8Ot5"}, "tplSlot": {"__ref": "bUR96Aofsu4i"}, "variable": {"__ref": "sjuYjpXB8lnL"}, "uuid": "mnyjl_ivnFp_", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "7eHHVQndMGBB": {"__type": "StateParam", "type": {"__ref": "uu2X9lb-gxfX"}, "state": {"__ref": "3gRGw3SaFZCz"}, "variable": {"__ref": "X-GirVtp0lyb"}, "uuid": "ggodfxDPpiOs", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": "defaultValue", "description": "The current value", "displayName": null, "about": "The current value", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "tAqUYf3AXAd7": {"__type": "PropParam", "type": {"__ref": "P8VQIYzm6tuS"}, "variable": {"__ref": "XnZ6KSq5Ns6d"}, "uuid": "z7oErr1stj_G", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the input value is invalid", "displayName": "Invalid", "about": "Whether the input value is invalid", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "0agE3xW6H-Eq": {"__type": "PropParam", "type": {"__ref": "ViRSrl-6ibqz"}, "variable": {"__ref": "4TU0kWmdwCiW"}, "uuid": "c0A_lWCOlnPl", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether to use native HTML form validation to prevent form submission when the value is missing or invalid, or mark the field as required or invalid via ARIA.", "displayName": null, "about": "Whether to use native HTML form validation to prevent form submission when the value is missing or invalid, or mark the field as required or invalid via ARIA.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "g4s579GmTreB": {"__type": "PropParam", "type": {"__ref": "I9FbRFjjOK2c"}, "variable": {"__ref": "d2GpFhY46j5a"}, "uuid": "X7cW6JOObv7b", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "3gRGw3SaFZCz": {"__type": "NamedState", "variableType": "array", "name": "value", "param": {"__ref": "7eHHVQndMGBB"}, "accessType": "writable", "onChangeParam": {"__ref": "g4s579GmTreB"}, "tplNode": null, "implicitState": null}, "WrstRlbE0oMM": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "bUR96Aofsu4i"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "ChTQOYhiWybP", "parent": null, "locked": null, "vsettings": [{"__ref": "ErFupkZqG4YG"}]}, "ukASI-FmmI6H": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "Or8afm8SSt3K", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "eAMvIpWXffUb": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerCheckboxGroup", "defaultExport": false, "displayName": "Aria Checkbox Group", "importName": "BaseCheckboxGroup", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"children": [{"type": "vbox", "styles": {"display": "flex", "gap": "5px", "padding": 0, "alignItems": "flex-start"}, "children": [{"type": "component", "name": "plasmic-react-aria-label", "props": {"children": {"type": "text", "value": "Checkbox Group"}}}, {"type": "component", "name": "plasmic-react-aria-checkbox", "props": {"children": {"type": "vbox", "styles": {"display": "flex", "alignItems": "center", "gap": "2px", "padding": 0}, "children": [{"type": "hbox", "styles": {"display": "flex", "alignItems": "center", "gap": "10px", "padding": 0}, "children": [{"type": "box", "styles": {"width": "7px", "height": "7px", "borderRadius": "3px", "borderWidth": "1px", "borderStyle": "solid", "borderColor": "black"}}, {"type": "text", "value": "Checkbox 1"}]}]}, "value": "checkbox1"}}, {"type": "component", "name": "plasmic-react-aria-checkbox", "props": {"children": {"type": "vbox", "styles": {"display": "flex", "alignItems": "center", "gap": "2px", "padding": 0}, "children": [{"type": "hbox", "styles": {"display": "flex", "alignItems": "center", "gap": "10px", "padding": 0}, "children": [{"type": "box", "styles": {"width": "7px", "height": "7px", "borderRadius": "3px", "borderWidth": "1px", "borderStyle": "solid", "borderColor": "black"}}, {"type": "text", "value": "Checkbox 2"}]}]}, "value": "checkbox2"}}, {"type": "component", "name": "plasmic-react-aria-checkbox", "props": {"children": {"type": "vbox", "styles": {"display": "flex", "alignItems": "center", "gap": "2px", "padding": 0}, "children": [{"type": "hbox", "styles": {"display": "flex", "alignItems": "center", "gap": "10px", "padding": 0}, "children": [{"type": "box", "styles": {"width": "7px", "height": "7px", "borderRadius": "3px", "borderWidth": "1px", "borderStyle": "solid", "borderColor": "black"}}, {"type": "text", "value": "Checkbox 3"}]}]}, "value": "checkbox3"}}, {"type": "component", "name": "plasmic-react-aria-description", "props": {"children": {"type": "text", "value": "Use the registered variants to see it in action..."}}}]}]}, "variants": {"disabled": {"__ref": "iWm2PmdvfevR"}, "readonly": {"__ref": "TE3FqdYdDvOD"}}, "refActions": []}, "1JE2OsoUyP-A": {"__type": "PropParam", "type": {"__ref": "dvEaxO52gcSq"}, "variable": {"__ref": "hLOygTAZOO9P"}, "uuid": "-A2ZZyU_Vp76", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the radio is read-only and unfocusable", "displayName": "Disabled", "about": "Whether the radio is read-only and unfocusable", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "LuxVn-AsxE6y": {"__type": "PropParam", "type": {"__ref": "BaucaOxNYxiw"}, "variable": {"__ref": "YyYlR4ciSB2R"}, "uuid": "HmrqaYocETzJ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the radio should be focused when rendered", "displayName": null, "about": "Whether the radio should be focused when rendered", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "_62dv5OKznRE": {"__type": "PropParam", "type": {"__ref": "NbfvTywvA1Ua"}, "variable": {"__ref": "cWXeJztnA1gW"}, "uuid": "KI6ZsL8BEBKW", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Assistive technology uses this if there is no visible label for this radio", "displayName": "ARIA label", "about": "Assistive technology uses this if there is no visible label for this radio", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "o-HJRYy9ZdDf": {"__type": "SlotParam", "type": {"__ref": "-tLFT8ulrP6N"}, "tplSlot": {"__ref": "yIrtttZ_NSSP"}, "variable": {"__ref": "najRI9lIG3Uz"}, "uuid": "8RmNA7icHi9b", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "QqqRWhhzlV5U": {"__type": "PropParam", "type": {"__ref": "BXLkg-Q30by3"}, "variable": {"__ref": "IOy7xMDptGkc"}, "uuid": "gSl3kqvktLtl", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The value of the input element, used when submitting an HTML form.", "displayName": null, "about": "The value of the input element, used when submitting an HTML form.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "n18rgy53oZC-": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "yIrtttZ_NSSP"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Di8FJpXjBG1q", "parent": null, "locked": null, "vsettings": [{"__ref": "yfmM4G6UYe_7"}]}, "C__sH9cs49R0": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "rFOfM9dr2eBo", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "9LIbd0y0nVET": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerRadio", "defaultExport": false, "displayName": "Aria Radio", "importName": "BaseRadio", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"children": {"type": "hbox", "styles": {"display": "flex", "alignItems": "center", "gap": "10px", "padding": 0}, "children": [{"type": "box", "styles": {"width": "7px", "height": "7px", "borderRadius": "100%", "borderWidth": "1px", "borderStyle": "solid", "borderColor": "black"}}, {"type": "component", "name": "plasmic-react-aria-label", "props": {"children": {"type": "text", "value": "Radio"}}}]}}, "variants": {"selected": {"__ref": "d-qsvYRCdk7P"}, "hovered": {"__ref": "V9zNJnxj-qQL"}, "pressed": {"__ref": "45-PQbpwgQ3a"}, "focused": {"__ref": "WxQRKPp0Gkqo"}, "focusVisible": {"__ref": "mweP3kFH7nfy"}, "disabled": {"__ref": "jyiQEYhqX_0D"}, "readonly": {"__ref": "qzJd_EyepgEm"}}, "refActions": []}, "noEPu1hz-AGU": {"__type": "PropParam", "type": {"__ref": "prmT-2TP9wLK"}, "variable": {"__ref": "AsQwW69ZaPvw"}, "uuid": "_3gmcWwoD6Rr", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Name for this field if it is part of a form", "displayName": "Form field key", "about": "Name for this field if it is part of a form", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "3ObckD7k7wWb": {"__type": "PropParam", "type": {"__ref": "K8cJR1KlqYoO"}, "variable": {"__ref": "7ruHdSYT6fjh"}, "uuid": "bN_5-S_xJEO3", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the radio group is read-only and unfocusable", "displayName": "Disabled", "about": "Whether the radio group is read-only and unfocusable", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "KYXG0xWiBhyM": {"__type": "PropParam", "type": {"__ref": "A8Jzwt09KTtt"}, "variable": {"__ref": "347UuLhtg_1I"}, "uuid": "YnT7WA18tQoB", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the value of this radio group can be changed by the user. Unlike disabled, read-only does not prevent the user from interacting with the component (such as focus).", "displayName": "Read only", "about": "Whether the value of this radio group can be changed by the user. Unlike disabled, read-only does not prevent the user from interacting with the component (such as focus).", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "wKszUesMeMhb": {"__type": "PropParam", "type": {"__ref": "ww8hnBmf7iu5"}, "variable": {"__ref": "Sx5oEnehFXm_"}, "uuid": "2zr0t59Vw8cs", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Assistive technology uses this if there is no visible label for this radio group", "displayName": "ARIA label", "about": "Assistive technology uses this if there is no visible label for this radio group", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "R8EZq7VnorZh": {"__type": "PropParam", "type": {"__ref": "IsCppGdq_yBV"}, "variable": {"__ref": "eBxXf5uLitgA"}, "uuid": "gpe-BdtOdow8", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether user input is required on the radio group before form submission.", "displayName": "Required", "about": "Whether user input is required on the radio group before form submission.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "JvK8uD4Yn-1i": {"__type": "SlotParam", "type": {"__ref": "sUiuwDNtxaGO"}, "tplSlot": {"__ref": "x82j4JqebwPk"}, "variable": {"__ref": "Ii-nAvOhCHpU"}, "uuid": "rwuAOOZrfp--", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "oyGh8ArLt5pI": {"__type": "StateParam", "type": {"__ref": "zQpGYaJg3U9J"}, "state": {"__ref": "_LhVlOQ40K7l"}, "variable": {"__ref": "GizeRjL7Yqkv"}, "uuid": "hhBySPH_JEP2", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": "defaultValue", "description": "The current value", "displayName": "Initial value", "about": "The current value", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "LzaVBIZ5ZcMy": {"__type": "PropParam", "type": {"__ref": "rqkdqciD_wRi"}, "variable": {"__ref": "VLEa0eTpjFN0"}, "uuid": "QE45PlxB-dzb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the input value is invalid", "displayName": "Invalid", "about": "Whether the input value is invalid", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "s762wGZP-37x": {"__type": "PropParam", "type": {"__ref": "DqjT5RtqqrJ4"}, "variable": {"__ref": "lXXU-16RB-6v"}, "uuid": "Q1zKoQsyBA5U", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether to use native HTML form validation to prevent form submission when the value is missing or invalid, or mark the field as required or invalid via ARIA.", "displayName": null, "about": "Whether to use native HTML form validation to prevent form submission when the value is missing or invalid, or mark the field as required or invalid via ARIA.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "hOpRRCsNjCKj": {"__type": "PropParam", "type": {"__ref": "9ADQdM1oIvLW"}, "variable": {"__ref": "zNJXMZnjqvgb"}, "uuid": "RU-0uAmdCtO6", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "_LhVlOQ40K7l": {"__type": "NamedState", "variableType": "array", "name": "value", "param": {"__ref": "oyGh8ArLt5pI"}, "accessType": "writable", "onChangeParam": {"__ref": "hOpRRCsNjCKj"}, "tplNode": null, "implicitState": null}, "6YziQWGvbttf": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "x82j4JqebwPk"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "_UNOrhO4uVPk", "parent": null, "locked": null, "vsettings": [{"__ref": "HdUeelbaW9sI"}]}, "VB5YF0sw6gSV": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "n_mStgejvyS_", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "JmI113krTe1V": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerRadioGroup", "defaultExport": false, "displayName": "Aria RadioGroup", "importName": "BaseRadioGroup", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"children": [{"type": "vbox", "styles": {"display": "flex", "gap": "5px", "padding": 0, "alignItems": "flex-start"}, "children": [{"type": "component", "name": "plasmic-react-aria-label", "props": {"children": {"type": "text", "value": "Radio Group"}}}, {"type": "component", "name": "plasmic-react-aria-radioGroup-radio", "props": {"children": {"type": "hbox", "styles": {"display": "flex", "alignItems": "center", "gap": "10px", "padding": 0}, "children": [{"type": "box", "styles": {"width": "7px", "height": "7px", "borderRadius": "100%", "borderWidth": "1px", "borderStyle": "solid", "borderColor": "black"}}, {"type": "component", "name": "plasmic-react-aria-label", "props": {"children": {"type": "text", "value": "Radio 1"}}}]}, "value": "radio1"}}, {"type": "component", "name": "plasmic-react-aria-radioGroup-radio", "props": {"children": {"type": "hbox", "styles": {"display": "flex", "alignItems": "center", "gap": "10px", "padding": 0}, "children": [{"type": "box", "styles": {"width": "7px", "height": "7px", "borderRadius": "100%", "borderWidth": "1px", "borderStyle": "solid", "borderColor": "black"}}, {"type": "component", "name": "plasmic-react-aria-label", "props": {"children": {"type": "text", "value": "Radio 2"}}}]}, "value": "radio2"}}, {"type": "component", "name": "plasmic-react-aria-radioGroup-radio", "props": {"children": {"type": "hbox", "styles": {"display": "flex", "alignItems": "center", "gap": "10px", "padding": 0}, "children": [{"type": "box", "styles": {"width": "7px", "height": "7px", "borderRadius": "100%", "borderWidth": "1px", "borderStyle": "solid", "borderColor": "black"}}, {"type": "component", "name": "plasmic-react-aria-label", "props": {"children": {"type": "text", "value": "Radio 3"}}}]}, "value": "radio3"}}, {"type": "component", "name": "plasmic-react-aria-description", "props": {"children": {"type": "text", "value": "Use the registered variants to see it in action..."}}}]}]}, "variants": {"disabled": {"__ref": "EcSk_UepuyDD"}, "readonly": {"__ref": "IP7tA5IIEOQ7"}}, "refActions": []}, "1_6EKRiLknji": {"__type": "PropParam", "type": {"__ref": "WuzVc5Gc4tZ2"}, "variable": {"__ref": "zgAmzKTjRlIQ"}, "uuid": "-addu0KIq7_Q", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Name for this field if it is part of a form", "displayName": "Form field key", "about": "Name for this field if it is part of a form", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "a44KecQLrdLi": {"__type": "PropParam", "type": {"__ref": "HHDTRqsOCXDu"}, "variable": {"__ref": "GGDzxEQ7tTjf"}, "uuid": "m_taXPVWtng5", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the Text Field is read-only and unfocusable", "displayName": "Disabled", "about": "Whether the Text Field is read-only and unfocusable", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "eQarnEg7Itmg": {"__type": "PropParam", "type": {"__ref": "LHELTqffD2s8"}, "variable": {"__ref": "dEbv_cpSIMC9"}, "uuid": "r4y0P5EQdoB5", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the value of this Text Field can be changed by the user. Unlike disabled, read-only does not prevent the user from interacting with the component (such as focus).", "displayName": "Read only", "about": "Whether the value of this Text Field can be changed by the user. Unlike disabled, read-only does not prevent the user from interacting with the component (such as focus).", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "JMRPjFYs_KHq": {"__type": "PropParam", "type": {"__ref": "M-EfgzJ_BFJy"}, "variable": {"__ref": "hFd2aIxaMU7P"}, "uuid": "qQHOef2Abp-l", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the Text Field should be focused when rendered", "displayName": null, "about": "Whether the Text Field should be focused when rendered", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "RguA2GhodHBc": {"__type": "PropParam", "type": {"__ref": "MFv0F1o4rhFJ"}, "variable": {"__ref": "zHFiuzcALvVo"}, "uuid": "NbxguK9wfqT2", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Assistive technology uses this if there is no visible label for this Text Field", "displayName": "ARIA label", "about": "Assistive technology uses this if there is no visible label for this Text Field", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "fj54d47nNhc_": {"__type": "PropParam", "type": {"__ref": "aiJMSOihdjo2"}, "variable": {"__ref": "wq3C9rD_d7BG"}, "uuid": "Fq5aZZwf1S13", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether user input is required on the Text Field before form submission.", "displayName": "Required", "about": "Whether user input is required on the Text Field before form submission.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "ksOo_v_gVFw-": {"__type": "StateParam", "type": {"__ref": "Yq3AxfJKGZvm"}, "state": {"__ref": "tVPmrzOjoZGk"}, "variable": {"__ref": "tJLtCxcpHMZT"}, "uuid": "cF5xaCGrulZj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": "defaultValue", "description": "The default value of the Text Field", "displayName": "Initial value", "about": "The default value of the Text Field", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "g7E02OPbOGvS": {"__type": "PropParam", "type": {"__ref": "NZQymuVMTj52"}, "variable": {"__ref": "dI7vvnWzwirn"}, "uuid": "hcrnymg6W166", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The maximum number of characters supported by the input", "displayName": null, "about": "The maximum number of characters supported by the input", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "kw7XxFuXi2bn": {"__type": "PropParam", "type": {"__ref": "lHpA-vyOKSzb"}, "variable": {"__ref": "rLncFH6Kos9A"}, "uuid": "sdZAd6JkJx06", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The minimum number of characters supported by the input", "displayName": null, "about": "The minimum number of characters supported by the input", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "44f4W_CHLAAb": {"__type": "PropParam", "type": {"__ref": "HEPvSeQnGw8B"}, "variable": {"__ref": "NVfqqq3K-L15"}, "uuid": "WWr9uIBKCsDy", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Regex pattern that the value of the input must match to be valid", "displayName": null, "about": "Regex pattern that the value of the input must match to be valid", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "I9JhLhnNpWHa": {"__type": "PropParam", "type": {"__ref": "CIEvjXt_qskU"}, "variable": {"__ref": "39qqBLJJb1pS"}, "uuid": "6bPTqdSOjseK", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The type of data that an input field is expected to handle. It influences the input's behavior, validation, and the kind of interface provided to the user.", "displayName": null, "about": "The type of data that an input field is expected to handle. It influences the input's behavior, validation, and the kind of interface provided to the user.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "gaMVb7Kg2Bcv": {"__type": "PropParam", "type": {"__ref": "eh2UrOgob4FJ"}, "variable": {"__ref": "Z2b4GUtVjn2a"}, "uuid": "KbUWKdp4mgiG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "hint to browsers as to the type of virtual keyboard configuration to use when editing this element or its contents.", "displayName": null, "about": "hint to browsers as to the type of virtual keyboard configuration to use when editing this element or its contents.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "XQIF0uc8H5yQ": {"__type": "PropParam", "type": {"__ref": "ikk3xHuDe20B"}, "variable": {"__ref": "iNDoqmB0Adgd"}, "uuid": "P_ecOhudT2M8", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether to use native HTML form validation to prevent form submission when the value is missing or invalid, or mark the field as required or invalid via ARIA.", "displayName": null, "about": "Whether to use native HTML form validation to prevent form submission when the value is missing or invalid, or mark the field as required or invalid via ARIA.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "V6m8AbI4CXeG": {"__type": "PropParam", "type": {"__ref": "OfutWf1EBk-8"}, "variable": {"__ref": "JYbp4CL7dxYo"}, "uuid": "VuugpagmVQoQ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Guidance as to the type of data expected in the field", "displayName": null, "about": "Guidance as to the type of data expected in the field", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "0jiDRhvy8Tj1": {"__type": "PropParam", "type": {"__ref": "v0S8RVUzWUNc"}, "variable": {"__ref": "FbB_kWZGVdAK"}, "uuid": "U0lVTWOTkcGW", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "hArsGir2njqa": {"__type": "PropParam", "type": {"__ref": "55DGmygxuqXj"}, "variable": {"__ref": "JPlPqLBI0TgR"}, "uuid": "55x0AECtWqG8", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "tA-fXcTnnUEy": {"__type": "PropParam", "type": {"__ref": "fXbrBOcCbPLG"}, "variable": {"__ref": "ao02F-uYMyNz"}, "uuid": "Wz-p22wgailY", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "LLQ9YDZXBDnC": {"__type": "PropParam", "type": {"__ref": "kJOgans-nD3L"}, "variable": {"__ref": "-5pWQRjVk-_S"}, "uuid": "pxFwfMGnAZBh", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "IbPXeC7UusRI": {"__type": "PropParam", "type": {"__ref": "wxdYAXW4dyBk"}, "variable": {"__ref": "u1c6yFMqwofr"}, "uuid": "8GtAB4nYBNIc", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "nBME0I8-RKdu": {"__type": "PropParam", "type": {"__ref": "62M1HIkmW5_W"}, "variable": {"__ref": "m9hTZPhoUM8y"}, "uuid": "Ou4h3WWhTza4", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "RwPD3dPWmSo3": {"__type": "PropParam", "type": {"__ref": "SVT7qumM6IXL"}, "variable": {"__ref": "iRguGR4pEn5p"}, "uuid": "6k6lRLzN-6ra", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "s_JRsXJIfEtz": {"__type": "PropParam", "type": {"__ref": "84WN68EnUzto"}, "variable": {"__ref": "uDOlyPdOITIY"}, "uuid": "6tvIgxO6yQRn", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "8jYgpV4uAKHT": {"__type": "PropParam", "type": {"__ref": "qCcmIEg78jI6"}, "variable": {"__ref": "WvMUC-83K1pL"}, "uuid": "hDRWj-rsWzk0", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "Wlfkpj2bA1JC": {"__type": "PropParam", "type": {"__ref": "hraHKjEhizJ4"}, "variable": {"__ref": "OsrelbxA-WYt"}, "uuid": "6Ow0k3uJLAgE", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "-i7-W1rtWkUt": {"__type": "PropParam", "type": {"__ref": "Tv2FDjMEP0MQ"}, "variable": {"__ref": "L3BEJCfUDmqZ"}, "uuid": "402WM0qFhvf3", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "EyVLrbkz2huj": {"__type": "PropParam", "type": {"__ref": "ZQvUj2wqop4x"}, "variable": {"__ref": "GKWSrcy8x-_V"}, "uuid": "C70wyzL-wdEk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "Ys7JPbKbsfPc": {"__type": "PropParam", "type": {"__ref": "K86gewW5yTWk"}, "variable": {"__ref": "wcSS85kesSRr"}, "uuid": "XQZFMeQ94nCS", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "k_0lADVc1q8i": {"__type": "PropParam", "type": {"__ref": "PYu5KgDsyCRO"}, "variable": {"__ref": "Jr5TrZOSPNqv"}, "uuid": "cn6ydt-zcpkb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "zYztCTglA05U": {"__type": "PropParam", "type": {"__ref": "As3rk5BDt9Sg"}, "variable": {"__ref": "AVt2pM8duhdk"}, "uuid": "sA0eBk_9RYPk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "HImE9S8NWqDd": {"__type": "SlotParam", "type": {"__ref": "m4PeFvBRyJq6"}, "tplSlot": {"__ref": "evtgdvlEQnsa"}, "variable": {"__ref": "_wrwJQ3STZMh"}, "uuid": "JfuQUsKhu9CB", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "KCMt8FhBdKf2": {"__type": "PropParam", "type": {"__ref": "Pkz8EymoniYa"}, "variable": {"__ref": "ovf9Sun8wkQ1"}, "uuid": "0hLQr7aoGW55", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the input value is invalid", "displayName": "Invalid", "about": "Whether the input value is invalid", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "1magH2O-EAbn": {"__type": "PropParam", "type": {"__ref": "v37DOIFIb0Yd"}, "variable": {"__ref": "xLgTmTZQYnn9"}, "uuid": "uWegZdUfcvom", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Errors for custom validation", "displayName": null, "about": "Errors for custom validation", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "tVPmrzOjoZGk": {"__type": "NamedState", "variableType": "text", "name": "value", "param": {"__ref": "ksOo_v_gVFw-"}, "accessType": "writable", "onChangeParam": {"__ref": "0jiDRhvy8Tj1"}, "tplNode": null, "implicitState": null}, "KIeELUFBH5Hj": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "evtgdvlEQnsa"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "mI12O0eVGNF-", "parent": null, "locked": null, "vsettings": [{"__ref": "FU8Q-Gzer9bZ"}]}, "IdcdHSnSjaIA": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "Q7JW7BCuHMJJ", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "8oL8yW0h89QN": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerTextField", "defaultExport": false, "displayName": "Aria <PERSON>F<PERSON>", "importName": "BaseTextField", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"children": {"type": "vbox", "styles": {"justifyContent": "flex-start", "alignItems": "flex-start", "width": "300px", "gap": "5px", "padding": 0}, "children": [{"type": "component", "name": "plasmic-react-aria-label", "props": {"children": {"type": "text", "value": "Label"}}}, {"type": "component", "name": "plasmic-react-aria-input", "styles": {"width": "100%"}}, {"type": "component", "name": "plasmic-react-aria-description", "props": {"children": {"type": "text", "value": "Type something..."}}}]}}, "variants": {"disabled": {"__ref": "CLMwkMtLyQBO"}, "readonly": {"__ref": "_D4SoskZ34qs"}}, "refActions": []}, "1br4K9-A-4KI": {"__type": "SlotParam", "type": {"__ref": "2NN_vqDayBK0"}, "tplSlot": {"__ref": "lYTQpzOmQnD0"}, "variable": {"__ref": "roq3JtC0pqAM"}, "uuid": "6GcdfB2hQZ11", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "e9FQDILaYbT0": {"__type": "PropParam", "type": {"__ref": "tCuyhysU37xM"}, "variable": {"__ref": "PjPH1fclIyi1"}, "uuid": "PQVww_Io_r-R", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "<PERSON><PERSON> Overlay", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "ew_18CMPUBat": {"__type": "StateParam", "type": {"__ref": "Q4JjCAknBIKR"}, "state": {"__ref": "fcfA-yA3-bCY"}, "variable": {"__ref": "naHW73VEN2iB"}, "uuid": "IWQIp-dOP5UY", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "b5rjC1pNSBz-"}, "previewExpr": null, "propEffect": "defaultOpen", "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "z4OvkRJ1viw3": {"__type": "PropParam", "type": {"__ref": "bZxzurABDWfN"}, "variable": {"__ref": "Xil6xjHHhp-g"}, "uuid": "xnD97woLomIU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether to close the modal when the user interacts outside it.", "displayName": null, "about": "Whether to close the modal when the user interacts outside it.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "GE-5s3Oqsous": {"__type": "PropParam", "type": {"__ref": "5j-SUQRER_lZ"}, "variable": {"__ref": "OGs6yq-cNpfI"}, "uuid": "0QwKgmIINP2u", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether pressing the escape key to close the modal should be disabled.", "displayName": null, "about": "Whether pressing the escape key to close the modal should be disabled.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "Cx-p60wNsUOX": {"__type": "PropParam", "type": {"__ref": "TjWfNNumhxqB"}, "variable": {"__ref": "r0bm0WDJAD3y"}, "uuid": "JNjMcKKT3H8Z", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "UBvedGHK9mBP": {"__type": "PropParam", "type": {"__ref": "zRW7QgE8l45a"}, "variable": {"__ref": "ER36WCCloIJo"}, "uuid": "UmH-eAiwyB6j", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "fcfA-yA3-bCY": {"__type": "NamedState", "variableType": "boolean", "name": "isOpen", "param": {"__ref": "ew_18CMPUBat"}, "accessType": "writable", "onChangeParam": {"__ref": "Cx-p60wNsUOX"}, "tplNode": null, "implicitState": null}, "Nd-LOkzWtFKY": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "lYTQpzOmQnD0"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "SMAeooKx8Z3f", "parent": null, "locked": null, "vsettings": [{"__ref": "_9OVhzVQsQWv"}]}, "Bjzxxbl0bfKM": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "Yvvjgq5bNuaE", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "5QfVILvE7Is_": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerModal", "defaultExport": false, "displayName": "<PERSON>", "importName": "BaseModal", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "iNH7uRErx18k"}, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": true, "isRepeatable": true, "styleSections": true, "helpers": null, "defaultSlotContents": {"children": {"type": "vbox", "styles": {"width": "stretch", "padding": 0, "gap": "10px", "justifyContent": "flex-start", "alignItems": "flex-start"}, "children": [{"type": "component", "name": "plasmic-react-aria-heading"}, {"type": "text", "value": "This is a Modal!"}, {"type": "text", "value": "You can put anything you can imagine here!", "styles": {"fontWeight": 500}}, {"type": "text", "value": "Use it in a `Aria Dialog Trigger` component to trigger it on a button click!"}]}}, "variants": {}, "refActions": []}, "lS-Qj3908pj8": {"__type": "SlotParam", "type": {"__ref": "PWEkwyABeyEq"}, "tplSlot": {"__ref": "B0_PDf44-JQQ"}, "variable": {"__ref": "1e8PM0pe2vg5"}, "uuid": "N3IdItCJuYvw", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "<PERSON><PERSON>", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "5C_9gv8wP8K8": {"__type": "SlotParam", "type": {"__ref": "ueNDGiZ6quru"}, "tplSlot": {"__ref": "5fMSofn20pBo"}, "variable": {"__ref": "clbcctroiUwj"}, "uuid": "RNuM2yOye_mi", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Tooltip Content", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "9cakiqAuJ1u-": {"__type": "PropParam", "type": {"__ref": "IurztkfPLH-O"}, "variable": {"__ref": "8Rjo_IQVp0f0"}, "uuid": "LjCEWbfKwb_3", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "IKJJOW69Bee7": {"__type": "PropParam", "type": {"__ref": "iDXGRdGnyHQG"}, "variable": {"__ref": "X_D3ps7IRUzV"}, "uuid": "WflhM6aQG5_y", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "rx7kSvIBSdSf": {"__type": "PropParam", "type": {"__ref": "MAAmAyi9KYtI"}, "variable": {"__ref": "hkdDizMmPlyV"}, "uuid": "kuBnVYeHB__U", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "EF6nKG6el95I"}, "previewExpr": null, "propEffect": null, "description": "The delay (in milliseconds) for the tooltip to show up.", "displayName": null, "about": "The delay (in milliseconds) for the tooltip to show up.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "wFClJulX6dq-": {"__type": "PropParam", "type": {"__ref": "fcI2NcJGUIns"}, "variable": {"__ref": "NkD1fsYc3fl6"}, "uuid": "8U-vjKk9B7qY", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "_Lt-<PERSON><PERSON><PERSON>Z<PERSON>7"}, "previewExpr": null, "propEffect": null, "description": "The delay (in milliseconds) for the tooltip to close.", "displayName": null, "about": "The delay (in milliseconds) for the tooltip to close.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "pvZe4FnLtgY6": {"__type": "PropParam", "type": {"__ref": "FKAJ_9paC6Rv"}, "variable": {"__ref": "He1WyGRpaNM6"}, "uuid": "T4M70L6bjRJX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "GopMTiyps_RM": {"__type": "PropParam", "type": {"__ref": "a4qHqsETEBCU"}, "variable": {"__ref": "W1MbEvt10iD5"}, "uuid": "Q9MveffMh9eN", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Default placement of the popover relative to the trigger, if there is enough space", "displayName": null, "about": "Default placement of the popover relative to the trigger, if there is enough space", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "y6yHoorvkTgq": {"__type": "PropParam", "type": {"__ref": "T1C6ayqAU_YI"}, "variable": {"__ref": "1sMB6-FBm7st"}, "uuid": "uFAAZyvAD2Ce", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Additional offset applied along the main axis between the popover and its trigger", "displayName": "Offset", "about": "Additional offset applied along the main axis between the popover and its trigger", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "AGaoy-yLimiu": {"__type": "PropParam", "type": {"__ref": "fBeMY84_yrNT"}, "variable": {"__ref": "ARX5FNJrddE4"}, "uuid": "brVaPiZ9ll9u", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The padding that should be applied between the popover and its surrounding container. This affects the positioning breakpoints that determine when it will attempt to flip.", "displayName": null, "about": "The padding that should be applied between the popover and its surrounding container. This affects the positioning breakpoints that determine when it will attempt to flip.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "SMz0E91xfeA9": {"__type": "PropParam", "type": {"__ref": "irkVCMfmozUd"}, "variable": {"__ref": "sCRmqwpHiFHK"}, "uuid": "AFsdmxA9rUhk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The additional offset applied along the cross axis between the popover and its anchor element.", "displayName": null, "about": "The additional offset applied along the cross axis between the popover and its anchor element.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "Q1k-9-wI5uAX": {"__type": "StateParam", "type": {"__ref": "RZHt-_3WzzVK"}, "state": {"__ref": "q5qdGHZlv36r"}, "variable": {"__ref": "_EhHr1S_q-9h"}, "uuid": "CYqv5VeQL8d7", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": "defaultOpen", "description": "Whether the overlay is open by default", "displayName": null, "about": "Whether the overlay is open by default", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "Mguzf6FO62RQ": {"__type": "PropParam", "type": {"__ref": "HsJHscpH32dz"}, "variable": {"__ref": "Ni7flbqOgTlv"}, "uuid": "rYgjZxe-CxNs", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "q5qdGHZlv36r": {"__type": "NamedState", "variableType": "boolean", "name": "isOpen", "param": {"__ref": "Q1k-9-wI5uAX"}, "accessType": "writable", "onChangeParam": {"__ref": "Mguzf6FO62RQ"}, "tplNode": null, "implicitState": null}, "GOLxeZCIJoXF": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "B0_PDf44-JQQ"}, {"__ref": "5fMSofn20pBo"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "W2wYDIdY4F_F", "parent": null, "locked": null, "vsettings": [{"__ref": "dYfaJR_wYsi1"}]}, "fTwOQEtzhW4M": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "AV4vTJgcQbno", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "NTYpn3pxiEuz": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerTooltip", "defaultExport": false, "displayName": "<PERSON>", "importName": "BaseTooltip", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": true, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {"children": {"type": "text", "value": "Hover me!", "styles": {"width": "hug"}}, "tooltipContent": {"type": "text", "value": "Hello from Tooltip!", "styles": {"background": "black", "color": "white", "padding": "7px", "borderRadius": "7px"}}}, "variants": {"placementTop": {"__ref": "-q6hj8NuwT3b"}, "placementBottom": {"__ref": "GYD0ufF0cCb9"}, "placementLeft": {"__ref": "61UsbUwssOYH"}, "placementRight": {"__ref": "aTDmCvyCVnUW"}}, "refActions": []}, "TpGiUPZY8zXM": {"__type": "SlotParam", "type": {"__ref": "ujcdmqbQTMix"}, "tplSlot": {"__ref": "k2u1TKeMT4Nb"}, "variable": {"__ref": "s7c4K7p3y2ln"}, "uuid": "ArY9SaejpJYk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "7UYhJ8OM4Jyn": {"__type": "SlotParam", "type": {"__ref": "en8fP03ewSC4"}, "tplSlot": {"__ref": "uijThjpZL-Hu"}, "variable": {"__ref": "HI2wgPj17d9e"}, "uuid": "AeT8K0Hltu4m", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "6s1FyXsphxs6": {"__type": "StateParam", "type": {"__ref": "xxtp6IvTz8mq"}, "state": {"__ref": "ahHO-fTK7Bae"}, "variable": {"__ref": "0FN2x9XQ3Qgm"}, "uuid": "eZt8ax5t9rAn", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": "defaultOpen", "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "8xEmkimGBoJg": {"__type": "PropParam", "type": {"__ref": "bdmABT4rGx2w"}, "variable": {"__ref": "bqU9oCyZxONd"}, "uuid": "cRlgrWnKEJFa", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "ahHO-fTK7Bae": {"__type": "NamedState", "variableType": "boolean", "name": "isOpen", "param": {"__ref": "6s1FyXsphxs6"}, "accessType": "writable", "onChangeParam": {"__ref": "8xEmkimGBoJg"}, "tplNode": null, "implicitState": null}, "b8CA_B1dUdHr": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "k2u1TKeMT4Nb"}, {"__ref": "uijThjpZL-Hu"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "JCio2kK5h-oi", "parent": null, "locked": null, "vsettings": [{"__ref": "tITUd8E2M1Mk"}]}, "Qdams5aqVXe_": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "OyMBl5-VaCk-", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "P4qvCGrY8at5": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerDialogTrigger", "defaultExport": false, "displayName": "Aria Dialog <PERSON>", "importName": "BaseDialogTrigger", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {"trigger": {"type": "component", "name": "plasmic-react-aria-button", "props": {"children": {"type": "text", "value": "Open Dialog"}}}, "dialog": [{"type": "component", "name": "plasmic-react-aria-modal", "props": {"children": {"type": "component", "name": "plasmic-react-aria-dialog", "props": {"children": {"type": "vbox", "styles": {"width": "stretch", "padding": 0, "gap": "10px", "justifyContent": "flex-start", "alignItems": "flex-start"}, "children": [{"type": "component", "name": "plasmic-react-aria-heading"}, {"type": "text", "value": "This is a Modal!"}, {"type": "text", "value": "You can put anything you can imagine here!", "styles": {"fontWeight": 500}}, {"type": "text", "value": "Use it in a `Aria Dialog Trigger` component to trigger it on a button click!"}]}}}}}]}, "variants": {}, "refActions": []}, "bY3H2VzK-zzh": {"__type": "SlotParam", "type": {"__ref": "kA-1kQ4F0jjz"}, "tplSlot": {"__ref": "Fi-T6DQYmhLD"}, "variable": {"__ref": "mAgaCtsvf5NW"}, "uuid": "Esr9tO8Myo8F", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "55Pzq4Iq4kTE": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "Fi-T6DQYmhLD"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "vS7Q1AF8P_DU", "parent": null, "locked": null, "vsettings": [{"__ref": "EfBijOC7hxZe"}]}, "z77jXHviIbWx": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "5iZSHdfs9hEa", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "SZ_Pl6qWZJ_E": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerSliderOutput", "defaultExport": false, "displayName": "Aria Slider Output", "importName": "BaseSliderOutput", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {"disabled": {"__ref": "IMoXvFpJ5bst"}}, "refActions": []}, "bGRZWJztFeCk": {"__type": "PropParam", "type": {"__ref": "_MSZSei-Jd9h"}, "variable": {"__ref": "10s-vcKvUNit"}, "uuid": "-sKfL8jA-Tnu", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Name for this field if it is part of a form", "displayName": "Form field key", "about": "Name for this field if it is part of a form", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "ztWdfzxSKDJu": {"__type": "PropParam", "type": {"__ref": "D3DYzdhj6JEK"}, "variable": {"__ref": "STCCF6wjECA6"}, "uuid": "R7qSzjC9Y5y0", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the slider thumb is read-only and unfocusable", "displayName": "Disabled", "about": "Whether the slider thumb is read-only and unfocusable", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "cYi6TixAJpii": {"__type": "PropParam", "type": {"__ref": "hSolk6RN9zER"}, "variable": {"__ref": "XGPa1e4G2os7"}, "uuid": "k7RVDjW1GZCm", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the slider thumb should be focused when rendered", "displayName": null, "about": "Whether the slider thumb should be focused when rendered", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "YnkBxEAOed6b": {"__type": "PropParam", "type": {"__ref": "g-PEbNFk3FCg"}, "variable": {"__ref": "Td9Dk9lBX1W6"}, "uuid": "hiksNUAKpjTK", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Enables the children slot for creating a more customized thumb", "displayName": "Advanced", "about": "Enables the children slot for creating a more customized thumb", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "66iDNAtM0XDs": {"__type": "SlotParam", "type": {"__ref": "rxmu9-Ivl4AJ"}, "tplSlot": {"__ref": "RLIxz_Xg8_JI"}, "variable": {"__ref": "uriIWitURGwf"}, "uuid": "lkGWgXDeaHek", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "1VHPDxXudeyv": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "RLIxz_Xg8_JI"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "7Mv6AVMppNeQ", "parent": null, "locked": null, "vsettings": [{"__ref": "eiE-rZYVc-tj"}]}, "ezWNyNKnPxW2": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "1UgjDI90QzAu", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "--jFi7qwrwBW": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerSliderThumb", "defaultExport": false, "displayName": "<PERSON> Slider Thumb", "importName": "BaseSliderThumb", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "OUQkrDjPcbPs"}, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {"dragging": {"__ref": "dWBH-UvJeMEJ"}, "hovered": {"__ref": "6tYdgtp7Lwx9"}, "focused": {"__ref": "cgK5F-K1iiSA"}, "focusVisible": {"__ref": "iY9UAUeaPIwF"}, "disabled": {"__ref": "ZBkt5BpxO8_4"}}, "refActions": []}, "FloImOO5Mh19": {"__type": "SlotParam", "type": {"__ref": "XOZxT7L-bAf_"}, "tplSlot": {"__ref": "d4FCMKYc_fqh"}, "variable": {"__ref": "e7sXaqcp-beG"}, "uuid": "aQuHe27e1wRL", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The thumbs of the slider. For range slider, you can add more than one thumb.", "displayName": "Thumbs", "about": "The thumbs of the slider. For range slider, you can add more than one thumb.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "BUAMaP0Ml2L8": {"__type": "SlotParam", "type": {"__ref": "UzVfoEieCLrS"}, "tplSlot": {"__ref": "gtNkHfwQLz0g"}, "variable": {"__ref": "chUhrBy1HrN3"}, "uuid": "pQygreI6XMOJ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Progress Bar", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false}, "3iF95mjXxoTt": {"__type": "PropParam", "type": {"__ref": "RtfpbVdU140B"}, "variable": {"__ref": "Q_n10gtbZcnH"}, "uuid": "YX971eHigEA_", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "FuoTpciDNPnZ": {"__type": "PropParam", "type": {"__ref": "oChVQBqaRdw1"}, "variable": {"__ref": "MgkEkCgXREFK"}, "uuid": "BAjyjSQqTo40", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "rQ6WSwx-zI2P": {"__type": "PropParam", "type": {"__ref": "BEhxTwLrERIy"}, "variable": {"__ref": "cC-s9jey48xj"}, "uuid": "xBux7YUdKxi6", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "ZKByXjXlu_do": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "d4FCMKYc_fqh"}, {"__ref": "gtNkHfwQLz0g"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "WXpsu1Ha8rGX", "parent": null, "locked": null, "vsettings": [{"__ref": "wy_-Mswp_EAU"}]}, "k3bYp7zKIlx4": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "VnDQU6a6pksa", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "PGyYJ8l05Oie": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerSliderTrack", "defaultExport": false, "displayName": "Aria Slider Track", "importName": "BaseSliderTrack", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "ijZ_5vXzFBlb"}, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"children": [{"type": "component", "name": "plasmic-react-aria-slider-sliderThumb"}], "progressBar": [{"type": "box", "styles": {"height": "100%", "width": "100%", "backgroundColor": "#ffa6a6", "padding": 0}}]}, "variants": {"hovered": {"__ref": "vIAC8hVdwW2M"}}, "refActions": []}, "J5vy-5dmOvWs": {"__type": "PropParam", "type": {"__ref": "BrZIVopt2NKk"}, "variable": {"__ref": "HmhoLdkOkqfM"}, "uuid": "kBmgdcrmmwmy", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the slider is read-only and unfocusable", "displayName": "Disabled", "about": "Whether the slider is read-only and unfocusable", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "AYRzuYV179-a": {"__type": "PropParam", "type": {"__ref": "SjZqBYreLW2t"}, "variable": {"__ref": "LHpLx3F2oVQG"}, "uuid": "zjCGLpgJ0D_K", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Assistive technology uses this if there is no visible label for this slider", "displayName": "ARIA label", "about": "Assistive technology uses this if there is no visible label for this slider", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "INX7lV-KHiUW": {"__type": "PropParam", "type": {"__ref": "0lrqngB69E_s"}, "variable": {"__ref": "-GLK-YKDzKUB"}, "uuid": "IPk5sG09o-vv", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "ls345aOF_Uyg"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "60uGR06bTBgb": {"__type": "PropParam", "type": {"__ref": "ha09dlrgaXch"}, "variable": {"__ref": "UfcOXkyZ326s"}, "uuid": "de0gVFqCqRkA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The minimum value of the slider", "displayName": null, "about": "The minimum value of the slider", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "CIqvFRE2qnoI": {"__type": "PropParam", "type": {"__ref": "qomxA-F0ih8e"}, "variable": {"__ref": "7JClTjtf3-MR"}, "uuid": "-_HL8jPqGJkS", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The maximum value of the slider", "displayName": null, "about": "The maximum value of the slider", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "U5nh5L2ES_sU": {"__type": "PropParam", "type": {"__ref": "vrtz-YpXHxpa"}, "variable": {"__ref": "LrwA8ktG32Ba"}, "uuid": "5ZRuT3q70Def", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The step value of the slider", "displayName": null, "about": "The step value of the slider", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "CHEQyE7rTj-y": {"__type": "StateParam", "type": {"__ref": "l0SSrerv7e4s"}, "state": {"__ref": "FeJdXgPcaOX3"}, "variable": {"__ref": "Qf9sDBnjOxbK"}, "uuid": "j7SYxMm2-BRk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "qHyTftIa0KAI"}, "previewExpr": null, "propEffect": "defaultValue", "description": "The intial value of the slider", "displayName": "Initial value", "about": "The intial value of the slider", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "kuKtXBRtcaKS": {"__type": "SlotParam", "type": {"__ref": "xjTrfF6oIOOC"}, "tplSlot": {"__ref": "hKivXUVGZgVE"}, "variable": {"__ref": "hh4oPONCcEnS"}, "uuid": "LK70A4fcUB30", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "jwri07I7H9s2": {"__type": "PropParam", "type": {"__ref": "dRqeNMBSdVl2"}, "variable": {"__ref": "QHG-txxJqR6X"}, "uuid": "sdqU358HYnG8", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "9hmzkyQVtQtJ": {"__type": "PropParam", "type": {"__ref": "7ybIiwT3j-Wr"}, "variable": {"__ref": "tSndEU00eXWV"}, "uuid": "b7ONaTcAQ2X0", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "FeJdXgPcaOX3": {"__type": "NamedState", "variableType": "array", "name": "value", "param": {"__ref": "CHEQyE7rTj-y"}, "accessType": "writable", "onChangeParam": {"__ref": "jwri07I7H9s2"}, "tplNode": null, "implicitState": null}, "Vut3qb93ALXn": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "hKivXUVGZgVE"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "ZBRKMYZ4TzD9", "parent": null, "locked": null, "vsettings": [{"__ref": "DNxngXC0fXRl"}]}, "b8RqNI6qtM1s": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "rll_gswfl0_x", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "nv8RZuTXE0mu": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerSlider", "defaultExport": false, "displayName": "Aria Range Slider", "importName": "BaseSlider", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "VjoEtY3euJhB"}, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"children": [{"type": "hbox", "styles": {"width": "stretch", "justifyContent": "space-between", "padding": "8px 0px"}, "children": [{"type": "component", "name": "plasmic-react-aria-label", "props": {"children": {"type": "text", "value": "Label"}}}, {"type": "component", "name": "plasmic-react-aria-slider-sliderOutput", "props": {"children": {"type": "text", "value": "Output"}}}]}, {"type": "component", "name": "plasmic-react-aria-slider-sliderTrack", "props": {"children": [{"type": "component", "name": "plasmic-react-aria-slider-sliderThumb"}, {"type": "component", "name": "plasmic-react-aria-slider-sliderThumb", "styles": {"backgroundColor": "blue"}}]}}]}, "variants": {"disabled": {"__ref": "cyYXgpqD-224"}}, "refActions": []}, "UVE3lCFVz7Q9": {"__type": "PropParam", "type": {"__ref": "s2UKZx42aXAO"}, "variable": {"__ref": "RasmWHASZVhJ"}, "uuid": "D-5g8lXQ-uAr", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Whether the slider is read-only and unfocusable", "displayName": "Disabled", "about": "Whether the slider is read-only and unfocusable", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "CiGQk81yZJZu": {"__type": "PropParam", "type": {"__ref": "OJAMDFVCVu_n"}, "variable": {"__ref": "62QsTaIqc9o4"}, "uuid": "Q7-2QQeFYpci", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Assistive technology uses this if there is no visible label for this slider", "displayName": "ARIA label", "about": "Assistive technology uses this if there is no visible label for this slider", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "R_14QGonhwXE": {"__type": "PropParam", "type": {"__ref": "N5bL4f6kZf6b"}, "variable": {"__ref": "JNFwjdvKc6P6"}, "uuid": "2gqen3AI5tkx", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "EHBO1F3b2yF2"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "Gl71sJP137Ku": {"__type": "PropParam", "type": {"__ref": "DWIFvyCtlJMU"}, "variable": {"__ref": "FKfQjTJQCFMn"}, "uuid": "IlFbePgso8YW", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The minimum value of the slider", "displayName": null, "about": "The minimum value of the slider", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "nopbxlBfRiyW": {"__type": "PropParam", "type": {"__ref": "RuBRPCwBZJSS"}, "variable": {"__ref": "45bKS-GrHA74"}, "uuid": "hCxxGBsrJ1L1", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The maximum value of the slider", "displayName": null, "about": "The maximum value of the slider", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "lSgzothCesfj": {"__type": "PropParam", "type": {"__ref": "6JzSqk1r3Ost"}, "variable": {"__ref": "Hw75gRubxJKg"}, "uuid": "9u7BfbFZiRm1", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "The step value of the slider", "displayName": null, "about": "The step value of the slider", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "YKmpJEhO_UIG": {"__type": "SlotParam", "type": {"__ref": "XflF2_zkoV2m"}, "tplSlot": {"__ref": "E_t-tCco_hFf"}, "variable": {"__ref": "Q-6NHccz1Qm7"}, "uuid": "sHnHBNORcJMl", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "xV9gFrR76sAK": {"__type": "StateParam", "type": {"__ref": "8ew-kQBAofF-"}, "state": {"__ref": "AJwxak35fqGJ"}, "variable": {"__ref": "Qe87ONt18-AR"}, "uuid": "Om7lQrceW9KD", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "WnsSlJpXQ0zH"}, "previewExpr": null, "propEffect": "defaultValue", "description": "The initial value of the slider", "displayName": "Initial value", "about": "The initial value of the slider", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "ungSok76jtwV": {"__type": "PropParam", "type": {"__ref": "yyRwe-lqlSje"}, "variable": {"__ref": "qV2M7a4Q_2IG"}, "uuid": "iN80KilNBh9x", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "dnkyMl2yYr7G": {"__type": "PropParam", "type": {"__ref": "WCY6ty33AgHJ"}, "variable": {"__ref": "5JWKIS4vKqKn"}, "uuid": "Jh09G7hIYGNW", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false}, "AJwxak35fqGJ": {"__type": "NamedState", "variableType": "number", "name": "value", "param": {"__ref": "xV9gFrR76sAK"}, "accessType": "writable", "onChangeParam": {"__ref": "ungSok76jtwV"}, "tplNode": null, "implicitState": null}, "fqFF4RipQaX4": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "E_t-tCco_hFf"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Q2ljDG-G7_FW", "parent": null, "locked": null, "vsettings": [{"__ref": "1J4jxO7qpK5q"}]}, "Uuaz9SnkNpB0": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "BZ1ohTiVVmPI", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "TSj4hE747s6j": {"__type": "CodeComponentMeta", "importPath": "@plasmicpkgs/react-aria/skinny/registerSlider", "defaultExport": false, "displayName": "<PERSON>", "importName": "BaseSlider", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "F-5Q1faKcB5E"}, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"children": [{"type": "hbox", "styles": {"width": "stretch", "justifyContent": "space-between", "padding": "8px 0px"}, "children": [{"type": "component", "name": "plasmic-react-aria-label", "props": {"children": {"type": "text", "value": "Label"}}}, {"type": "component", "name": "plasmic-react-aria-slider-sliderOutput", "props": {"children": {"type": "text", "value": "Output"}}}]}, {"type": "component", "name": "plasmic-react-aria-slider-sliderTrack"}]}, "variants": {"disabled": {"__ref": "f4PgX4asvCjL"}}, "refActions": []}, "7eWz-PjYEr8F": {"__type": "TplTag", "tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "VQjBJGYX-dh-", "parent": null, "locked": null, "vsettings": [{"__ref": "UzmW-J5TAbOm"}]}, "7BOkqiB7Zne7": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "vQzw1wJzSIvH", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "Q97QBhR4qzQ5": {"__type": "CodeComponentMeta", "importPath": "./components/Counter", "defaultExport": false, "displayName": null, "importName": null, "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": true, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": []}, "gFsR5kx2FRUy": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "EuMrphw5O8RY": {"__type": "Var", "name": "children", "uuid": "KrkafH7t4QY4"}, "XWWg-oC-tfWS": {"__type": "Choice", "name": "choice", "options": ["label", "description"]}, "Movlw3_hea0J": {"__type": "Var", "name": "slot", "uuid": "h_RJa3B2LedK"}, "dxDiej9oxSFJ": {"__type": "CustomCode", "code": "\"label\"", "fallback": null}, "U3FV0e9uZjxy": {"__type": "TplSlot", "param": {"__ref": "xzhIZOtTVlLY"}, "defaultContents": [], "uuid": "2vP6YiEXu_iR", "parent": {"__ref": "Gd8vUVqlHwMz"}, "locked": null, "vsettings": [{"__ref": "4k3P_W1dfHau"}]}, "tFCS_PKlU0lT": {"__type": "VariantSetting", "variants": [{"__ref": "dGaJ3dAGvUvJ"}], "args": [], "attrs": {}, "rs": {"__ref": "3QWghINpqQ2A"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "0S0RSrYtx8Bp": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "WxY_NznGwG7i": {"__type": "Var", "name": "children", "uuid": "ns37YR7OM3q1"}, "lMJsosksYcF7": {"__type": "Text", "name": "text"}, "5hptDbtVcEle": {"__type": "Var", "name": "slot", "uuid": "HGJSWnQRPFG2"}, "KavzKlYgKFpl": {"__type": "CustomCode", "code": "\"title\"", "fallback": null}, "uEtAK9VLi0mn": {"__type": "TplSlot", "param": {"__ref": "v7hIyuJ0crPn"}, "defaultContents": [], "uuid": "80cB5BBQCq_s", "parent": {"__ref": "fVleBC21Wxfj"}, "locked": null, "vsettings": [{"__ref": "PCzgpU9N2Zq-"}]}, "3jPCsFfU4Vc-": {"__type": "VariantSetting", "variants": [{"__ref": "RXqLI_eLgPt7"}], "args": [], "attrs": {}, "rs": {"__ref": "fkSIJY3I-oRQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "107k7j7RG3cK": {"__type": "RuleSet", "values": {"font-size": "20px", "font-weight": "bold", "margin-bottom": "10px"}, "mixins": []}, "w02JB_4gwzXh": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "keA-DcmTwzAQ": {"__type": "Var", "name": "children", "uuid": "FEBGLbKGpGCT"}, "NpnnbFDGDYWy": {"__type": "Text", "name": "text"}, "lD8DZxqCwvoq": {"__type": "Var", "name": "slot", "uuid": "Xjg18Ve5dLyH"}, "PBmfChuoyaNN": {"__type": "CustomCode", "code": "\"description\"", "fallback": null}, "cthBJY7tIEiq": {"__type": "TplSlot", "param": {"__ref": "KduweZF6b2Au"}, "defaultContents": [], "uuid": "p6-PW48vA3tS", "parent": {"__ref": "rkLub4IQr-KE"}, "locked": null, "vsettings": [{"__ref": "gXtkPU1wCZOS"}]}, "87AlTZtsW3KZ": {"__type": "VariantSetting", "variants": [{"__ref": "7Lovrtb2Lat1"}], "args": [], "attrs": {}, "rs": {"__ref": "_za__IlAxSPN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "owCaAL_tscsG": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "IZhfXHGU5qAI": {"__type": "Var", "name": "children", "uuid": "x33mvVD8Azty"}, "Wl2GxtXfb_lv": {"__type": "TplSlot", "param": {"__ref": "jNXtIh6qmpD5"}, "defaultContents": [], "uuid": "OgkL52AOjx5o", "parent": {"__ref": "eZbZlxF59e-R"}, "locked": null, "vsettings": [{"__ref": "5BHVcLNwjiSC"}]}, "UhtBKl7MYy7Y": {"__type": "VariantSetting", "variants": [{"__ref": "PzHz0FefVgTI"}], "args": [], "attrs": {}, "rs": {"__ref": "IV9o9adoF2pL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "iPXAC3gT-_tJ": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "5AuajdEq3_yu": {"__type": "Var", "name": "children", "uuid": "T8tdynClg45a"}, "pw-RwkS5GAZl": {"__type": "TplSlot", "param": {"__ref": "i2MY6DAp4qsC"}, "defaultContents": [], "uuid": "QFSdTn0F-6NM", "parent": {"__ref": "7ZfHZKMOlE1H"}, "locked": null, "vsettings": [{"__ref": "18BG8n33mPCX"}]}, "pV2lINbcDVbz": {"__type": "VariantSetting", "variants": [{"__ref": "n5BD8rXzj5QR"}], "args": [], "attrs": {}, "rs": {"__ref": "K6tAk_Ym1FdK"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "aGfVd0s0Yi9Q": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-placement=top]", "displayName": "Placement (Top)"}, "m-h1_w93yZ6O": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-placement=left]", "displayName": "Placement (Left)"}, "CIXFrc4Ijr6h": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-placement=right]", "displayName": "Placement (Right)"}, "BBG1BRtqkVB3": {"__type": "BoolType", "name": "bool"}, "Tqs1FSK3-o4Y": {"__type": "Var", "name": "customize", "uuid": "VZbUtWy_tQVY"}, "jc6wymUKJetp": {"__type": "CustomCode", "code": "true", "fallback": null}, "PnT91rP9FC07": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "hb0R18Z1Y7Sh": {"__type": "Var", "name": "children", "uuid": "mzTIp29St9_M"}, "wdnip2BhEImR": {"__type": "TplSlot", "param": {"__ref": "2DFskbdQy0aO"}, "defaultContents": [], "uuid": "xebgYFQALalD", "parent": {"__ref": "BnJMQqnk_JxB"}, "locked": null, "vsettings": [{"__ref": "hBuZK-PGud-y"}]}, "CuGDTvOwflQV": {"__type": "VariantSetting", "variants": [{"__ref": "EqMkE8AI3N4F"}], "args": [], "attrs": {}, "rs": {"__ref": "BQYkF00jAD5f"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "1v6mxW_yftjZ": {"__type": "Text", "name": "text"}, "rsJMm3aI1wkb": {"__type": "Var", "name": "name", "uuid": "b3DPLL-2SReG"}, "0FsiBOwcU3pp": {"__type": "Text", "name": "text"}, "I-kZWEWBURiv": {"__type": "Var", "name": "aria-label", "uuid": "6cMMoLxNHft8"}, "CU22cC8o4MTi": {"__type": "BoolType", "name": "bool"}, "ggrMiDQpOPMQ": {"__type": "Var", "name": "isDisabled", "uuid": "Jf7Stpm8uSBk"}, "IjtpsdBDzpt5": {"__type": "BoolType", "name": "bool"}, "oonJKJfAecGr": {"__type": "Var", "name": "autoFocus", "uuid": "Dw0wGzcJmB2O"}, "An_dMBZTUhIA": {"__type": "Choice", "name": "choice", "options": ["Dynamic options"]}, "yjCF6tPVM6U8": {"__type": "Var", "name": "<PERSON><PERSON><PERSON>", "uuid": "0PW7DStCKr9q"}, "bFhz2h3VloKW": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "PRLoLNs7vRc7"}]}, "jmjDtb1pdDzV": {"__type": "Var", "name": "onSelectionChange", "uuid": "TCFYaFMIXDhO"}, "apsYnD89D1iU": {"__type": "Choice", "name": "choice", "options": ["Dynamic options"]}, "zvaqMVfiz0gh": {"__type": "Var", "name": "disabled<PERSON><PERSON>s", "uuid": "LOx5BiKvK_pL"}, "4woNmID5nfEV": {"__type": "BoolType", "name": "bool"}, "c8i_jVr53eyw": {"__type": "Var", "name": "isOpen", "uuid": "xU07AV83sB0q"}, "vvZ8rh5VQBQk": {"__type": "CustomCode", "code": "false", "fallback": null}, "nlA8kVM9fnN9": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "PpDaQoUjK_Kz"}]}, "CILxfk4Ae6o_": {"__type": "Var", "name": "onOpenChange", "uuid": "FrzyN9zLDRyQ"}, "jhFNWeC72DWj": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "N6jyRAkZ0Q-I": {"__type": "Var", "name": "children", "uuid": "9KibjspIPgfw"}, "l0wUnAegff5S": {"__type": "TplSlot", "param": {"__ref": "QuQZ-9LzrRUU"}, "defaultContents": [], "uuid": "Y3LxrvQl83lM", "parent": {"__ref": "oeevVi3DMLg5"}, "locked": null, "vsettings": [{"__ref": "hJjwMR3AQjcp"}]}, "CN3Rg-pvyufh": {"__type": "VariantSetting", "variants": [{"__ref": "r6qvgw_GHIEb"}], "args": [], "attrs": {}, "rs": {"__ref": "_HXiAMPDyu9g"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "BordBQlJxaZr": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focused]", "displayName": "Focused"}, "oWem6E5kLLWM": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focus-visible]", "displayName": "Focus Visible"}, "I-M4xgdFSr4p": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-disabled]", "displayName": "Disabled"}, "mK4y1Dsbrm_I": {"__type": "Text", "name": "text"}, "IOiu09f7yG5F": {"__type": "Var", "name": "name", "uuid": "WQ_wkXoLI1C2"}, "19U4cEeLv-uO": {"__type": "Text", "name": "text"}, "uy3tsih2MsEy": {"__type": "Var", "name": "aria-label", "uuid": "XHEoX_xrXuKQ"}, "4iJHi0qEhfpO": {"__type": "BoolType", "name": "bool"}, "TPitDcWVAhrz": {"__type": "Var", "name": "isDisabled", "uuid": "0mm6fI0O_5rh"}, "gibM3ypmOOJ0": {"__type": "Choice", "name": "choice", "options": ["Dynamic options"]}, "EFR4NtkWeTyY": {"__type": "Var", "name": "<PERSON><PERSON><PERSON>", "uuid": "l-_pd6fy5Xsd"}, "EkdF0O_J9Aj6": {"__type": "Choice", "name": "choice", "options": ["Dynamic options"]}, "zzux5vPQja2C": {"__type": "Var", "name": "disabled<PERSON><PERSON>s", "uuid": "fcSV-e0N6WMh"}, "EkT6DMWWzNTg": {"__type": "BoolType", "name": "bool"}, "bk02pXKvUW7X": {"__type": "Var", "name": "isOpen", "uuid": "IvLeOzesWSaQ"}, "BUpJZdh5CxKR": {"__type": "CustomCode", "code": "false", "fallback": null}, "mCnYnvIHeGYY": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "sy8fHIPPcyDL"}]}, "o0y3guzs5cUU": {"__type": "Var", "name": "onSelectionChange", "uuid": "nd0nTqEofBik"}, "7NLP25QnrQi5": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "b_TfCJMUGW5t"}]}, "v6LpNWFaqEr-": {"__type": "Var", "name": "onOpenChange", "uuid": "c4JlD7M46mMs"}, "107UDlzfQsTB": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "a8xxyrLBaq_x": {"__type": "Var", "name": "children", "uuid": "yfgds86HBf8g"}, "yCYQDwxq1xng": {"__type": "TplSlot", "param": {"__ref": "D5LSBZeo8HJO"}, "defaultContents": [], "uuid": "79VueDGaE5Aj", "parent": {"__ref": "l6uA9OWQ8e_M"}, "locked": null, "vsettings": [{"__ref": "mIrEWl4TD5OD"}]}, "7vXpMbdB0LD_": {"__type": "VariantSetting", "variants": [{"__ref": "m-y17Tx0tv99"}], "args": [], "attrs": {}, "rs": {"__ref": "_Vkt4GuQljNt"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "LGvg8H14akbp": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-disabled]", "displayName": "Disabled"}, "h-NciQLfOP0Y": {"__type": "BoolType", "name": "bool"}, "XBWylTcS3qjI": {"__type": "Var", "name": "autoFocus", "uuid": "hNdpw9m5KBS_"}, "ngTTMrCmylQ5": {"__type": "BoolType", "name": "bool"}, "FlBVoapURd7i": {"__type": "Var", "name": "isDisabled", "uuid": "__hnIPgXMOti"}, "nOV4UTWbz62s": {"__type": "Text", "name": "text"}, "w4JSwXCRg4Wm": {"__type": "Var", "name": "aria-label", "uuid": "J4QCBZuVFSTH"}, "b7lIEBDjgH9M": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "f1cr2I_RYc4X": {"__type": "Var", "name": "children", "uuid": "a1hvdvUg6Ymg"}, "OWQsL007i7WF": {"__type": "HrefType", "name": "href"}, "0acZJXfUQQw6": {"__type": "Var", "name": "href", "uuid": "B5UehdSrN8wO"}, "LxhjEUGMTbl6": {"__type": "Choice", "name": "choice", "options": ["_blank", "_self", "_parent", "_top"]}, "y6bwksY8B8Ez": {"__type": "Var", "name": "target", "uuid": "pfutAPwF7We6"}, "T7R5L3IROLaR": {"__type": "BoolType", "name": "bool"}, "SAk-sCFKzuDm": {"__type": "Var", "name": "submitsForm", "uuid": "8LPPAkRby8qn"}, "XaWoKKFErIZ7": {"__type": "BoolType", "name": "bool"}, "H11yU8RkV_w-": {"__type": "Var", "name": "resetsForm", "uuid": "tPArNR52q6T1"}, "dMo3cWtKus74": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "1E_hs3UIao6C"}]}, "bUoX6mPnWShW": {"__type": "Var", "name": "onPress", "uuid": "KzpRpmaC5o1c"}, "LNJN3o-Y3NYs": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "yZk4Ihe3midI"}]}, "Rn-35nq6z4GS": {"__type": "Var", "name": "onFocus", "uuid": "ZfGhRlutIF1x"}, "Ia26f5w32gPM": {"__type": "TplSlot", "param": {"__ref": "Eqwt1eEARK-2"}, "defaultContents": [], "uuid": "it2wWQdA-3X3", "parent": {"__ref": "J7fi3lvAutOZ"}, "locked": null, "vsettings": [{"__ref": "tIBz6FlUjE34"}]}, "jsFtIgzzG0eA": {"__type": "VariantSetting", "variants": [{"__ref": "3tpDmYiHYGZj"}], "args": [], "attrs": {}, "rs": {"__ref": "68keyo8jMIzx"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "l18hNHPc4XYm": {"__type": "RuleSet", "values": {"padding-top": "2px", "padding-right": "10px", "padding-bottom": "2px", "padding-left": "10px", "border-top-width": "1px", "border-right-width": "1px", "border-bottom-width": "1px", "border-left-width": "1px", "border-top-style": "solid", "border-right-style": "solid", "border-bottom-style": "solid", "border-left-style": "solid", "border-top-color": "black", "border-right-color": "black", "border-bottom-color": "black", "border-left-color": "black", "background": "linear-gradient(#EFEFEF, #EFEFEF)", "color": "#000000", "cursor": "pointer", "font-family": "<PERSON><PERSON>", "font-size": "1rem", "line-height": "1.2", "text-decoration-line": "none"}, "mixins": []}, "uEQ57zNciURJ": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-hovered]", "displayName": "Hovered"}, "iCaekTQa19yE": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-pressed]", "displayName": "Pressed"}, "qv69q2pH2gw_": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focused]", "displayName": "Focused"}, "NJix-KjiMoz2": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focus-visible]", "displayName": "Focus Visible"}, "jHYKLL7P7J3K": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-disabled]", "displayName": "Disabled"}, "RY8GVB_1cjpp": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "RXmtXkYyAmwB": {"__type": "Var", "name": "children", "uuid": "N1rSowcLA5N2"}, "Shn5gE9_ESPv": {"__type": "TplSlot", "param": {"__ref": "ZLVfAvxq6BGG"}, "defaultContents": [], "uuid": "eEVenu_nXu7u", "parent": {"__ref": "b0Gt2-3jAvHh"}, "locked": null, "vsettings": [{"__ref": "YI5KRHKJuhrb"}]}, "7N8EPc3eWqyH": {"__type": "VariantSetting", "variants": [{"__ref": "mEnjH3DU2iQz"}], "args": [], "attrs": {}, "rs": {"__ref": "gEMLLXglxCfz"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "R_Bsfrl7F21a": {"__type": "RuleSet", "values": {"cursor": "pointer"}, "mixins": []}, "DcAjXUJfmngF": {"__type": "Text", "name": "text"}, "aWx8ROAYrFhD": {"__type": "Var", "name": "id", "uuid": "Vzsse0boL7Ec"}, "-O4kqVKNNRu6": {"__type": "Text", "name": "text"}, "M70ruus8uZwK": {"__type": "Var", "name": "textValue", "uuid": "m83gOV_JE9t2"}, "LRH_H8Jbf4nO": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "1BefZagFU6FD": {"__type": "Var", "name": "children", "uuid": "Z7MZFMev4iYx"}, "aD6WoIRO3mRm": {"__type": "TplSlot", "param": {"__ref": "YnGWnntf4Vaw"}, "defaultContents": [], "uuid": "g9h9TeFUVrrS", "parent": {"__ref": "HKJt9kjEpdBs"}, "locked": null, "vsettings": [{"__ref": "gxbm5VXibyze"}]}, "Q4BZSoVEieqH": {"__type": "VariantSetting", "variants": [{"__ref": "GWQygEnv9jqh"}], "args": [], "attrs": {}, "rs": {"__ref": "cz2rg8VvTOiA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "2Ww-GmZbt1MZ": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-hovered]", "displayName": "Hovered"}, "TY6-qFFK9N-j": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-pressed]", "displayName": "Pressed"}, "esesoc6E11ka": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focused]", "displayName": "Focused"}, "zhS_4mn6s2gW": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focus-visible]", "displayName": "Focus Visible"}, "-pWt-ZBaR4-v": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-selected]", "displayName": "Selected"}, "dXnr82cDHpVp": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-disabled]", "displayName": "Disabled"}, "vIx0WW6-1IuE": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "2ruFtl0qRfPP": {"__type": "Var", "name": "header", "uuid": "rcNhUNTD674F"}, "ypuzcUYkIGKa": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "F2IG4lJ7jMzq": {"__type": "Var", "name": "items", "uuid": "KmK7e9eONe9M"}, "8iLi_hPVMzaZ": {"__type": "TplSlot", "param": {"__ref": "wwe8bzYv_tiK"}, "defaultContents": [], "uuid": "B0jL7hKOLJox", "parent": {"__ref": "USpndbark-aW"}, "locked": null, "vsettings": [{"__ref": "bJYfKJv78oZS"}]}, "kdMN63ovNKJS": {"__type": "TplSlot", "param": {"__ref": "JxtNbtwVRIwW"}, "defaultContents": [], "uuid": "1f6jgOlUwsg9", "parent": {"__ref": "USpndbark-aW"}, "locked": null, "vsettings": [{"__ref": "PY5KXo_iOLpO"}]}, "vLuJUw4BqyHO": {"__type": "VariantSetting", "variants": [{"__ref": "fcl12scH4tr-"}], "args": [], "attrs": {}, "rs": {"__ref": "NLyNut7af54b"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "UvazGFJ8tyCV": {"__type": "RuleSet", "values": {"padding-top": "10px", "padding-right": "10px", "padding-bottom": "10px", "padding-left": "10px", "width": "stretch"}, "mixins": []}, "6Bz25Iv7Fiut": {"__type": "RenderableType", "name": "renderable", "params": [{"__ref": "FEh3_bD7Fjtn"}, {"__ref": "kAN_mwjZgAae"}], "allowRootWrapper": true}, "-PJd6qbT9KR5": {"__type": "Var", "name": "children", "uuid": "MhqKcfXSpIMr"}, "tiCqnWSm0qXE": {"__type": "Choice", "name": "choice", "options": ["none", "single"]}, "axH05nmP42PG": {"__type": "Var", "name": "selectionMode", "uuid": "5fDgSmKmpLJ4"}, "MQjq1Vkw9urQ": {"__type": "CustomCode", "code": "\"none\"", "fallback": null}, "Ocn76EGt99da": {"__type": "Choice", "name": "choice", "options": ["Dynamic options"]}, "PUkEG4zs-L9I": {"__type": "Var", "name": "<PERSON><PERSON><PERSON><PERSON>", "uuid": "O9Fcax1NSfdR"}, "Fr9zCpFBsHfM": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "EGSLodXDKqqn"}]}, "ddCYHw05amhZ": {"__type": "Var", "name": "onSelectionChange", "uuid": "_P9bq2Ayxo6i"}, "AOi_mhKj56cA": {"__type": "TplSlot", "param": {"__ref": "hHX7e_Totljv"}, "defaultContents": [], "uuid": "rGW7AityuCM8", "parent": {"__ref": "AkUg3LwwB-b3"}, "locked": null, "vsettings": [{"__ref": "p_jdQSve0GRu"}]}, "0l6hjI2J_PRO": {"__type": "VariantSetting", "variants": [{"__ref": "PKdRLBegukyT"}], "args": [], "attrs": {}, "rs": {"__ref": "RMeoVGPnpItY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "UdShSLkh2xok": {"__type": "RuleSet", "values": {"border-top-width": "1px", "border-right-width": "1px", "border-bottom-width": "1px", "border-left-width": "1px", "border-top-style": "solid", "border-right-style": "solid", "border-bottom-style": "solid", "border-left-style": "solid", "border-top-color": "black", "border-right-color": "black", "border-bottom-color": "black", "border-left-color": "black", "width": "250px"}, "mixins": []}, "qW25QwYUPo0n": {"__type": "CodeComponentHelper", "importPath": "@plasmicpkgs/react-aria/skinny/registerListBox", "importName": "listboxHelpers", "defaultExport": false}, "8FICfkTkGTH8": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focused]", "displayName": "Focused"}, "XirKfoe6MmkW": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focus-visible]", "displayName": "Focus Visible"}, "AtJBasd67zTq": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "lR5WszAYFS9j": {"__type": "Var", "name": "children", "uuid": "j68veow9QPr4"}, "ocxIXVYUb8QQ": {"__type": "BoolType", "name": "bool"}, "dpHkeRl3NtZX": {"__type": "Var", "name": "shouldFlip", "uuid": "SEZHoHNleSFG"}, "-PiBNnnYWjWn": {"__type": "DefaultStylesClassNamePropType", "name": "defaultStylesClassName", "includeTagStyles": false}, "9eFDnm0zFgOH": {"__type": "Var", "name": "resetClassName", "uuid": "Re6TpUHz2MWh"}, "Qu44Krt-5kjz": {"__type": "BoolType", "name": "bool"}, "ay0MrEAfW2G8": {"__type": "Var", "name": "matchTriggerWidth", "uuid": "prshwbfwEFZy"}, "pcljy3LFVVGK": {"__type": "CustomCode", "code": "true", "fallback": null}, "3rjpFCnAFf4a": {"__type": "Choice", "name": "choice", "options": ["top", "bottom", "start", "end", "left", "right"]}, "l8o2z-FBr3Bv": {"__type": "Var", "name": "placement", "uuid": "Dqrz0UjJiKKF"}, "ouHMyS8DbgrS": {"__type": "<PERSON><PERSON>", "name": "num"}, "FR0UkGrz4320": {"__type": "Var", "name": "offset", "uuid": "sD7WIwMkvQmX"}, "_5xHy-P8f_C4": {"__type": "<PERSON><PERSON>", "name": "num"}, "t7wJxz9dGEst": {"__type": "Var", "name": "containerPadding", "uuid": "R28syRZA4D9T"}, "SC2k1MQwSWzY": {"__type": "<PERSON><PERSON>", "name": "num"}, "mmft_3LUJ81m": {"__type": "Var", "name": "crossOffset", "uuid": "hR6Ay4dOCP7b"}, "s-vWYlBySosa": {"__type": "TplSlot", "param": {"__ref": "W2VqdwCldUR2"}, "defaultContents": [], "uuid": "bh03xiY49h7K", "parent": {"__ref": "Zv9NGOY8_8Z0"}, "locked": null, "vsettings": [{"__ref": "QiPR6JsWWqlv"}]}, "pav1_4-ZtR5T": {"__type": "VariantSetting", "variants": [{"__ref": "7Ew-QWB5H_rA"}], "args": [], "attrs": {}, "rs": {"__ref": "JirUq2C0oOtu"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "4WVyIQCSY8S-": {"__type": "RuleSet", "values": {"border-top-width": "1px", "border-right-width": "1px", "border-bottom-width": "1px", "border-left-width": "1px", "border-top-style": "solid", "border-right-style": "solid", "border-bottom-style": "solid", "border-left-style": "solid", "border-top-color": "black", "border-right-color": "black", "border-bottom-color": "black", "border-left-color": "black", "background": "linear-gradient(#FDE3C3, #FDE3C3)", "width": "300px"}, "mixins": []}, "HlHweaJXssVD": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-placement=top]", "displayName": "Placement (Top)"}, "KJCv5bZiUWhZ": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-placement=bottom]", "displayName": "Placement (Bottom)"}, "OJy-92lAzTUN": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-placement=left]", "displayName": "Placement (Left)"}, "erJfZ29H7FMv": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-placement=right]", "displayName": "Placement (Right)"}, "L4DvI6ATpd4y": {"__type": "Text", "name": "text"}, "Hyyx4zqy4dwz": {"__type": "Var", "name": "name", "uuid": "gPxUV9CwlG-r"}, "ob6jxt07cS5G": {"__type": "BoolType", "name": "bool"}, "5FuK9qIohnUo": {"__type": "Var", "name": "disabled", "uuid": "stOi2KRhEP96"}, "0Lrw-jHcEvw0": {"__type": "BoolType", "name": "bool"}, "CqTJ2pLipl4S": {"__type": "Var", "name": "readOnly", "uuid": "2qKoM3sXZfef"}, "NlYpJ8E9dZna": {"__type": "BoolType", "name": "bool"}, "8iardJmfNt_W": {"__type": "Var", "name": "autoFocus", "uuid": "lktUchBfxvlB"}, "vkqgmk9rwFix": {"__type": "Text", "name": "text"}, "rZbH8-vCJIXQ": {"__type": "Var", "name": "aria-label", "uuid": "edr1_X1zq2uJ"}, "YSeqAIRgpVgc": {"__type": "BoolType", "name": "bool"}, "1J3f8zQiQJSe": {"__type": "Var", "name": "required", "uuid": "3zWiTTIQkvvz"}, "9NYpPBudi_Qh": {"__type": "Text", "name": "text"}, "xfKQo9ZLFkQ2": {"__type": "Var", "name": "placeholder", "uuid": "nNVqUTjZs1u3"}, "KuDHWoD4rYnE": {"__type": "Text", "name": "text"}, "pxOqvUrIa0EW": {"__type": "Var", "name": "value", "uuid": "WDiWgllxIpvN"}, "0R5cJFyzTFO9": {"__type": "<PERSON><PERSON>", "name": "num"}, "EDCpCuDeyccs": {"__type": "Var", "name": "max<PERSON><PERSON><PERSON>", "uuid": "AnnpB6xRztBN"}, "U_mi0-ITp4s-": {"__type": "<PERSON><PERSON>", "name": "num"}, "fEtBWTNJlsum": {"__type": "Var", "name": "<PERSON><PERSON><PERSON><PERSON>", "uuid": "EA8tV5spqo59"}, "_EtnhA-jbIiX": {"__type": "Choice", "name": "choice", "options": ["none", "text", "tel", "url", "email", "numeric", "decimal", "search"]}, "YOCba2k0_1yV": {"__type": "Var", "name": "inputMode", "uuid": "g0ZpMncwGvVS"}, "MQPbY_QdKm4y": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "DxYEkgCpdJ1C"}]}, "SyyGNF30VrAe": {"__type": "Var", "name": "onChange", "uuid": "-nZXImup50Lm"}, "eYStq3MPBpvb": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "afrtsH1LBmFH"}]}, "6ndHrGUZf3hY": {"__type": "Var", "name": "onFocus", "uuid": "1dkmtM4leK08"}, "Si9Jr-CvFoZP": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "DL0rn-uc8Nhi"}]}, "LFDQsv8f0i_n": {"__type": "Var", "name": "onBlur", "uuid": "ixPASGk1IyqE"}, "zJUQoAyPly8r": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "un9yuJ8vwbl-"}]}, "Yua6WWXBTTgf": {"__type": "Var", "name": "onKeyDown", "uuid": "rAy239C0LoH8"}, "D-15jUvzGYkU": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "IfVNic3-zyJ1"}]}, "3gmKR1ZwhFsw": {"__type": "Var", "name": "onKeyUp", "uuid": "szuRGsl7dnhV"}, "rS-05zsKcfkT": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "VXx8eYOuKhNf"}]}, "sTClV6rWn_rL": {"__type": "Var", "name": "onCopy", "uuid": "_ZAovF4zoYdq"}, "Fe6qkLW0AdWv": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "Xjtdu7suHsEl"}]}, "5zsJvhYgslt3": {"__type": "Var", "name": "onCut", "uuid": "2eH9G17rqpFq"}, "yltQqZZW2Tzs": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "aO_fZq1qVNl5"}]}, "acQiIBI2Eg3A": {"__type": "Var", "name": "onPaste", "uuid": "fLe5ROobY8J8"}, "QIcxAml0lSwf": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "2pJZr-FIVBa3"}]}, "LfX3a4lIW0zE": {"__type": "Var", "name": "onCompositionStart", "uuid": "aUC2tqbm7n0n"}, "NOz0mEgDDss-": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "yNPTyN2_W2Fn"}]}, "x94h4koZbG3x": {"__type": "Var", "name": "onCompositionEnd", "uuid": "jK9WWANvqBla"}, "_9zoQ_CczKQx": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "2q0FeobO1YUb"}]}, "BFtVQY-HeV2R": {"__type": "Var", "name": "onCompositionUpdate", "uuid": "B1h2l2YZ6WGq"}, "mEwMcl875c7T": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "b_bk46CyDL0E"}]}, "Fu9kRrjLuvrg": {"__type": "Var", "name": "onSelect", "uuid": "pneO_V1h8ELP"}, "gmEAsMBoxROp": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "zaQfVtHpDqmv"}]}, "0wBtIE3xnvgW": {"__type": "Var", "name": "onBeforeInput", "uuid": "iJERgdwqm0yk"}, "cyGf5nPEfzV-": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "0pCqyLUmmtcL"}]}, "O4B-r3kpeQs-": {"__type": "Var", "name": "onInput", "uuid": "xPbRtww79m4l"}, "foBYqTf2I7tH": {"__type": "VariantSetting", "variants": [{"__ref": "OzWC1l3fsP7x"}], "args": [], "attrs": {}, "rs": {"__ref": "WdMOJdW1m2F1"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "jdBIxxMPlkVU": {"__type": "CodeComponentHelper", "importPath": "@plasmicpkgs/react-aria/skinny/registerTextArea", "importName": "inputHelpers", "defaultExport": false}, "7r3WrUHWZ0XV": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focused]", "displayName": "Focused"}, "9bYRqh0h7Pzw": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focus-visible]", "displayName": "Focus Visible"}, "06YAvkUrUTqK": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-hovered]", "displayName": "Hovered"}, "a6i_MKuzAyM3": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-disabled]", "displayName": "Disabled"}, "G-xGo1M6BUYk": {"__type": "Text", "name": "text"}, "IpwYRfDvF8Yb": {"__type": "Var", "name": "name", "uuid": "_RMlnI05A96S"}, "t8OlVzu8Q23P": {"__type": "BoolType", "name": "bool"}, "K_AfbBuyF-FQ": {"__type": "Var", "name": "disabled", "uuid": "T4_J5_TtaR-E"}, "1yYFgmM7wL2M": {"__type": "BoolType", "name": "bool"}, "Gt9n4wr5YId4": {"__type": "Var", "name": "readOnly", "uuid": "eRFFGLSUPAAq"}, "-4vALmVwryAD": {"__type": "BoolType", "name": "bool"}, "n6RM_6E1VQNJ": {"__type": "Var", "name": "autoFocus", "uuid": "MvWa5Kf1nVMf"}, "cOwzWrEF6bjr": {"__type": "Text", "name": "text"}, "tUgV4rb7dIxh": {"__type": "Var", "name": "aria-label", "uuid": "JI8WKIeCGcNl"}, "ilpO8rd5Zjfm": {"__type": "BoolType", "name": "bool"}, "mEedLlHjUgDI": {"__type": "Var", "name": "required", "uuid": "_31en<PERSON><PERSON><PERSON><PERSON><PERSON>-"}, "81lBpKKXvJvU": {"__type": "Text", "name": "text"}, "0Gq1K3h17BOZ": {"__type": "Var", "name": "placeholder", "uuid": "gkTOK9yOk5oL"}, "gk4IK9eiVI4S": {"__type": "Text", "name": "text"}, "XN33DL9YFMd9": {"__type": "Var", "name": "value", "uuid": "CeKXmvGj-u66"}, "RzDIN25oqtxL": {"__type": "<PERSON><PERSON>", "name": "num"}, "yAcwc5RaOgt5": {"__type": "Var", "name": "max<PERSON><PERSON><PERSON>", "uuid": "xD8MRoyyw-DI"}, "fPofvXDoC2QD": {"__type": "<PERSON><PERSON>", "name": "num"}, "hn-1IQIYVJ8s": {"__type": "Var", "name": "<PERSON><PERSON><PERSON><PERSON>", "uuid": "3zM56e4uVSDW"}, "glbs3c4MaSaa": {"__type": "Text", "name": "text"}, "qmqazPW5eaUJ": {"__type": "Var", "name": "pattern", "uuid": "xlHq1Co8Ni7d"}, "ojI-UItn-Yjy": {"__type": "Choice", "name": "choice", "options": ["text", "search", "url", "tel", "email", "password"]}, "4oH-WmXh_PFD": {"__type": "Var", "name": "type", "uuid": "s6KfxLjqJjEt"}, "kP8RkRS-AlLQ": {"__type": "Choice", "name": "choice", "options": ["none", "text", "tel", "url", "email", "numeric", "decimal", "search"]}, "Sh_vMfvfTcZT": {"__type": "Var", "name": "inputMode", "uuid": "iiX-QuUjFg0A"}, "aOwLz00rom2w": {"__type": "Choice", "name": "choice", "options": ["on", "off", "name", "honorific-prefix", "given-name", "additional-name", "family-name", "honorific-suffix", "nickname", "email", "username", "new-password", "current-password", "one-time-code", "organization-title", "organization", "street-address", "shipping", "billing", "address-line1", "address-line2", "address-line3", "address-level4", "address-level3", "address-level2", "address-level1", "country", "country-name", "postal-code", "cc-name", "cc-given-name", "cc-additional-name", "cc-family-name", "cc-number", "cc-exp", "cc-exp-month", "cc-exp-year", "cc-csc", "cc-type", "transaction-currency", "transaction-amount", "language", "bday", "bday-day", "bday-month", "bday-year", "sex", "tel", "tel-country-code", "tel-national", "tel-area-code", "tel-local", "tel-local-suffix", "tel-local-prefix", "tel-extension", "impp", "url", "photo", "webauthn"]}, "cOYbdrj8mtMk": {"__type": "Var", "name": "autoComplete", "uuid": "fr_oM4hfu3Ht"}, "dNnoG4vhW8RV": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "xyPTf4q2m782"}]}, "NdIKsBUlgnUG": {"__type": "Var", "name": "onChange", "uuid": "k7vDtoN77a8X"}, "-AOSS5QtimRU": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "26ubtInyGCwB"}]}, "d8gVxToFAtFE": {"__type": "Var", "name": "onFocus", "uuid": "DTOzcdf8Z-dW"}, "RCbp52uYzmJ_": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "E2t_PojpwXlk"}]}, "GlH_ieDBXkr6": {"__type": "Var", "name": "onBlur", "uuid": "sK5zFGC4dHiE"}, "fFu6wjab0sfk": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "JRDwFAkyHibK"}]}, "GKNbX3-4Obrd": {"__type": "Var", "name": "onKeyDown", "uuid": "msO-qfL_5FjO"}, "QRgXGImaca8Z": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "F1UC3ACKkTXO"}]}, "8qPA7-02aSgw": {"__type": "Var", "name": "onKeyUp", "uuid": "5J81VX91-muj"}, "xA4rHK7fwa9-": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "mOvavMop2Qdx"}]}, "qP84a8q263eC": {"__type": "Var", "name": "onCopy", "uuid": "FgQ73ghOn39k"}, "8eyudggKbGCU": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "9xHTXtpN1fLW"}]}, "aa1kQN4F8hau": {"__type": "Var", "name": "onCut", "uuid": "O_Pxk3z43_rj"}, "CYU6VsDzm8UE": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "nGRPadIiYb4A"}]}, "pibAyUkwF5WO": {"__type": "Var", "name": "onPaste", "uuid": "XOdmK5sGpiCf"}, "HKHuvUmn0UIP": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "-SHiqH7jX7ag"}]}, "4hgNy0mjOFGg": {"__type": "Var", "name": "onCompositionStart", "uuid": "hvWEdnO_RMU2"}, "Z6Er7SZppbAR": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "nfEz3K_i7ZPr"}]}, "3IncsEJJ2Olp": {"__type": "Var", "name": "onCompositionEnd", "uuid": "shaJCGgvKuLw"}, "SF4IPZOlA3WP": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "jjI2E_OTXA6C"}]}, "4_eHcCqyp2CF": {"__type": "Var", "name": "onCompositionUpdate", "uuid": "P5IIKpzH7Go1"}, "RPcQ5b-su6tP": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "fQzmjKmD5L5H"}]}, "WUaPiMHz5TL2": {"__type": "Var", "name": "onSelect", "uuid": "3-F5TjlEkMMm"}, "pcsLQPEmfPm9": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "9TDvGNf4ppVa"}]}, "1YTcLyh61tz5": {"__type": "Var", "name": "onBeforeInput", "uuid": "yPTMDaRRRwED"}, "EcqIz50JmpfG": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "Sn662q-WqB9m"}]}, "B7-TGNZtSY_d": {"__type": "Var", "name": "onInput", "uuid": "p0xczp5tQ9x2"}, "FP6emYbGjemD": {"__type": "VariantSetting", "variants": [{"__ref": "JSuP08XEi3TQ"}], "args": [], "attrs": {}, "rs": {"__ref": "anO6Qmbgg-FX"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "c3774Xx5iX5r": {"__type": "RuleSet", "values": {"padding-top": "4px", "padding-right": "10px", "padding-bottom": "4px", "padding-left": "10px", "border-top-width": "1px", "border-right-width": "1px", "border-bottom-width": "1px", "border-left-width": "1px", "border-top-style": "solid", "border-right-style": "solid", "border-bottom-style": "solid", "border-left-style": "solid", "border-top-color": "black", "border-right-color": "black", "border-bottom-color": "black", "border-left-color": "black", "width": "300px"}, "mixins": []}, "W0Y6ItoFC5oj": {"__type": "CodeComponentHelper", "importPath": "@plasmicpkgs/react-aria/skinny/registerInput", "importName": "inputHelpers", "defaultExport": false}, "cYUNMH632k-r": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focused]", "displayName": "Focused"}, "PaDt2wwFxwhF": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focus-visible]", "displayName": "Focus Visible"}, "M6u19u8i55K_": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-hovered]", "displayName": "Hovered"}, "hlL9gnqnqYfl": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-disabled]", "displayName": "Disabled"}, "xngPheiwGhDV": {"__type": "Text", "name": "text"}, "KDkmYRQf1F3x": {"__type": "Var", "name": "name", "uuid": "MiOkqsmrdv6J"}, "2HPKSxxVSktb": {"__type": "BoolType", "name": "bool"}, "uJKsGGKWKNLM": {"__type": "Var", "name": "isDisabled", "uuid": "EpmLBk13Bk3e"}, "D6Y_BPD1mkae": {"__type": "BoolType", "name": "bool"}, "axZob0foZ34W": {"__type": "Var", "name": "isReadOnly", "uuid": "YbXfLTUlF_Rk"}, "F_V4Ylok7Q_a": {"__type": "BoolType", "name": "bool"}, "wiJW-zBY-KA1": {"__type": "Var", "name": "autoFocus", "uuid": "AP1bcP0cIG4y"}, "RzWAtD_l5W_Q": {"__type": "Text", "name": "text"}, "UsIbf2Hz86yt": {"__type": "Var", "name": "aria-label", "uuid": "jw-3rpUSvZ0f"}, "WF1gvR4hX2ND": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "SN1ybtrWiLBX": {"__type": "Var", "name": "children", "uuid": "FrnvD8eQBerW"}, "-f4Gw0z4dYpt": {"__type": "Text", "name": "text"}, "tj9JgFuhof2r": {"__type": "Var", "name": "value", "uuid": "TeXX2aVsdNAU"}, "K2MnTYcIM74j": {"__type": "BoolType", "name": "bool"}, "6C6BMqLLXMfD": {"__type": "Var", "name": "isSelected", "uuid": "fNq8gY5VbuCX"}, "LceYW_ztWYdj": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "eR_GnUlzXNRh"}]}, "DL61YwIrkplm": {"__type": "Var", "name": "onChange", "uuid": "l1Ob5_ieQxG4"}, "OFn2PnBpO9nH": {"__type": "TplSlot", "param": {"__ref": "8tvWcD6ObNC9"}, "defaultContents": [], "uuid": "xwcZLMpuGP5y", "parent": {"__ref": "kxL1LrvPWSs8"}, "locked": null, "vsettings": [{"__ref": "KYRCh9tjgdg4"}]}, "5mfTBoJgnWsP": {"__type": "VariantSetting", "variants": [{"__ref": "5_LRVlcQFHM_"}], "args": [], "attrs": {}, "rs": {"__ref": "aWPT2tQO9k0i"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "6_vF-hFsFpdP": {"__type": "RuleSet", "values": {"padding-top": "0", "padding-right": "0", "padding-bottom": "0", "padding-left": "0", "display": "flex", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start"}, "mixins": []}, "vZkVvy5pjdmt": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-hovered]", "displayName": "Hovered"}, "XAw8TOegMMPd": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-pressed]", "displayName": "Pressed"}, "CYBigR2-YotR": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focused]", "displayName": "Focused"}, "Rj6EBO_aZA2_": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focus-visible]", "displayName": "Focus Visible"}, "GoCG6LaRdbXz": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-selected]", "displayName": "Selected"}, "8TgpZFZ9Q5aV": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-disabled]", "displayName": "Disabled"}, "_IW7bH_v-ztZ": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-readonly]", "displayName": "Read Only"}, "dUNCWrSUow9Y": {"__type": "Text", "name": "text"}, "bTpZeSHgKy5n": {"__type": "Var", "name": "name", "uuid": "tv79ON76ZGYv"}, "9F6dhzSwkS5Y": {"__type": "BoolType", "name": "bool"}, "CLyEiAIt0sJD": {"__type": "Var", "name": "isDisabled", "uuid": "E8vBrVP5kA6K"}, "uPGPq_Ab-VoL": {"__type": "BoolType", "name": "bool"}, "BBo6wjBN5ksD": {"__type": "Var", "name": "isReadOnly", "uuid": "tk6c6NfoTXqK"}, "5thYpzj6VgLx": {"__type": "Text", "name": "text"}, "zHtK3RO12MjM": {"__type": "Var", "name": "aria-label", "uuid": "qvZZJp338GGU"}, "L32Rr3QaBN2t": {"__type": "BoolType", "name": "bool"}, "OXYoeoLBeoI9": {"__type": "Var", "name": "isRequired", "uuid": "QHZ8h3qKAzSg"}, "91yuLmaDcfIL": {"__type": "BoolType", "name": "bool"}, "UOhd6b47Dgz2": {"__type": "Var", "name": "autoFocus", "uuid": "sq6ZpodoSUX6"}, "YQGIK4vSOuBt": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "S5ebJU82zFch": {"__type": "Var", "name": "children", "uuid": "SLkAA0Opaa-4"}, "iurmlo_wx3af": {"__type": "Text", "name": "text"}, "wiyCkhMwtDO1": {"__type": "Var", "name": "value", "uuid": "tCLHZnkbpSPE"}, "jjoljbdvmXlN": {"__type": "BoolType", "name": "bool"}, "hD_HpaANQx-h": {"__type": "Var", "name": "isSelected", "uuid": "8_LLcYhB-sNq"}, "RgKwRfAPc62_": {"__type": "CustomCode", "code": "false", "fallback": null}, "8S5mSipR2sL1": {"__type": "BoolType", "name": "bool"}, "xFsgXK0g1wbc": {"__type": "Var", "name": "isIndeterminate", "uuid": "il5r4VvjXu8Z"}, "eqKRw4-2tXQO": {"__type": "BoolType", "name": "bool"}, "KA5YVXRQ9Bg3": {"__type": "Var", "name": "isInvalid", "uuid": "WsyJqtBiIpfD"}, "09pvEZwIR6Vv": {"__type": "Choice", "name": "choice", "options": ["native", "aria"]}, "WVTdd7NzLVkw": {"__type": "Var", "name": "validation<PERSON><PERSON><PERSON><PERSON>", "uuid": "s42OlGTVKB4b"}, "7NFkf0PkvPwZ": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "aNx24tkvZAER"}]}, "Zv-hBK6Zo3ya": {"__type": "Var", "name": "onChange", "uuid": "bIbXLverthxB"}, "_MIz55uks9YL": {"__type": "TplSlot", "param": {"__ref": "lsC4m80mrUAb"}, "defaultContents": [], "uuid": "487xy_gcTDrL", "parent": {"__ref": "v3W21WJ1-t0E"}, "locked": null, "vsettings": [{"__ref": "js1sXTmtGcjh"}]}, "BoHrHTJ0ONJ4": {"__type": "VariantSetting", "variants": [{"__ref": "kQh9Iwf1iIq7"}], "args": [], "attrs": {}, "rs": {"__ref": "rePXdasx3iWc"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "--9qVSVEGGS1": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-hovered]", "displayName": "Hovered"}, "xOFDRSMiAVr9": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-pressed]", "displayName": "Pressed"}, "TjvRbkOlQlv7": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focused]", "displayName": "Focused"}, "GJlvf5UbeHUB": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focus-visible]", "displayName": "Focus Visible"}, "JMsn0girLWos": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-indeterminate]", "displayName": "Indeterminate"}, "0GlWZ0BeiHvW": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-disabled]", "displayName": "Disabled"}, "TaNKKXke4RlR": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-selected]", "displayName": "Selected"}, "LhuVuPXTSBhr": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-readonly]", "displayName": "Read Only"}, "RbuYAt7PFInk": {"__type": "Text", "name": "text"}, "F6diT1sda7hS": {"__type": "Var", "name": "name", "uuid": "FinxhQfFwjca"}, "c2Cx_O7Zh0f0": {"__type": "BoolType", "name": "bool"}, "z1StCxkuWVkB": {"__type": "Var", "name": "isDisabled", "uuid": "vBbWHpNdvEZc"}, "yvtHWFKWSt-w": {"__type": "BoolType", "name": "bool"}, "GtntqeoUw3iS": {"__type": "Var", "name": "isReadOnly", "uuid": "Znzb8uQs7GKS"}, "otHNppLg7x13": {"__type": "Text", "name": "text"}, "fNa3fN81DS9c": {"__type": "Var", "name": "aria-label", "uuid": "jO9b18AZrXKA"}, "pXMp30od_Vvk": {"__type": "BoolType", "name": "bool"}, "EQ4ODapkLU0v": {"__type": "Var", "name": "isRequired", "uuid": "0FMUbw7O0WLf"}, "xdSugX3u8Ot5": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "sjuYjpXB8lnL": {"__type": "Var", "name": "children", "uuid": "_b6sa6VxYvst"}, "uu2X9lb-gxfX": {"__type": "Choice", "name": "choice", "options": ["Dynamic options"]}, "X-GirVtp0lyb": {"__type": "Var", "name": "value", "uuid": "Q35xO3o61g3R"}, "P8VQIYzm6tuS": {"__type": "BoolType", "name": "bool"}, "XnZ6KSq5Ns6d": {"__type": "Var", "name": "isInvalid", "uuid": "kUhvQRP9tWvl"}, "ViRSrl-6ibqz": {"__type": "Choice", "name": "choice", "options": ["native", "aria"]}, "4TU0kWmdwCiW": {"__type": "Var", "name": "validation<PERSON><PERSON><PERSON><PERSON>", "uuid": "jlwRTaXnIcSe"}, "I9FbRFjjOK2c": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "q7u57Won_MJK"}]}, "d2GpFhY46j5a": {"__type": "Var", "name": "onChange", "uuid": "dFz3ZjPiZzwo"}, "bUR96Aofsu4i": {"__type": "TplSlot", "param": {"__ref": "Jf_AyPbp3wPQ"}, "defaultContents": [], "uuid": "NBpm5sphe7Z8", "parent": {"__ref": "WrstRlbE0oMM"}, "locked": null, "vsettings": [{"__ref": "OzdIVwFDFdLF"}]}, "ErFupkZqG4YG": {"__type": "VariantSetting", "variants": [{"__ref": "ukASI-FmmI6H"}], "args": [], "attrs": {}, "rs": {"__ref": "tEgYy413b7V1"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "iWm2PmdvfevR": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-disabled]", "displayName": "Disabled"}, "TE3FqdYdDvOD": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-readonly]", "displayName": "Read Only"}, "dvEaxO52gcSq": {"__type": "BoolType", "name": "bool"}, "hLOygTAZOO9P": {"__type": "Var", "name": "isDisabled", "uuid": "lCyd87anZk6x"}, "BaucaOxNYxiw": {"__type": "BoolType", "name": "bool"}, "YyYlR4ciSB2R": {"__type": "Var", "name": "autoFocus", "uuid": "ag5IJ0HZVpcL"}, "NbfvTywvA1Ua": {"__type": "Text", "name": "text"}, "cWXeJztnA1gW": {"__type": "Var", "name": "aria-label", "uuid": "wVNyr6pdf1-Y"}, "-tLFT8ulrP6N": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "najRI9lIG3Uz": {"__type": "Var", "name": "children", "uuid": "smVX0Zkg1YQh"}, "BXLkg-Q30by3": {"__type": "Text", "name": "text"}, "IOy7xMDptGkc": {"__type": "Var", "name": "value", "uuid": "-iyH4rMsTrLl"}, "yIrtttZ_NSSP": {"__type": "TplSlot", "param": {"__ref": "o-HJRYy9ZdDf"}, "defaultContents": [], "uuid": "K9XAo9YAPp4i", "parent": {"__ref": "n18rgy53oZC-"}, "locked": null, "vsettings": [{"__ref": "jUi3d8xcdnwe"}]}, "yfmM4G6UYe_7": {"__type": "VariantSetting", "variants": [{"__ref": "C__sH9cs49R0"}], "args": [], "attrs": {}, "rs": {"__ref": "O8GX0FysGZWr"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "d-qsvYRCdk7P": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-selected]", "displayName": "Selected"}, "V9zNJnxj-qQL": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-hovered]", "displayName": "Hovered"}, "45-PQbpwgQ3a": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-pressed]", "displayName": "Pressed"}, "WxQRKPp0Gkqo": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focused]", "displayName": "Focused"}, "mweP3kFH7nfy": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focus-visible]", "displayName": "Focus Visible"}, "jyiQEYhqX_0D": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-disabled]", "displayName": "Disabled"}, "qzJd_EyepgEm": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-readonly]", "displayName": "Read Only"}, "prmT-2TP9wLK": {"__type": "Text", "name": "text"}, "AsQwW69ZaPvw": {"__type": "Var", "name": "name", "uuid": "s2ZwaKuzbP1R"}, "K8cJR1KlqYoO": {"__type": "BoolType", "name": "bool"}, "7ruHdSYT6fjh": {"__type": "Var", "name": "isDisabled", "uuid": "_UBC9tQ6zVgZ"}, "A8Jzwt09KTtt": {"__type": "BoolType", "name": "bool"}, "347UuLhtg_1I": {"__type": "Var", "name": "isReadOnly", "uuid": "BYwZqU9DEdo2"}, "ww8hnBmf7iu5": {"__type": "Text", "name": "text"}, "Sx5oEnehFXm_": {"__type": "Var", "name": "aria-label", "uuid": "Y8GwmkDzvmgx"}, "IsCppGdq_yBV": {"__type": "BoolType", "name": "bool"}, "eBxXf5uLitgA": {"__type": "Var", "name": "isRequired", "uuid": "D6eYD0QNlMuE"}, "sUiuwDNtxaGO": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "Ii-nAvOhCHpU": {"__type": "Var", "name": "children", "uuid": "siYUncLr8L-b"}, "zQpGYaJg3U9J": {"__type": "Choice", "name": "choice", "options": ["Dynamic options"]}, "GizeRjL7Yqkv": {"__type": "Var", "name": "value", "uuid": "-yRifvBbm_Bi"}, "rqkdqciD_wRi": {"__type": "BoolType", "name": "bool"}, "VLEa0eTpjFN0": {"__type": "Var", "name": "isInvalid", "uuid": "NvoS4J-4-K79"}, "DqjT5RtqqrJ4": {"__type": "Choice", "name": "choice", "options": ["native", "aria"]}, "lXXU-16RB-6v": {"__type": "Var", "name": "validation<PERSON><PERSON><PERSON><PERSON>", "uuid": "caHva0zgZ2lg"}, "9ADQdM1oIvLW": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "OivwnX8YMajV"}]}, "zNJXMZnjqvgb": {"__type": "Var", "name": "onChange", "uuid": "ENedHAou_uul"}, "x82j4JqebwPk": {"__type": "TplSlot", "param": {"__ref": "JvK8uD4Yn-1i"}, "defaultContents": [], "uuid": "CXvIuHEzy7ki", "parent": {"__ref": "6YziQWGvbttf"}, "locked": null, "vsettings": [{"__ref": "GBxVcltvaVMj"}]}, "HdUeelbaW9sI": {"__type": "VariantSetting", "variants": [{"__ref": "VB5YF0sw6gSV"}], "args": [], "attrs": {}, "rs": {"__ref": "QVEMNmin-_7F"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "EcSk_UepuyDD": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-disabled]", "displayName": "Disabled"}, "IP7tA5IIEOQ7": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-readonly]", "displayName": "Read Only"}, "WuzVc5Gc4tZ2": {"__type": "Text", "name": "text"}, "zgAmzKTjRlIQ": {"__type": "Var", "name": "name", "uuid": "MhyPDSAtFZWU"}, "HHDTRqsOCXDu": {"__type": "BoolType", "name": "bool"}, "GGDzxEQ7tTjf": {"__type": "Var", "name": "isDisabled", "uuid": "9Dv4JKPr4N4U"}, "LHELTqffD2s8": {"__type": "BoolType", "name": "bool"}, "dEbv_cpSIMC9": {"__type": "Var", "name": "isReadOnly", "uuid": "1QApwWLc_Aju"}, "M-EfgzJ_BFJy": {"__type": "BoolType", "name": "bool"}, "hFd2aIxaMU7P": {"__type": "Var", "name": "autoFocus", "uuid": "RzutfKah2Ylx"}, "MFv0F1o4rhFJ": {"__type": "Text", "name": "text"}, "zHFiuzcALvVo": {"__type": "Var", "name": "aria-label", "uuid": "BaLLKTh9km5T"}, "aiJMSOihdjo2": {"__type": "BoolType", "name": "bool"}, "wq3C9rD_d7BG": {"__type": "Var", "name": "isRequired", "uuid": "CHsiNivgBoWn"}, "Yq3AxfJKGZvm": {"__type": "Text", "name": "text"}, "tJLtCxcpHMZT": {"__type": "Var", "name": "value", "uuid": "bf3yq4H90iMW"}, "NZQymuVMTj52": {"__type": "<PERSON><PERSON>", "name": "num"}, "dI7vvnWzwirn": {"__type": "Var", "name": "max<PERSON><PERSON><PERSON>", "uuid": "B5rswwn_YrUE"}, "lHpA-vyOKSzb": {"__type": "<PERSON><PERSON>", "name": "num"}, "rLncFH6Kos9A": {"__type": "Var", "name": "<PERSON><PERSON><PERSON><PERSON>", "uuid": "yTwt5XA1A1ck"}, "HEPvSeQnGw8B": {"__type": "Text", "name": "text"}, "NVfqqq3K-L15": {"__type": "Var", "name": "pattern", "uuid": "kTueHy-WCFsL"}, "CIEvjXt_qskU": {"__type": "Choice", "name": "choice", "options": ["text", "search", "url", "tel", "email", "password"]}, "39qqBLJJb1pS": {"__type": "Var", "name": "type", "uuid": "sUtVInUf0S8r"}, "eh2UrOgob4FJ": {"__type": "Choice", "name": "choice", "options": ["none", "text", "tel", "url", "email", "numeric", "decimal", "search"]}, "Z2b4GUtVjn2a": {"__type": "Var", "name": "inputMode", "uuid": "4NvKrzGIrITx"}, "ikk3xHuDe20B": {"__type": "Choice", "name": "choice", "options": ["native", "aria"]}, "iNDoqmB0Adgd": {"__type": "Var", "name": "validation<PERSON><PERSON><PERSON><PERSON>", "uuid": "TABRBzzHxM7C"}, "OfutWf1EBk-8": {"__type": "Choice", "name": "choice", "options": ["on", "off", "name", "honorific-prefix", "given-name", "additional-name", "family-name", "honorific-suffix", "nickname", "email", "username", "new-password", "current-password", "one-time-code", "organization-title", "organization", "street-address", "shipping", "billing", "address-line1", "address-line2", "address-line3", "address-level4", "address-level3", "address-level2", "address-level1", "country", "country-name", "postal-code", "cc-name", "cc-given-name", "cc-additional-name", "cc-family-name", "cc-number", "cc-exp", "cc-exp-month", "cc-exp-year", "cc-csc", "cc-type", "transaction-currency", "transaction-amount", "language", "bday", "bday-day", "bday-month", "bday-year", "sex", "tel", "tel-country-code", "tel-national", "tel-area-code", "tel-local", "tel-local-suffix", "tel-local-prefix", "tel-extension", "impp", "url", "photo", "webauthn"]}, "JYbp4CL7dxYo": {"__type": "Var", "name": "autoComplete", "uuid": "ZWrsmcpXDkAP"}, "v0S8RVUzWUNc": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "ZgYRd3P_dmGv"}]}, "FbB_kWZGVdAK": {"__type": "Var", "name": "onChange", "uuid": "Kd2dTdQaKzl3"}, "55DGmygxuqXj": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "OVM6CnlUSy3s"}]}, "JPlPqLBI0TgR": {"__type": "Var", "name": "onFocus", "uuid": "6l46VlbSSXTm"}, "fXbrBOcCbPLG": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "NJjmhNf03DdL"}]}, "ao02F-uYMyNz": {"__type": "Var", "name": "onBlur", "uuid": "-45aIiNoF32A"}, "kJOgans-nD3L": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "HAdptiTGaU1A"}]}, "-5pWQRjVk-_S": {"__type": "Var", "name": "onFocusChange", "uuid": "WHwdpHrixh8z"}, "wxdYAXW4dyBk": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "ziXhIQYnr8vr"}]}, "u1c6yFMqwofr": {"__type": "Var", "name": "onKeyDown", "uuid": "SpsLVG_TVYvC"}, "62M1HIkmW5_W": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "rkdphp-_tdTb"}]}, "m9hTZPhoUM8y": {"__type": "Var", "name": "onKeyUp", "uuid": "A4VKDJuX61db"}, "SVT7qumM6IXL": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "Tis8E3zKbGBH"}]}, "iRguGR4pEn5p": {"__type": "Var", "name": "onCopy", "uuid": "p3coesRM1ROy"}, "84WN68EnUzto": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "y9G9AeHJKx7E"}]}, "uDOlyPdOITIY": {"__type": "Var", "name": "onCut", "uuid": "0SG3xHyb20Lc"}, "qCcmIEg78jI6": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "n7YV1g8kPeOb"}]}, "WvMUC-83K1pL": {"__type": "Var", "name": "onPaste", "uuid": "3mnO2_XmrjyU"}, "hraHKjEhizJ4": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "jZtbbSncrMY8"}]}, "OsrelbxA-WYt": {"__type": "Var", "name": "onCompositionStart", "uuid": "mNXlvejfaR_z"}, "Tv2FDjMEP0MQ": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "VJyVM8sEuFjv"}]}, "L3BEJCfUDmqZ": {"__type": "Var", "name": "onCompositionEnd", "uuid": "1SwQJJXpB5s3"}, "ZQvUj2wqop4x": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "398eD2wTNSB7"}]}, "GKWSrcy8x-_V": {"__type": "Var", "name": "onCompositionUpdate", "uuid": "_e40lfEV66lD"}, "K86gewW5yTWk": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "4jxLxwlviwNE"}]}, "wcSS85kesSRr": {"__type": "Var", "name": "onSelect", "uuid": "xXOFuoHJIDaQ"}, "PYu5KgDsyCRO": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "HUJKplHd9uTU"}]}, "Jr5TrZOSPNqv": {"__type": "Var", "name": "onBeforeInput", "uuid": "_5WZW311m1GL"}, "As3rk5BDt9Sg": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "Tf6GY771iC30"}]}, "AVt2pM8duhdk": {"__type": "Var", "name": "onInput", "uuid": "nWKEYu3kQ9u_"}, "m4PeFvBRyJq6": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "_wrwJQ3STZMh": {"__type": "Var", "name": "children", "uuid": "SYDpRlrWio3o"}, "Pkz8EymoniYa": {"__type": "BoolType", "name": "bool"}, "ovf9Sun8wkQ1": {"__type": "Var", "name": "isInvalid", "uuid": "uAPgPizJT_5O"}, "v37DOIFIb0Yd": {"__type": "AnyType", "name": "any"}, "xLgTmTZQYnn9": {"__type": "Var", "name": "customValidationErrors", "uuid": "IOKC-9gurXWm"}, "evtgdvlEQnsa": {"__type": "TplSlot", "param": {"__ref": "HImE9S8NWqDd"}, "defaultContents": [], "uuid": "B33XQR-HvZN4", "parent": {"__ref": "KIeELUFBH5Hj"}, "locked": null, "vsettings": [{"__ref": "lZgxYyjL_Dlj"}]}, "FU8Q-Gzer9bZ": {"__type": "VariantSetting", "variants": [{"__ref": "IdcdHSnSjaIA"}], "args": [], "attrs": {}, "rs": {"__ref": "zxITxIuqxNR9"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "CLMwkMtLyQBO": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-disabled]", "displayName": "Disabled"}, "_D4SoskZ34qs": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-readonly]", "displayName": "Read Only"}, "2NN_vqDayBK0": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "roq3JtC0pqAM": {"__type": "Var", "name": "children", "uuid": "0LzLtQievdtb"}, "tCuyhysU37xM": {"__type": "ClassNamePropType", "name": "className", "selectors": [], "defaultStyles": {}}, "PjPH1fclIyi1": {"__type": "Var", "name": "modalOverlayClass", "uuid": "dhu55pclQN_H"}, "Q4JjCAknBIKR": {"__type": "BoolType", "name": "bool"}, "naHW73VEN2iB": {"__type": "Var", "name": "isOpen", "uuid": "yHYcB8-VKWJ3"}, "b5rjC1pNSBz-": {"__type": "CustomCode", "code": "true", "fallback": null}, "bZxzurABDWfN": {"__type": "BoolType", "name": "bool"}, "Xil6xjHHhp-g": {"__type": "Var", "name": "isDismissable", "uuid": "ZkZ7_xcfeBw6"}, "5j-SUQRER_lZ": {"__type": "BoolType", "name": "bool"}, "OGs6yq-cNpfI": {"__type": "Var", "name": "isKeyboardDismissDisabled", "uuid": "3IlSqLmdybRw"}, "TjWfNNumhxqB": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "pqOarHvRq0f5"}]}, "r0bm0WDJAD3y": {"__type": "Var", "name": "onOpenChange", "uuid": "JW7wJortLFqo"}, "zRW7QgE8l45a": {"__type": "DefaultStylesClassNamePropType", "name": "defaultStylesClassName", "includeTagStyles": false}, "ER36WCCloIJo": {"__type": "Var", "name": "resetClassName", "uuid": "v5NGFZww-2sL"}, "lYTQpzOmQnD0": {"__type": "TplSlot", "param": {"__ref": "1br4K9-A-4KI"}, "defaultContents": [], "uuid": "SQnhwLgbhibS", "parent": {"__ref": "Nd-LOkzWtFKY"}, "locked": null, "vsettings": [{"__ref": "CaMn4a7nUnR7"}]}, "_9OVhzVQsQWv": {"__type": "VariantSetting", "variants": [{"__ref": "Bjzxxbl0bfKM"}], "args": [], "attrs": {}, "rs": {"__ref": "ZMN9QIvtkK3v"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "iNH7uRErx18k": {"__type": "RuleSet", "values": {"padding-top": "20px", "padding-right": "20px", "padding-bottom": "20px", "padding-left": "20px", "border-top-width": "1px", "border-right-width": "1px", "border-bottom-width": "1px", "border-left-width": "1px", "border-top-style": "solid", "border-right-style": "solid", "border-bottom-style": "solid", "border-left-style": "solid", "border-top-color": "black", "border-right-color": "black", "border-bottom-color": "black", "border-left-color": "black", "background": "linear-gradient(#FDE3C3, #FDE3C3)", "width": "50%", "position": "fixed", "top": "10%", "left": "50%", "transform": "translateX(-50%)", "max-width": "300px"}, "mixins": []}, "PWEkwyABeyEq": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "1e8PM0pe2vg5": {"__type": "Var", "name": "children", "uuid": "__Df3K8OGacx"}, "ueNDGiZ6quru": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "clbcctroiUwj": {"__type": "Var", "name": "tooltipContent", "uuid": "wTWTHLChSiIs"}, "IurztkfPLH-O": {"__type": "DefaultStylesClassNamePropType", "name": "defaultStylesClassName", "includeTagStyles": false}, "8Rjo_IQVp0f0": {"__type": "Var", "name": "resetClassName", "uuid": "ruEL8yj8QjZU"}, "iDXGRdGnyHQG": {"__type": "BoolType", "name": "bool"}, "X_D3ps7IRUzV": {"__type": "Var", "name": "isDisabled", "uuid": "BNsraoLnvMiB"}, "MAAmAyi9KYtI": {"__type": "<PERSON><PERSON>", "name": "num"}, "hkdDizMmPlyV": {"__type": "Var", "name": "delay", "uuid": "VEh-SmF_lns1"}, "EF6nKG6el95I": {"__type": "CustomCode", "code": "0", "fallback": null}, "fcI2NcJGUIns": {"__type": "<PERSON><PERSON>", "name": "num"}, "NkD1fsYc3fl6": {"__type": "Var", "name": "close<PERSON><PERSON><PERSON>", "uuid": "XsfSHpBoyUam"}, "_Lt-KEE3ZHS7": {"__type": "CustomCode", "code": "0", "fallback": null}, "FKAJ_9paC6Rv": {"__type": "Choice", "name": "choice", "options": ["focus", "focus and hover"]}, "He1WyGRpaNM6": {"__type": "Var", "name": "trigger", "uuid": "0Nb85jb0yhD6"}, "a4qHqsETEBCU": {"__type": "Choice", "name": "choice", "options": ["top", "bottom", "start", "end", "left", "right"]}, "W1MbEvt10iD5": {"__type": "Var", "name": "placement", "uuid": "CDccCaPYjL-k"}, "T1C6ayqAU_YI": {"__type": "<PERSON><PERSON>", "name": "num"}, "1sMB6-FBm7st": {"__type": "Var", "name": "offset", "uuid": "tFZkVRGyow-y"}, "fBeMY84_yrNT": {"__type": "<PERSON><PERSON>", "name": "num"}, "ARX5FNJrddE4": {"__type": "Var", "name": "containerPadding", "uuid": "2a-f9MobozJK"}, "irkVCMfmozUd": {"__type": "<PERSON><PERSON>", "name": "num"}, "sCRmqwpHiFHK": {"__type": "Var", "name": "crossOffset", "uuid": "qtglPyfjmxs4"}, "RZHt-_3WzzVK": {"__type": "BoolType", "name": "bool"}, "_EhHr1S_q-9h": {"__type": "Var", "name": "isOpen", "uuid": "mtQ0fvcqOfIH"}, "HsJHscpH32dz": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "JnmThHNDXW0G"}]}, "Ni7flbqOgTlv": {"__type": "Var", "name": "onOpenChange", "uuid": "NpDGFKRqI-DB"}, "B0_PDf44-JQQ": {"__type": "TplSlot", "param": {"__ref": "lS-Qj3908pj8"}, "defaultContents": [], "uuid": "7STYm1P3GpaH", "parent": {"__ref": "GOLxeZCIJoXF"}, "locked": null, "vsettings": [{"__ref": "RT-fK5UeFb6c"}]}, "5fMSofn20pBo": {"__type": "TplSlot", "param": {"__ref": "5C_9gv8wP8K8"}, "defaultContents": [], "uuid": "myz3cE3eChf0", "parent": {"__ref": "GOLxeZCIJoXF"}, "locked": null, "vsettings": [{"__ref": "65b9CV25fCtY"}]}, "dYfaJR_wYsi1": {"__type": "VariantSetting", "variants": [{"__ref": "fTwOQEtzhW4M"}], "args": [], "attrs": {}, "rs": {"__ref": "39b7bEFMVaJ3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "-q6hj8NuwT3b": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-placement=top]", "displayName": "Placement (Top)"}, "GYD0ufF0cCb9": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-placement=bottom]", "displayName": "Placement (Bottom)"}, "61UsbUwssOYH": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-placement=left]", "displayName": "Placement (Left)"}, "aTDmCvyCVnUW": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-placement=right]", "displayName": "Placement (Right)"}, "ujcdmqbQTMix": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "s7c4K7p3y2ln": {"__type": "Var", "name": "trigger", "uuid": "B3A3glSJb3gS"}, "en8fP03ewSC4": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "HI2wgPj17d9e": {"__type": "Var", "name": "dialog", "uuid": "o8xIe9OGmukr"}, "xxtp6IvTz8mq": {"__type": "BoolType", "name": "bool"}, "0FN2x9XQ3Qgm": {"__type": "Var", "name": "isOpen", "uuid": "vE8lDsWxXwsT"}, "bdmABT4rGx2w": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "oL_6z8nAn-wl"}]}, "bqU9oCyZxONd": {"__type": "Var", "name": "onOpenChange", "uuid": "TLzfRCepGaXM"}, "k2u1TKeMT4Nb": {"__type": "TplSlot", "param": {"__ref": "TpGiUPZY8zXM"}, "defaultContents": [], "uuid": "7yYve_T9yWhO", "parent": {"__ref": "b8CA_B1dUdHr"}, "locked": null, "vsettings": [{"__ref": "lw6PsYxON9Wv"}]}, "uijThjpZL-Hu": {"__type": "TplSlot", "param": {"__ref": "7UYhJ8OM4Jyn"}, "defaultContents": [], "uuid": "4jAw_hMdYVh2", "parent": {"__ref": "b8CA_B1dUdHr"}, "locked": null, "vsettings": [{"__ref": "qayU2gBj55zM"}]}, "tITUd8E2M1Mk": {"__type": "VariantSetting", "variants": [{"__ref": "Qdams5aqVXe_"}], "args": [], "attrs": {}, "rs": {"__ref": "18lYYIzZ-HTb"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "kA-1kQ4F0jjz": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "mAgaCtsvf5NW": {"__type": "Var", "name": "children", "uuid": "xqXeuk_gB3RS"}, "Fi-T6DQYmhLD": {"__type": "TplSlot", "param": {"__ref": "bY3H2VzK-zzh"}, "defaultContents": [], "uuid": "cp9DFjlaXzOF", "parent": {"__ref": "55Pzq4Iq4kTE"}, "locked": null, "vsettings": [{"__ref": "MOtNQWErSs4f"}]}, "EfBijOC7hxZe": {"__type": "VariantSetting", "variants": [{"__ref": "z77jXHviIbWx"}], "args": [], "attrs": {}, "rs": {"__ref": "_t7h7FpA0Fbx"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "IMoXvFpJ5bst": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-disabled]", "displayName": "Disabled"}, "_MSZSei-Jd9h": {"__type": "Text", "name": "text"}, "10s-vcKvUNit": {"__type": "Var", "name": "name", "uuid": "E8Tcq8N6wM3_"}, "D3DYzdhj6JEK": {"__type": "BoolType", "name": "bool"}, "STCCF6wjECA6": {"__type": "Var", "name": "isDisabled", "uuid": "SDPpQzO3chP0"}, "hSolk6RN9zER": {"__type": "BoolType", "name": "bool"}, "XGPa1e4G2os7": {"__type": "Var", "name": "autoFocus", "uuid": "ceQIUeeixgZ5"}, "g-PEbNFk3FCg": {"__type": "BoolType", "name": "bool"}, "Td9Dk9lBX1W6": {"__type": "Var", "name": "advanced", "uuid": "ykLh0jr3V2YD"}, "rxmu9-Ivl4AJ": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "uriIWitURGwf": {"__type": "Var", "name": "children", "uuid": "VBZo-ud800Sk"}, "RLIxz_Xg8_JI": {"__type": "TplSlot", "param": {"__ref": "66iDNAtM0XDs"}, "defaultContents": [], "uuid": "zOFRixngPjYQ", "parent": {"__ref": "1VHPDxXudeyv"}, "locked": null, "vsettings": [{"__ref": "dk84MM0ER_Nh"}]}, "eiE-rZYVc-tj": {"__type": "VariantSetting", "variants": [{"__ref": "ezWNyNKnPxW2"}], "args": [], "attrs": {}, "rs": {"__ref": "tlxpozHoyzgW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "OUQkrDjPcbPs": {"__type": "RuleSet", "values": {"border-top-left-radius": "100%", "border-top-right-radius": "100%", "border-bottom-right-radius": "100%", "border-bottom-left-radius": "100%", "background": "linear-gradient(#C80101, #C80101)", "width": "20px", "height": "20px", "position": "absolute", "top": "5px", "cursor": "pointer"}, "mixins": []}, "dWBH-UvJeMEJ": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-dragging]", "displayName": "Dragging"}, "6tYdgtp7Lwx9": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-hovered]", "displayName": "Hovered"}, "cgK5F-K1iiSA": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focused]", "displayName": "Focused"}, "iY9UAUeaPIwF": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-focus-visible]", "displayName": "Focus Visible"}, "ZBkt5BpxO8_4": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-disabled]", "displayName": "Disabled"}, "XOZxT7L-bAf_": {"__type": "RenderableType", "name": "renderable", "params": [{"__ref": "XTuLUdIWyyGB"}], "allowRootWrapper": true}, "e7sXaqcp-beG": {"__type": "Var", "name": "children", "uuid": "iberE894Yk5e"}, "UzVfoEieCLrS": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "chUhrBy1HrN3": {"__type": "Var", "name": "progressBar", "uuid": "1DX5et_126sS"}, "RtfpbVdU140B": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "BrtDhF5Rr3zU"}]}, "Q_n10gtbZcnH": {"__type": "Var", "name": "onHoverStart", "uuid": "EfhoX7ssnSqq"}, "oChVQBqaRdw1": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "xLkFKm1eMckq"}]}, "MgkEkCgXREFK": {"__type": "Var", "name": "onHoverEnd", "uuid": "SXv6gc_cW8z3"}, "BEhxTwLrERIy": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "QG6TPwNelItD"}]}, "cC-s9jey48xj": {"__type": "Var", "name": "onHoverChange", "uuid": "-S8ow7v5Z7ph"}, "d4FCMKYc_fqh": {"__type": "TplSlot", "param": {"__ref": "FloImOO5Mh19"}, "defaultContents": [], "uuid": "-gjw0LMUvnB9", "parent": {"__ref": "ZKByXjXlu_do"}, "locked": null, "vsettings": [{"__ref": "SOT9aCqzhGDq"}]}, "gtNkHfwQLz0g": {"__type": "TplSlot", "param": {"__ref": "BUAMaP0Ml2L8"}, "defaultContents": [], "uuid": "bL7EkbUqovV6", "parent": {"__ref": "ZKByXjXlu_do"}, "locked": null, "vsettings": [{"__ref": "ggfWXAAtFkEe"}]}, "wy_-Mswp_EAU": {"__type": "VariantSetting", "variants": [{"__ref": "k3bYp7zKIlx4"}], "args": [], "attrs": {}, "rs": {"__ref": "evU5Pk6qTm7Y"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "ijZ_5vXzFBlb": {"__type": "RuleSet", "values": {"padding-top": "0", "padding-right": "0", "padding-bottom": "0", "padding-left": "0", "background": "linear-gradient(#aaa, #aaa)", "width": "stretch", "height": "10px", "position": "relative"}, "mixins": []}, "vIAC8hVdwW2M": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-hovered]", "displayName": "Hovered"}, "BrZIVopt2NKk": {"__type": "BoolType", "name": "bool"}, "HmhoLdkOkqfM": {"__type": "Var", "name": "isDisabled", "uuid": "GZcbdpO2AZM5"}, "SjZqBYreLW2t": {"__type": "Text", "name": "text"}, "LHpLx3F2oVQG": {"__type": "Var", "name": "aria-label", "uuid": "m7Z5vcrXk5r-"}, "0lrqngB69E_s": {"__type": "Choice", "name": "choice", "options": ["horizontal", "vertical"]}, "-GLK-YKDzKUB": {"__type": "Var", "name": "orientation", "uuid": "NC-NazUXeXje"}, "ls345aOF_Uyg": {"__type": "CustomCode", "code": "\"horizontal\"", "fallback": null}, "ha09dlrgaXch": {"__type": "<PERSON><PERSON>", "name": "num"}, "UfcOXkyZ326s": {"__type": "Var", "name": "minValue", "uuid": "hfZ5GO6kN_iW"}, "qomxA-F0ih8e": {"__type": "<PERSON><PERSON>", "name": "num"}, "7JClTjtf3-MR": {"__type": "Var", "name": "maxValue", "uuid": "6x2YddwJBKV4"}, "vrtz-YpXHxpa": {"__type": "<PERSON><PERSON>", "name": "num"}, "LrwA8ktG32Ba": {"__type": "Var", "name": "step", "uuid": "5oEVDuTnTuPa"}, "l0SSrerv7e4s": {"__type": "AnyType", "name": "any"}, "Qf9sDBnjOxbK": {"__type": "Var", "name": "value", "uuid": "xhRhmY_UPMO0"}, "qHyTftIa0KAI": {"__type": "CustomCode", "code": "[20,50]", "fallback": null}, "xjTrfF6oIOOC": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "hh4oPONCcEnS": {"__type": "Var", "name": "children", "uuid": "DLStUjX3GwyO"}, "dRqeNMBSdVl2": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "s0aCf3-IkErH"}]}, "QHG-txxJqR6X": {"__type": "Var", "name": "onChange", "uuid": "NjkZ6gL7mIOc"}, "7ybIiwT3j-Wr": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "82i1cs0fhKb0"}]}, "tSndEU00eXWV": {"__type": "Var", "name": "onChangeEnd", "uuid": "4pnkENmyLyep"}, "hKivXUVGZgVE": {"__type": "TplSlot", "param": {"__ref": "kuKtXBRtcaKS"}, "defaultContents": [], "uuid": "58Kb4iyZg2cc", "parent": {"__ref": "Vut3qb93ALXn"}, "locked": null, "vsettings": [{"__ref": "bcOyGEz5EPNp"}]}, "DNxngXC0fXRl": {"__type": "VariantSetting", "variants": [{"__ref": "b8RqNI6qtM1s"}], "args": [], "attrs": {}, "rs": {"__ref": "TL8BnQaYm92w"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "VjoEtY3euJhB": {"__type": "RuleSet", "values": {"width": "300px"}, "mixins": []}, "cyYXgpqD-224": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-disabled]", "displayName": "Disabled"}, "s2UKZx42aXAO": {"__type": "BoolType", "name": "bool"}, "RasmWHASZVhJ": {"__type": "Var", "name": "isDisabled", "uuid": "J-LE1Is7I9Nd"}, "OJAMDFVCVu_n": {"__type": "Text", "name": "text"}, "62QsTaIqc9o4": {"__type": "Var", "name": "aria-label", "uuid": "NuYJPX7q5ijO"}, "N5bL4f6kZf6b": {"__type": "Choice", "name": "choice", "options": ["horizontal", "vertical"]}, "JNFwjdvKc6P6": {"__type": "Var", "name": "orientation", "uuid": "HDm_BZHacVrT"}, "EHBO1F3b2yF2": {"__type": "CustomCode", "code": "\"horizontal\"", "fallback": null}, "DWIFvyCtlJMU": {"__type": "<PERSON><PERSON>", "name": "num"}, "FKfQjTJQCFMn": {"__type": "Var", "name": "minValue", "uuid": "emeiv5GwJF9l"}, "RuBRPCwBZJSS": {"__type": "<PERSON><PERSON>", "name": "num"}, "45bKS-GrHA74": {"__type": "Var", "name": "maxValue", "uuid": "X4fpUQm7WZtQ"}, "6JzSqk1r3Ost": {"__type": "<PERSON><PERSON>", "name": "num"}, "Hw75gRubxJKg": {"__type": "Var", "name": "step", "uuid": "k5we9v2d8_L2"}, "XflF2_zkoV2m": {"__type": "RenderableType", "name": "renderable", "params": [], "allowRootWrapper": null}, "Q-6NHccz1Qm7": {"__type": "Var", "name": "children", "uuid": "h-aZcQ56y7jA"}, "8ew-kQBAofF-": {"__type": "<PERSON><PERSON>", "name": "num"}, "Qe87ONt18-AR": {"__type": "Var", "name": "value", "uuid": "r45fA69esu0t"}, "WnsSlJpXQ0zH": {"__type": "CustomCode", "code": "0", "fallback": null}, "yyRwe-lqlSje": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "fjs1Sb4g4tag"}]}, "qV2M7a4Q_2IG": {"__type": "Var", "name": "onChange", "uuid": "EFKkxwpsq_jy"}, "WCY6ty33AgHJ": {"__type": "FunctionType", "name": "func", "params": [{"__ref": "BbTvBtQ642G3"}]}, "5JWKIS4vKqKn": {"__type": "Var", "name": "onChangeEnd", "uuid": "UNZKpnm_nbxT"}, "E_t-tCco_hFf": {"__type": "TplSlot", "param": {"__ref": "YKmpJEhO_UIG"}, "defaultContents": [], "uuid": "NireQ0dTsQ05", "parent": {"__ref": "fqFF4RipQaX4"}, "locked": null, "vsettings": [{"__ref": "Rcn-M8IHe82B"}]}, "1J4jxO7qpK5q": {"__type": "VariantSetting", "variants": [{"__ref": "Uuaz9SnkNpB0"}], "args": [], "attrs": {}, "rs": {"__ref": "PV6wL8ESX4by"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "F-5Q1faKcB5E": {"__type": "RuleSet", "values": {"width": "300px"}, "mixins": []}, "f4PgX4asvCjL": {"__type": "CodeComponentVariantMeta", "cssSelector": "[data-disabled]", "displayName": "Disabled"}, "UzmW-J5TAbOm": {"__type": "VariantSetting", "variants": [{"__ref": "7BOkqiB7Zne7"}], "args": [], "attrs": {}, "rs": {"__ref": "Yd6j9z6G4NiA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "4k3P_W1dfHau": {"__type": "VariantSetting", "variants": [{"__ref": "dGaJ3dAGvUvJ"}], "args": [], "attrs": {}, "rs": {"__ref": "Y_voRrBaAI7I"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "3QWghINpqQ2A": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "PCzgpU9N2Zq-": {"__type": "VariantSetting", "variants": [{"__ref": "RXqLI_eLgPt7"}], "args": [], "attrs": {}, "rs": {"__ref": "LjaP2-_r1zCl"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "fkSIJY3I-oRQ": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "gXtkPU1wCZOS": {"__type": "VariantSetting", "variants": [{"__ref": "7Lovrtb2Lat1"}], "args": [], "attrs": {}, "rs": {"__ref": "FIS9ZUGd7Z0N"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "_za__IlAxSPN": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "5BHVcLNwjiSC": {"__type": "VariantSetting", "variants": [{"__ref": "PzHz0FefVgTI"}], "args": [], "attrs": {}, "rs": {"__ref": "HEwMX0HetBky"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "IV9o9adoF2pL": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "18BG8n33mPCX": {"__type": "VariantSetting", "variants": [{"__ref": "n5BD8rXzj5QR"}], "args": [], "attrs": {}, "rs": {"__ref": "KEEhh8yFFzCU"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "K6tAk_Ym1FdK": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "hBuZK-PGud-y": {"__type": "VariantSetting", "variants": [{"__ref": "EqMkE8AI3N4F"}], "args": [], "attrs": {}, "rs": {"__ref": "_LZX64zVgfs0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "BQYkF00jAD5f": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "PRLoLNs7vRc7": {"__type": "ArgType", "name": "arg", "argName": "selected<PERSON><PERSON><PERSON>", "displayName": null, "type": {"__ref": "P33ZoLtmY0z7"}}, "PpDaQoUjK_Kz": {"__type": "ArgType", "name": "arg", "argName": "isOpen", "displayName": null, "type": {"__ref": "LzvWQmizB0Ks"}}, "hJjwMR3AQjcp": {"__type": "VariantSetting", "variants": [{"__ref": "r6qvgw_GHIEb"}], "args": [], "attrs": {}, "rs": {"__ref": "nc2WC5LsPaef"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "_HXiAMPDyu9g": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "sy8fHIPPcyDL": {"__type": "ArgType", "name": "arg", "argName": "selected<PERSON><PERSON><PERSON>", "displayName": null, "type": {"__ref": "JWPIer7I5GFY"}}, "b_TfCJMUGW5t": {"__type": "ArgType", "name": "arg", "argName": "isOpen", "displayName": null, "type": {"__ref": "PVje6AcMfPY2"}}, "mIrEWl4TD5OD": {"__type": "VariantSetting", "variants": [{"__ref": "m-y17Tx0tv99"}], "args": [], "attrs": {}, "rs": {"__ref": "9lp301BI_8Sm"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "_Vkt4GuQljNt": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "1E_hs3UIao6C": {"__type": "ArgType", "name": "arg", "argName": "event", "displayName": null, "type": {"__ref": "oVGc2GClVM97"}}, "yZk4Ihe3midI": {"__type": "ArgType", "name": "arg", "argName": "event", "displayName": null, "type": {"__ref": "cdvGgLCxPS9U"}}, "tIBz6FlUjE34": {"__type": "VariantSetting", "variants": [{"__ref": "3tpDmYiHYGZj"}], "args": [], "attrs": {}, "rs": {"__ref": "4C0EhvurNJ6k"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "68keyo8jMIzx": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "YI5KRHKJuhrb": {"__type": "VariantSetting", "variants": [{"__ref": "mEnjH3DU2iQz"}], "args": [], "attrs": {}, "rs": {"__ref": "ylV0xeXWQeAG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "gEMLLXglxCfz": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "gxbm5VXibyze": {"__type": "VariantSetting", "variants": [{"__ref": "GWQygEnv9jqh"}], "args": [], "attrs": {}, "rs": {"__ref": "omkPMZxmIMg1"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "cz2rg8VvTOiA": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "bJYfKJv78oZS": {"__type": "VariantSetting", "variants": [{"__ref": "fcl12scH4tr-"}], "args": [], "attrs": {}, "rs": {"__ref": "oB3zW_PA5YAB"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "PY5KXo_iOLpO": {"__type": "VariantSetting", "variants": [{"__ref": "fcl12scH4tr-"}], "args": [], "attrs": {}, "rs": {"__ref": "Q0bHMXgP3xwb"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "NLyNut7af54b": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "FEh3_bD7Fjtn": {"__type": "ComponentInstance", "name": "instance", "component": {"__ref": "S04ZmdyZZkRx"}}, "kAN_mwjZgAae": {"__type": "ComponentInstance", "name": "instance", "component": {"__ref": "KMhpYZizzUGY"}}, "EGSLodXDKqqn": {"__type": "ArgType", "name": "arg", "argName": "<PERSON><PERSON><PERSON><PERSON>", "displayName": null, "type": {"__ref": "8TyOvt_5IvKi"}}, "p_jdQSve0GRu": {"__type": "VariantSetting", "variants": [{"__ref": "PKdRLBegukyT"}], "args": [], "attrs": {}, "rs": {"__ref": "s2avOHYdLcAD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "RMeoVGPnpItY": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "QiPR6JsWWqlv": {"__type": "VariantSetting", "variants": [{"__ref": "7Ew-QWB5H_rA"}], "args": [], "attrs": {}, "rs": {"__ref": "XRVlfaN4zvaA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "JirUq2C0oOtu": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "DxYEkgCpdJ1C": {"__type": "ArgType", "name": "arg", "argName": "value", "displayName": null, "type": {"__ref": "fAlmRhK_7pkM"}}, "afrtsH1LBmFH": {"__type": "ArgType", "name": "arg", "argName": "focusEvent", "displayName": null, "type": {"__ref": "3uY3uCg91Z7G"}}, "DL0rn-uc8Nhi": {"__type": "ArgType", "name": "arg", "argName": "focusEvent", "displayName": null, "type": {"__ref": "BYrE5R4YHJu3"}}, "un9yuJ8vwbl-": {"__type": "ArgType", "name": "arg", "argName": "keyboardEvent", "displayName": null, "type": {"__ref": "AIEZiOYflZyr"}}, "IfVNic3-zyJ1": {"__type": "ArgType", "name": "arg", "argName": "keyboardEvent", "displayName": null, "type": {"__ref": "wZcvN2uNFsyT"}}, "VXx8eYOuKhNf": {"__type": "ArgType", "name": "arg", "argName": "clipbordEvent", "displayName": null, "type": {"__ref": "VEeaE5CDM8z_"}}, "Xjtdu7suHsEl": {"__type": "ArgType", "name": "arg", "argName": "clipbordEvent", "displayName": null, "type": {"__ref": "mQzcMe6xrrhd"}}, "aO_fZq1qVNl5": {"__type": "ArgType", "name": "arg", "argName": "clipbordEvent", "displayName": null, "type": {"__ref": "uE2SswMZFa2a"}}, "2pJZr-FIVBa3": {"__type": "ArgType", "name": "arg", "argName": "compositionEvent", "displayName": null, "type": {"__ref": "prGMzrYPLdIf"}}, "yNPTyN2_W2Fn": {"__type": "ArgType", "name": "arg", "argName": "compositionEvent", "displayName": null, "type": {"__ref": "rRs8llIvNuZL"}}, "2q0FeobO1YUb": {"__type": "ArgType", "name": "arg", "argName": "compositionEvent", "displayName": null, "type": {"__ref": "-YTDleW6aQM4"}}, "b_bk46CyDL0E": {"__type": "ArgType", "name": "arg", "argName": "selectionEvent", "displayName": null, "type": {"__ref": "j-jGyJFgInuD"}}, "zaQfVtHpDqmv": {"__type": "ArgType", "name": "arg", "argName": "inputEvent", "displayName": null, "type": {"__ref": "qbd1AErbHIB4"}}, "0pCqyLUmmtcL": {"__type": "ArgType", "name": "arg", "argName": "inputEvent", "displayName": null, "type": {"__ref": "fqZtz44vPNBH"}}, "WdMOJdW1m2F1": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "xyPTf4q2m782": {"__type": "ArgType", "name": "arg", "argName": "value", "displayName": null, "type": {"__ref": "9-m3f2xvvW96"}}, "26ubtInyGCwB": {"__type": "ArgType", "name": "arg", "argName": "focusEvent", "displayName": null, "type": {"__ref": "4m431MuyoFEF"}}, "E2t_PojpwXlk": {"__type": "ArgType", "name": "arg", "argName": "focusEvent", "displayName": null, "type": {"__ref": "wDNkKKUtLrUf"}}, "JRDwFAkyHibK": {"__type": "ArgType", "name": "arg", "argName": "keyboardEvent", "displayName": null, "type": {"__ref": "qnMBHMH3ZO44"}}, "F1UC3ACKkTXO": {"__type": "ArgType", "name": "arg", "argName": "keyboardEvent", "displayName": null, "type": {"__ref": "RQTa_hv5gJdY"}}, "mOvavMop2Qdx": {"__type": "ArgType", "name": "arg", "argName": "clipbordEvent", "displayName": null, "type": {"__ref": "iUBky7I2bT0i"}}, "9xHTXtpN1fLW": {"__type": "ArgType", "name": "arg", "argName": "clipbordEvent", "displayName": null, "type": {"__ref": "bfichykzMzqK"}}, "nGRPadIiYb4A": {"__type": "ArgType", "name": "arg", "argName": "clipbordEvent", "displayName": null, "type": {"__ref": "ywa_7UWOcP7F"}}, "-SHiqH7jX7ag": {"__type": "ArgType", "name": "arg", "argName": "compositionEvent", "displayName": null, "type": {"__ref": "m2cnVz485WoG"}}, "nfEz3K_i7ZPr": {"__type": "ArgType", "name": "arg", "argName": "compositionEvent", "displayName": null, "type": {"__ref": "UF37MlQx0lzU"}}, "jjI2E_OTXA6C": {"__type": "ArgType", "name": "arg", "argName": "compositionEvent", "displayName": null, "type": {"__ref": "nu9L62Wf-v38"}}, "fQzmjKmD5L5H": {"__type": "ArgType", "name": "arg", "argName": "selectionEvent", "displayName": null, "type": {"__ref": "SeXbdtKJnf1i"}}, "9TDvGNf4ppVa": {"__type": "ArgType", "name": "arg", "argName": "inputEvent", "displayName": null, "type": {"__ref": "-bbIGGEaDf2U"}}, "Sn662q-WqB9m": {"__type": "ArgType", "name": "arg", "argName": "inputEvent", "displayName": null, "type": {"__ref": "rZ77HPcACpPC"}}, "anO6Qmbgg-FX": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "eR_GnUlzXNRh": {"__type": "ArgType", "name": "arg", "argName": "isSelected", "displayName": null, "type": {"__ref": "i8isZh2uXI4Q"}}, "KYRCh9tjgdg4": {"__type": "VariantSetting", "variants": [{"__ref": "5_LRVlcQFHM_"}], "args": [], "attrs": {}, "rs": {"__ref": "6DnRgcsve0Kh"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "aWPT2tQO9k0i": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "aNx24tkvZAER": {"__type": "ArgType", "name": "arg", "argName": "isSelected", "displayName": null, "type": {"__ref": "O1U6RfiYPbwP"}}, "js1sXTmtGcjh": {"__type": "VariantSetting", "variants": [{"__ref": "kQh9Iwf1iIq7"}], "args": [], "attrs": {}, "rs": {"__ref": "0EcWhO4J5rCD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "rePXdasx3iWc": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "q7u57Won_MJK": {"__type": "ArgType", "name": "arg", "argName": "value", "displayName": null, "type": {"__ref": "27x60PJvcbj_"}}, "OzdIVwFDFdLF": {"__type": "VariantSetting", "variants": [{"__ref": "ukASI-FmmI6H"}], "args": [], "attrs": {}, "rs": {"__ref": "r98tQkaqsvfD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "tEgYy413b7V1": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "jUi3d8xcdnwe": {"__type": "VariantSetting", "variants": [{"__ref": "C__sH9cs49R0"}], "args": [], "attrs": {}, "rs": {"__ref": "KYLrLtxqBRDG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "O8GX0FysGZWr": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "OivwnX8YMajV": {"__type": "ArgType", "name": "arg", "argName": "value", "displayName": null, "type": {"__ref": "4PfgecsKU0we"}}, "GBxVcltvaVMj": {"__type": "VariantSetting", "variants": [{"__ref": "VB5YF0sw6gSV"}], "args": [], "attrs": {}, "rs": {"__ref": "VQ-g3phFOpHO"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "QVEMNmin-_7F": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "ZgYRd3P_dmGv": {"__type": "ArgType", "name": "arg", "argName": "value", "displayName": null, "type": {"__ref": "NyPoDSR9_0CG"}}, "OVM6CnlUSy3s": {"__type": "ArgType", "name": "arg", "argName": "focusEvent", "displayName": null, "type": {"__ref": "4Yz0xifKe0Tx"}}, "NJjmhNf03DdL": {"__type": "ArgType", "name": "arg", "argName": "focusEvent", "displayName": null, "type": {"__ref": "tknEeEHC6kMR"}}, "HAdptiTGaU1A": {"__type": "ArgType", "name": "arg", "argName": "isFocused", "displayName": null, "type": {"__ref": "zLKoMrJNUiJ6"}}, "ziXhIQYnr8vr": {"__type": "ArgType", "name": "arg", "argName": "keyboardEvent", "displayName": null, "type": {"__ref": "Yn6yHufvvBvi"}}, "rkdphp-_tdTb": {"__type": "ArgType", "name": "arg", "argName": "keyboardEvent", "displayName": null, "type": {"__ref": "BbhI0CsTq9Uc"}}, "Tis8E3zKbGBH": {"__type": "ArgType", "name": "arg", "argName": "clipbordEvent", "displayName": null, "type": {"__ref": "8aPIINSK6wij"}}, "y9G9AeHJKx7E": {"__type": "ArgType", "name": "arg", "argName": "clipbordEvent", "displayName": null, "type": {"__ref": "3Q51ynUZZQjY"}}, "n7YV1g8kPeOb": {"__type": "ArgType", "name": "arg", "argName": "clipbordEvent", "displayName": null, "type": {"__ref": "zCl6M42EM-n0"}}, "jZtbbSncrMY8": {"__type": "ArgType", "name": "arg", "argName": "compositionEvent", "displayName": null, "type": {"__ref": "wjXfrD17OTRX"}}, "VJyVM8sEuFjv": {"__type": "ArgType", "name": "arg", "argName": "compositionEvent", "displayName": null, "type": {"__ref": "yvIsIfoHpDwz"}}, "398eD2wTNSB7": {"__type": "ArgType", "name": "arg", "argName": "compositionEvent", "displayName": null, "type": {"__ref": "a60MG2JtEsLv"}}, "4jxLxwlviwNE": {"__type": "ArgType", "name": "arg", "argName": "selectionEvent", "displayName": null, "type": {"__ref": "bTvAoScIl-Jy"}}, "HUJKplHd9uTU": {"__type": "ArgType", "name": "arg", "argName": "inputEvent", "displayName": null, "type": {"__ref": "Fi3oyXqN9aep"}}, "Tf6GY771iC30": {"__type": "ArgType", "name": "arg", "argName": "inputEvent", "displayName": null, "type": {"__ref": "roma7ymfcac6"}}, "lZgxYyjL_Dlj": {"__type": "VariantSetting", "variants": [{"__ref": "IdcdHSnSjaIA"}], "args": [], "attrs": {}, "rs": {"__ref": "ryiSQlo42jJV"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "zxITxIuqxNR9": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "pqOarHvRq0f5": {"__type": "ArgType", "name": "arg", "argName": "isOpen", "displayName": null, "type": {"__ref": "HuasZHFPas2a"}}, "CaMn4a7nUnR7": {"__type": "VariantSetting", "variants": [{"__ref": "Bjzxxbl0bfKM"}], "args": [], "attrs": {}, "rs": {"__ref": "nbWHajKyCgh_"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "ZMN9QIvtkK3v": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "JnmThHNDXW0G": {"__type": "ArgType", "name": "arg", "argName": "isOpen", "displayName": null, "type": {"__ref": "2gBqvWvHedsF"}}, "RT-fK5UeFb6c": {"__type": "VariantSetting", "variants": [{"__ref": "fTwOQEtzhW4M"}], "args": [], "attrs": {}, "rs": {"__ref": "n53f1eLZuGlY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "65b9CV25fCtY": {"__type": "VariantSetting", "variants": [{"__ref": "fTwOQEtzhW4M"}], "args": [], "attrs": {}, "rs": {"__ref": "RloxlCFpVW0q"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "39b7bEFMVaJ3": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "oL_6z8nAn-wl": {"__type": "ArgType", "name": "arg", "argName": "isOpen", "displayName": null, "type": {"__ref": "gMoqtLLOp2EE"}}, "lw6PsYxON9Wv": {"__type": "VariantSetting", "variants": [{"__ref": "Qdams5aqVXe_"}], "args": [], "attrs": {}, "rs": {"__ref": "ltm1ZdjhgXiG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "qayU2gBj55zM": {"__type": "VariantSetting", "variants": [{"__ref": "Qdams5aqVXe_"}], "args": [], "attrs": {}, "rs": {"__ref": "4oB7hU5OIwPw"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "18lYYIzZ-HTb": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "MOtNQWErSs4f": {"__type": "VariantSetting", "variants": [{"__ref": "z77jXHviIbWx"}], "args": [], "attrs": {}, "rs": {"__ref": "er0yUmR9gvog"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "_t7h7FpA0Fbx": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "dk84MM0ER_Nh": {"__type": "VariantSetting", "variants": [{"__ref": "ezWNyNKnPxW2"}], "args": [], "attrs": {}, "rs": {"__ref": "5CUH0M77yqjq"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "tlxpozHoyzgW": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "XTuLUdIWyyGB": {"__type": "ComponentInstance", "name": "instance", "component": {"__ref": "zSvSCDUhsjJz"}}, "BrtDhF5Rr3zU": {"__type": "ArgType", "name": "arg", "argName": "event", "displayName": null, "type": {"__ref": "2YnwS4pIw3k5"}}, "xLkFKm1eMckq": {"__type": "ArgType", "name": "arg", "argName": "event", "displayName": null, "type": {"__ref": "i56jGsdhUw6F"}}, "QG6TPwNelItD": {"__type": "ArgType", "name": "arg", "argName": "isHovering", "displayName": null, "type": {"__ref": "VJFyvpRoBXun"}}, "SOT9aCqzhGDq": {"__type": "VariantSetting", "variants": [{"__ref": "k3bYp7zKIlx4"}], "args": [], "attrs": {}, "rs": {"__ref": "ZVZqiRE_2Q-3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "ggfWXAAtFkEe": {"__type": "VariantSetting", "variants": [{"__ref": "k3bYp7zKIlx4"}], "args": [], "attrs": {}, "rs": {"__ref": "9Fx1aZxJVUCk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "evU5Pk6qTm7Y": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "s0aCf3-IkErH": {"__type": "ArgType", "name": "arg", "argName": "value", "displayName": null, "type": {"__ref": "fnNg_zIMCHfq"}}, "82i1cs0fhKb0": {"__type": "ArgType", "name": "arg", "argName": "value", "displayName": null, "type": {"__ref": "Tm4Ue0ggShZa"}}, "bcOyGEz5EPNp": {"__type": "VariantSetting", "variants": [{"__ref": "b8RqNI6qtM1s"}], "args": [], "attrs": {}, "rs": {"__ref": "_2HU7iJKpK3W"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "TL8BnQaYm92w": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "fjs1Sb4g4tag": {"__type": "ArgType", "name": "arg", "argName": "value", "displayName": null, "type": {"__ref": "Ev0CouWghoCd"}}, "BbTvBtQ642G3": {"__type": "ArgType", "name": "arg", "argName": "value", "displayName": null, "type": {"__ref": "5Kk8UR2pVlYk"}}, "Rcn-M8IHe82B": {"__type": "VariantSetting", "variants": [{"__ref": "Uuaz9SnkNpB0"}], "args": [], "attrs": {}, "rs": {"__ref": "nUvU817bgkzM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "PV6wL8ESX4by": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "Yd6j9z6G4NiA": {"__type": "RuleSet", "values": {"display": "flex", "flex-direction": "row"}, "mixins": []}, "Y_voRrBaAI7I": {"__type": "RuleSet", "values": {}, "mixins": []}, "LjaP2-_r1zCl": {"__type": "RuleSet", "values": {}, "mixins": []}, "FIS9ZUGd7Z0N": {"__type": "RuleSet", "values": {}, "mixins": []}, "HEwMX0HetBky": {"__type": "RuleSet", "values": {}, "mixins": []}, "KEEhh8yFFzCU": {"__type": "RuleSet", "values": {}, "mixins": []}, "_LZX64zVgfs0": {"__type": "RuleSet", "values": {}, "mixins": []}, "P33ZoLtmY0z7": {"__type": "Text", "name": "text"}, "LzvWQmizB0Ks": {"__type": "BoolType", "name": "bool"}, "nc2WC5LsPaef": {"__type": "RuleSet", "values": {}, "mixins": []}, "JWPIer7I5GFY": {"__type": "Text", "name": "text"}, "PVje6AcMfPY2": {"__type": "BoolType", "name": "bool"}, "9lp301BI_8Sm": {"__type": "RuleSet", "values": {}, "mixins": []}, "oVGc2GClVM97": {"__type": "AnyType", "name": "any"}, "cdvGgLCxPS9U": {"__type": "AnyType", "name": "any"}, "4C0EhvurNJ6k": {"__type": "RuleSet", "values": {}, "mixins": []}, "ylV0xeXWQeAG": {"__type": "RuleSet", "values": {}, "mixins": []}, "omkPMZxmIMg1": {"__type": "RuleSet", "values": {}, "mixins": []}, "oB3zW_PA5YAB": {"__type": "RuleSet", "values": {}, "mixins": []}, "Q0bHMXgP3xwb": {"__type": "RuleSet", "values": {}, "mixins": []}, "8TyOvt_5IvKi": {"__type": "AnyType", "name": "any"}, "s2avOHYdLcAD": {"__type": "RuleSet", "values": {}, "mixins": []}, "XRVlfaN4zvaA": {"__type": "RuleSet", "values": {}, "mixins": []}, "fAlmRhK_7pkM": {"__type": "Text", "name": "text"}, "3uY3uCg91Z7G": {"__type": "AnyType", "name": "any"}, "BYrE5R4YHJu3": {"__type": "AnyType", "name": "any"}, "AIEZiOYflZyr": {"__type": "AnyType", "name": "any"}, "wZcvN2uNFsyT": {"__type": "AnyType", "name": "any"}, "VEeaE5CDM8z_": {"__type": "AnyType", "name": "any"}, "mQzcMe6xrrhd": {"__type": "AnyType", "name": "any"}, "uE2SswMZFa2a": {"__type": "AnyType", "name": "any"}, "prGMzrYPLdIf": {"__type": "AnyType", "name": "any"}, "rRs8llIvNuZL": {"__type": "AnyType", "name": "any"}, "-YTDleW6aQM4": {"__type": "AnyType", "name": "any"}, "j-jGyJFgInuD": {"__type": "AnyType", "name": "any"}, "qbd1AErbHIB4": {"__type": "AnyType", "name": "any"}, "fqZtz44vPNBH": {"__type": "AnyType", "name": "any"}, "9-m3f2xvvW96": {"__type": "Text", "name": "text"}, "4m431MuyoFEF": {"__type": "AnyType", "name": "any"}, "wDNkKKUtLrUf": {"__type": "AnyType", "name": "any"}, "qnMBHMH3ZO44": {"__type": "AnyType", "name": "any"}, "RQTa_hv5gJdY": {"__type": "AnyType", "name": "any"}, "iUBky7I2bT0i": {"__type": "AnyType", "name": "any"}, "bfichykzMzqK": {"__type": "AnyType", "name": "any"}, "ywa_7UWOcP7F": {"__type": "AnyType", "name": "any"}, "m2cnVz485WoG": {"__type": "AnyType", "name": "any"}, "UF37MlQx0lzU": {"__type": "AnyType", "name": "any"}, "nu9L62Wf-v38": {"__type": "AnyType", "name": "any"}, "SeXbdtKJnf1i": {"__type": "AnyType", "name": "any"}, "-bbIGGEaDf2U": {"__type": "AnyType", "name": "any"}, "rZ77HPcACpPC": {"__type": "AnyType", "name": "any"}, "i8isZh2uXI4Q": {"__type": "BoolType", "name": "bool"}, "6DnRgcsve0Kh": {"__type": "RuleSet", "values": {}, "mixins": []}, "O1U6RfiYPbwP": {"__type": "BoolType", "name": "bool"}, "0EcWhO4J5rCD": {"__type": "RuleSet", "values": {}, "mixins": []}, "27x60PJvcbj_": {"__type": "AnyType", "name": "any"}, "r98tQkaqsvfD": {"__type": "RuleSet", "values": {}, "mixins": []}, "KYLrLtxqBRDG": {"__type": "RuleSet", "values": {}, "mixins": []}, "4PfgecsKU0we": {"__type": "Text", "name": "text"}, "VQ-g3phFOpHO": {"__type": "RuleSet", "values": {}, "mixins": []}, "NyPoDSR9_0CG": {"__type": "Text", "name": "text"}, "4Yz0xifKe0Tx": {"__type": "AnyType", "name": "any"}, "tknEeEHC6kMR": {"__type": "AnyType", "name": "any"}, "zLKoMrJNUiJ6": {"__type": "BoolType", "name": "bool"}, "Yn6yHufvvBvi": {"__type": "AnyType", "name": "any"}, "BbhI0CsTq9Uc": {"__type": "AnyType", "name": "any"}, "8aPIINSK6wij": {"__type": "AnyType", "name": "any"}, "3Q51ynUZZQjY": {"__type": "AnyType", "name": "any"}, "zCl6M42EM-n0": {"__type": "AnyType", "name": "any"}, "wjXfrD17OTRX": {"__type": "AnyType", "name": "any"}, "yvIsIfoHpDwz": {"__type": "AnyType", "name": "any"}, "a60MG2JtEsLv": {"__type": "AnyType", "name": "any"}, "bTvAoScIl-Jy": {"__type": "AnyType", "name": "any"}, "Fi3oyXqN9aep": {"__type": "AnyType", "name": "any"}, "roma7ymfcac6": {"__type": "AnyType", "name": "any"}, "ryiSQlo42jJV": {"__type": "RuleSet", "values": {}, "mixins": []}, "HuasZHFPas2a": {"__type": "BoolType", "name": "bool"}, "nbWHajKyCgh_": {"__type": "RuleSet", "values": {}, "mixins": []}, "2gBqvWvHedsF": {"__type": "BoolType", "name": "bool"}, "n53f1eLZuGlY": {"__type": "RuleSet", "values": {}, "mixins": []}, "RloxlCFpVW0q": {"__type": "RuleSet", "values": {}, "mixins": []}, "gMoqtLLOp2EE": {"__type": "BoolType", "name": "bool"}, "ltm1ZdjhgXiG": {"__type": "RuleSet", "values": {}, "mixins": []}, "4oB7hU5OIwPw": {"__type": "RuleSet", "values": {}, "mixins": []}, "er0yUmR9gvog": {"__type": "RuleSet", "values": {}, "mixins": []}, "5CUH0M77yqjq": {"__type": "RuleSet", "values": {}, "mixins": []}, "2YnwS4pIw3k5": {"__type": "AnyType", "name": "any"}, "i56jGsdhUw6F": {"__type": "AnyType", "name": "any"}, "VJFyvpRoBXun": {"__type": "BoolType", "name": "bool"}, "ZVZqiRE_2Q-3": {"__type": "RuleSet", "values": {}, "mixins": []}, "9Fx1aZxJVUCk": {"__type": "RuleSet", "values": {}, "mixins": []}, "fnNg_zIMCHfq": {"__type": "AnyType", "name": "any"}, "Tm4Ue0ggShZa": {"__type": "AnyType", "name": "any"}, "_2HU7iJKpK3W": {"__type": "RuleSet", "values": {}, "mixins": []}, "Ev0CouWghoCd": {"__type": "<PERSON><PERSON>", "name": "num"}, "5Kk8UR2pVlYk": {"__type": "<PERSON><PERSON>", "name": "num"}, "nUvU817bgkzM": {"__type": "RuleSet", "values": {}, "mixins": []}, "RJ9Iqajlsc3E": {"__type": "Component", "uuid": "tW98DgSciuLk", "name": "NewPage", "params": [], "states": [], "tplTree": {"__ref": "nkjyp3oSPYqJ"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "QhV9Gj0xZMMD"}], "variantGroups": [], "pageMeta": {"__ref": "KcbLS3NbDhOq"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false}, "4SrwZ0MOWvO3": {"__type": "PageArena", "component": {"__ref": "RJ9Iqajlsc3E"}, "matrix": {"__ref": "oRedrp9Y165y"}, "customMatrix": {"__ref": "97-8CxGb3ojF"}}, "nkjyp3oSPYqJ": {"__type": "TplTag", "tag": "div", "name": null, "children": [{"__ref": "3uhUsiOkwuks"}, {"__ref": "waC8xsU-xZot"}, {"__ref": "vIIOlQSgeOG1"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Fh0GqFV1hk3S", "parent": null, "locked": null, "vsettings": [{"__ref": "-XW4-D0it8lS"}]}, "QhV9Gj0xZMMD": {"__type": "<PERSON><PERSON><PERSON>", "uuid": "ksiLJy-BEYOP", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null}, "KcbLS3NbDhOq": {"__type": "PageMeta", "path": "/new-page", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null}, "oRedrp9Y165y": {"__type": "ArenaFrameGrid", "rows": [{"__ref": "OiWOZ3hpygdo"}]}, "97-8CxGb3ojF": {"__type": "ArenaFrameGrid", "rows": [{"__ref": "xymVx4VZUtZ4"}]}, "-XW4-D0it8lS": {"__type": "VariantSetting", "variants": [{"__ref": "QhV9Gj0xZMMD"}], "args": [], "attrs": {}, "rs": {"__ref": "4nLJTKSjahdB"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "OiWOZ3hpygdo": {"__type": "ArenaFrameRow", "cols": [{"__ref": "3-m5XdsuAuDZ"}, {"__ref": "aOhSVR2Ci7kY"}], "rowKey": {"__ref": "QhV9Gj0xZMMD"}}, "xymVx4VZUtZ4": {"__type": "ArenaFrameRow", "cols": [], "rowKey": null}, "4nLJTKSjahdB": {"__type": "RuleSet", "values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center"}, "mixins": []}, "3-m5XdsuAuDZ": {"__type": "ArenaFrameCell", "frame": {"__ref": "Fuqxm9gkdzKe"}, "cellKey": null}, "aOhSVR2Ci7kY": {"__type": "ArenaFrameCell", "frame": {"__ref": "iUFf6rPMNsP5"}, "cellKey": null}, "Fuqxm9gkdzKe": {"__type": "ArenaFrame", "uuid": "B8Hq8Xl6x27D", "width": 1366, "height": 768, "container": {"__ref": "ES0ZG89pCDfL"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "QhV9Gj0xZMMD"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null}, "iUFf6rPMNsP5": {"__type": "ArenaFrame", "uuid": "NMm7iDVTEyPa", "width": 414, "height": 736, "container": {"__ref": "Kuoom2YojKxT"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "QhV9Gj0xZMMD"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null}, "ES0ZG89pCDfL": {"__type": "TplComponent", "name": null, "component": {"__ref": "RJ9Iqajlsc3E"}, "uuid": "LGhGY1Ihps14", "parent": null, "locked": null, "vsettings": [{"__ref": "5e6hXvccqiF4"}]}, "Kuoom2YojKxT": {"__type": "TplComponent", "name": null, "component": {"__ref": "RJ9Iqajlsc3E"}, "uuid": "MqiLzr4LGKTc", "parent": null, "locked": null, "vsettings": [{"__ref": "9bd67PcTm0ts"}]}, "5e6hXvccqiF4": {"__type": "VariantSetting", "variants": [{"__ref": "UIvZWnR-Ngmy"}], "args": [], "attrs": {}, "rs": {"__ref": "-Yr1HRpuc-Yq"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "9bd67PcTm0ts": {"__type": "VariantSetting", "variants": [{"__ref": "UIvZWnR-Ngmy"}], "args": [], "attrs": {}, "rs": {"__ref": "BQD0EHtzuUDN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "-Yr1HRpuc-Yq": {"__type": "RuleSet", "values": {}, "mixins": []}, "BQD0EHtzuUDN": {"__type": "RuleSet", "values": {}, "mixins": []}, "3uhUsiOkwuks": {"__type": "TplTag", "tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "pUryuC1AtkW4", "parent": {"__ref": "nkjyp3oSPYqJ"}, "locked": null, "vsettings": [{"__ref": "KJsZy7J5Y8vs"}]}, "KJsZy7J5Y8vs": {"__type": "VariantSetting", "variants": [{"__ref": "QhV9Gj0xZMMD"}], "args": [], "attrs": {}, "rs": {"__ref": "GJLxcZegahXF"}, "dataCond": null, "dataRep": null, "text": {"__ref": "lCZsC8ufYkgp"}, "columnsConfig": null}, "GJLxcZegahXF": {"__type": "RuleSet", "values": {"position": "relative"}, "mixins": []}, "lCZsC8ufYkgp": {"__type": "RawText", "markers": [], "text": "Enter some text"}, "waC8xsU-xZot": {"__type": "TplComponent", "name": "Counter", "component": {"__ref": "rzhFuVDaPvrN"}, "uuid": "661PpA4YH_4L", "parent": {"__ref": "nkjyp3oSPYqJ"}, "locked": null, "vsettings": [{"__ref": "t3a_t3gxATjb"}]}, "t3a_t3gxATjb": {"__type": "VariantSetting", "variants": [{"__ref": "QhV9Gj0xZMMD"}], "args": [], "attrs": {}, "rs": {"__ref": "orNadRZUH-Rj"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "orNadRZUH-Rj": {"__type": "RuleSet", "values": {"max-width": "100%", "object-fit": "cover"}, "mixins": []}, "vIIOlQSgeOG1": {"__type": "TplComponent", "name": null, "component": {"__ref": "ogA3Ba-Zhixt"}, "uuid": "f1YfW3YTC_6M", "parent": {"__ref": "nkjyp3oSPYqJ"}, "locked": null, "vsettings": [{"__ref": "g8aYRUeAgizB"}]}, "g8aYRUeAgizB": {"__type": "VariantSetting", "variants": [{"__ref": "QhV9Gj0xZMMD"}], "args": [{"__ref": "MfXfnjrKS8x5"}], "attrs": {}, "rs": {"__ref": "zaJRxz4oPLzV"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null}, "MfXfnjrKS8x5": {"__type": "Arg", "param": {"__ref": "Eqwt1eEARK-2"}, "expr": {"__ref": "_mvSF4-Jc1cr"}}, "zaJRxz4oPLzV": {"__type": "RuleSet", "values": {"padding-top": "2px", "padding-right": "10px", "padding-bottom": "2px", "padding-left": "10px", "border-top-width": "1px", "border-right-width": "1px", "border-bottom-width": "1px", "border-left-width": "1px", "border-top-style": "solid", "border-right-style": "solid", "border-bottom-style": "solid", "border-left-style": "solid", "border-top-color": "black", "border-right-color": "black", "border-bottom-color": "black", "border-left-color": "black", "background": "linear-gradient(#EFEFEF, #EFEFEF)", "color": "#000000", "cursor": "pointer", "font-family": "<PERSON><PERSON>", "font-size": "1rem", "line-height": "1.2", "text-decoration-line": "none", "max-width": "100%", "object-fit": "cover"}, "mixins": []}, "_mvSF4-Jc1cr": {"__type": "RenderExpr", "tpl": [{"__ref": "_bmxuO8JMH2A"}]}, "_bmxuO8JMH2A": {"__type": "TplTag", "tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "Vb-Re75Fl0So", "parent": {"__ref": "vIIOlQSgeOG1"}, "locked": null, "vsettings": [{"__ref": "2MNHafc1HMRu"}]}, "2MNHafc1HMRu": {"__type": "VariantSetting", "variants": [{"__ref": "QhV9Gj0xZMMD"}], "args": [], "attrs": {}, "rs": {"__ref": "zP9zIsXxwd1L"}, "dataCond": null, "dataRep": null, "text": {"__ref": "iV2sWwaSguNr"}, "columnsConfig": null}, "zP9zIsXxwd1L": {"__type": "RuleSet", "values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": []}, "iV2sWwaSguNr": {"__type": "RawText", "markers": [], "text": "<PERSON><PERSON>"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}