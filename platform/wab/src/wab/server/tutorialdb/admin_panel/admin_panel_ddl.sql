begin;

drop table if exists customers cascade;
drop table if exists orders cascade;
drop sequence if exists customers_id_seq cascade;
drop sequence if exists order_id_order_id_seq cascade;

--
-- Name: customers; Type: TABLE; Schema: public;
--

CREATE TABLE public.customers (
    user_id integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    username text,
    email text,
    password character varying,
    country text,
    first_name text,
    last_name text
);


--
-- Name: customers_id_seq; Type: SEQUENCE; Schema: public;
--

ALTER TABLE public.customers ALTER COLUMN user_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.customers_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

--
-- Name: orders; Type: TABLE; Schema: public;
--

CREATE TABLE public.orders (
    order_id integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    total_price numeric,
    is_paid boolean,
    is_shipped boolean,
    status text,
    user_id integer,
    shipped_at timestamp with time zone
);

--
-- Name: order_id_order_id_seq; Type: SEQUENCE; Schema: public;
--

ALTER TABLE public.orders ALTER COLUMN order_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.order_id_order_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

commit;