// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import PlasmicOptimizationOption from "@/wab/client/plasmic/plasmic_kit_analytics/PlasmicOptimizationOption";
import {
  DefaultOptimizationsSelectProps,
  PlasmicOptimizationsSelect,
} from "@/wab/client/plasmic/plasmic_kit_analytics/PlasmicOptimizationsSelect";
import { HTMLElementRefOf } from "@plasmicapp/react-web";
import * as React from "react";

export interface OptimizationsSelectProps
  extends DefaultOptimizationsSelectProps {
  options?: Array<{ label: string; value?: string }>;
  selected?: string;
  onChange: (_?: string) => void;
}

function OptimizationsSelect_(
  props: OptimizationsSelectProps,
  ref: HTMLElementRefOf<"div">
) {
  const { options, selected, onChange, ...rest } = props;

  return (
    <PlasmicOptimizationsSelect
      root={{ ref }}
      {...rest}
      children={(options ?? []).map(({ label, value }) => (
        <PlasmicOptimizationOption
          key={label}
          label={label}
          unset={!value}
          selected={value === selected || (!value && !selected)}
          onClick={() => onChange(value)}
        />
      ))}
    />
  );
}

const OptimizationsSelect = React.forwardRef(OptimizationsSelect_);
export default OptimizationsSelect;
