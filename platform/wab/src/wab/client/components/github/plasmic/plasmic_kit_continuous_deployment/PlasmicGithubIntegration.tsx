/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: fpbcKyXdMTvY59T4C5fjcC
// Component: FuvSZfvXL5

import * as React from "react";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicLink as PlasmicLink__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  generateStateOnChangeProp,
  generateStateValueProp,
  hasVariant,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import Button from "../../../widgets/Button"; // plasmic-import: SEF-sRmSoqV5c/component
import Select from "../../../widgets/Select"; // plasmic-import: j_4IQyOWK2b/component
import Switch from "../../../widgets/Switch"; // plasmic-import: b35JDgXpbiF/component
import ErrorFeedback from "../../ErrorFeedback"; // plasmic-import: 6ztKJ9-EG9Y/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_css from "../../../../plasmic/PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../../../../plasmic/plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import projectcss from "../../../modals/plasmic/plasmic_kit_project_settings/plasmic_plasmic_kit_project_settings.module.css"; // plasmic-import: fpbcKyXdMTvY59T4C5fjcC/projectcss
import sty from "./PlasmicGithubIntegration.module.css"; // plasmic-import: FuvSZfvXL5/css

import InfoIcon from "../../../../plasmic/plasmic_kit/PlasmicIcon__Info"; // plasmic-import: BjAly3N4fWuWe/icon
import OpenIcon from "../../../../plasmic/plasmic_kit/PlasmicIcon__Open"; // plasmic-import: 7D0GDLdF72udM/icon
import ArrowRightSvgIcon from "../../../../plasmic/plasmic_kit_icons/icons/PlasmicIcon__ArrowRightSvg"; // plasmic-import: 9Jv8jb253/icon
import ArrowUpRightSvgIcon from "../../../../plasmic/plasmic_kit_icons/icons/PlasmicIcon__ArrowUpRightSvg"; // plasmic-import: N_BtK6grX/icon
import ChevronDownSvgIcon from "../../../../plasmic/plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg"; // plasmic-import: xZrB9_0ir/icon
import PlusSvgIcon from "../../../../plasmic/plasmic_kit_icons/icons/PlasmicIcon__PlusSvg"; // plasmic-import: sQKgd2GNr/icon

createPlasmicElementProxy;

export type PlasmicGithubIntegration__VariantMembers = {
  view: "existingRepo";
  errors:
    | "name"
    | "directory"
    | "hasPublishSiteError"
    | "hasDomainError"
    | "missingDomain"
    | "invalidDomainError"
    | "publishSiteWarning";
  loading: "githubData" | "branches" | "detectedOptions" | "saving";
  hide: "action";
  isPublishingSite: "isPublishingSite";
  hideGithubPages: "hideGithubPages";
};
export type PlasmicGithubIntegration__VariantsArgs = {
  view?: SingleChoiceArg<"existingRepo">;
  errors?: MultiChoiceArg<
    | "name"
    | "directory"
    | "hasPublishSiteError"
    | "hasDomainError"
    | "missingDomain"
    | "invalidDomainError"
    | "publishSiteWarning"
  >;
  loading?: MultiChoiceArg<
    "githubData" | "branches" | "detectedOptions" | "saving"
  >;
  hide?: MultiChoiceArg<"action">;
  isPublishingSite?: SingleBooleanChoiceArg<"isPublishingSite">;
  hideGithubPages?: SingleBooleanChoiceArg<"hideGithubPages">;
};
type VariantPropType = keyof PlasmicGithubIntegration__VariantsArgs;
export const PlasmicGithubIntegration__VariantProps =
  new Array<VariantPropType>(
    "view",
    "errors",
    "loading",
    "hide",
    "isPublishingSite",
    "hideGithubPages"
  );

export type PlasmicGithubIntegration__ArgsType = {};
type ArgPropType = keyof PlasmicGithubIntegration__ArgsType;
export const PlasmicGithubIntegration__ArgProps = new Array<ArgPropType>();

export type PlasmicGithubIntegration__OverridesType = {
  root?: Flex__<"div">;
  newRepoButton?: Flex__<typeof Button>;
  existingRepoButton?: Flex__<typeof Button>;
  newBox?: Flex__<"div">;
  orgBox?: Flex__<"div">;
  org?: Flex__<typeof Select>;
  missingOrg?: Flex__<typeof Button>;
  nameBox?: Flex__<"div">;
  name?: Flex__<"input">;
  nameError?: Flex__<"div">;
  privateBox?: Flex__<"div">;
  privateRepo?: Flex__<typeof Switch>;
  existingBox?: Flex__<"div">;
  repositoryBox?: Flex__<"div">;
  repository?: Flex__<typeof Select>;
  missingRepo?: Flex__<typeof Button>;
  branchBox?: Flex__<"div">;
  branch?: Flex__<typeof Select>;
  directoryBox?: Flex__<"div">;
  directory?: Flex__<"input">;
  directoryError?: Flex__<"div">;
  opts?: Flex__<"div">;
  frameworkBox?: Flex__<"div">;
  framework?: Flex__<typeof Select>;
  languageBox?: Flex__<"div">;
  language?: Flex__<typeof Select>;
  modeBox?: Flex__<"div">;
  modeInfo?: Flex__<"svg">;
  mode?: Flex__<typeof Select>;
  actionBox?: Flex__<"div">;
  actionInfo?: Flex__<"svg">;
  action?: Flex__<typeof Select>;
  deployment?: Flex__<"div">;
  publishSiteLabel?: Flex__<"div">;
  publishSite?: Flex__<typeof Switch>;
  publishSiteError?: Flex__<typeof ErrorFeedback>;
  domainError2?: Flex__<typeof ErrorFeedback>;
  apparentSubdomainInput?: Flex__<"div">;
  subdomainInput?: Flex__<"input">;
  domainError?: Flex__<typeof ErrorFeedback>;
  moreProvidersLink?: Flex__<"a">;
  pushButton?: Flex__<typeof Button>;
};

export interface DefaultGithubIntegrationProps {
  view?: SingleChoiceArg<"existingRepo">;
  errors?: MultiChoiceArg<
    | "name"
    | "directory"
    | "hasPublishSiteError"
    | "hasDomainError"
    | "missingDomain"
    | "invalidDomainError"
    | "publishSiteWarning"
  >;
  loading?: MultiChoiceArg<
    "githubData" | "branches" | "detectedOptions" | "saving"
  >;
  hide?: MultiChoiceArg<"action">;
  isPublishingSite?: SingleBooleanChoiceArg<"isPublishingSite">;
  hideGithubPages?: SingleBooleanChoiceArg<"hideGithubPages">;
  className?: string;
}

const $$ = {};

function PlasmicGithubIntegration__RenderFunc(props: {
  variants: PlasmicGithubIntegration__VariantsArgs;
  args: PlasmicGithubIntegration__ArgsType;
  overrides: PlasmicGithubIntegration__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "view",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.view,
      },
      {
        path: "errors",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.errors,
      },
      {
        path: "loading",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.loading,
      },
      {
        path: "hide",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.hide,
      },
      {
        path: "isPublishingSite",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.isPublishingSite,
      },
      {
        path: "hideGithubPages",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.hideGithubPages,
      },
      {
        path: "org.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
      {
        path: "privateRepo.isChecked",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
      {
        path: "repository.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
      {
        path: "branch.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
      {
        path: "framework.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
      {
        path: "language.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
      {
        path: "mode.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
      {
        path: "action.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
      {
        path: "publishSite.isChecked",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          hasVariant($state, "isPublishingSite", "isPublishingSite")
            ? "isChecked"
            : undefined,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <Stack__
      as={"div"}
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      hasGap={true}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        {
          [sty.roothide_action]: hasVariant($state, "hide", "action"),
          [sty.rootloading_githubData]: hasVariant(
            $state,
            "loading",
            "githubData"
          ),
          [sty.rootview_existingRepo]: hasVariant(
            $state,
            "view",
            "existingRepo"
          ),
        }
      )}
    >
      <Stack__
        as={"div"}
        hasGap={true}
        className={classNames(projectcss.all, sty.freeBox__w2So6, {
          [sty.freeBoxerrors_name__w2So6NelV0]: hasVariant(
            $state,
            "errors",
            "name"
          ),
          [sty.freeBoxerrors_publishSiteWarning_isPublishingSite__w2So62OGsOQOy2M]:
            hasVariant($state, "errors", "publishSiteWarning") &&
            hasVariant($state, "isPublishingSite", "isPublishingSite"),
          [sty.freeBoxhide_action__w2So6ZcgUd]: hasVariant(
            $state,
            "hide",
            "action"
          ),
          [sty.freeBoxisPublishingSite__w2So6QOy2M]: hasVariant(
            $state,
            "isPublishingSite",
            "isPublishingSite"
          ),
          [sty.freeBoxloading_detectedOptions__w2So6HilqL]: hasVariant(
            $state,
            "loading",
            "detectedOptions"
          ),
          [sty.freeBoxloading_githubData__w2So60KfO]: hasVariant(
            $state,
            "loading",
            "githubData"
          ),
          [sty.freeBoxview_existingRepo__w2So6EHbf9]: hasVariant(
            $state,
            "view",
            "existingRepo"
          ),
        })}
      >
        <div
          className={classNames(projectcss.all, sty.freeBox__oumws, {
            [sty.freeBoxview_existingRepo__oumwseHbf9]: hasVariant(
              $state,
              "view",
              "existingRepo"
            ),
          })}
        >
          <Button
            data-plasmic-name={"newRepoButton"}
            data-plasmic-override={overrides.newRepoButton}
            className={classNames("__wab_instance", sty.newRepoButton, {
              [sty.newRepoButtonisPublishingSite]: hasVariant(
                $state,
                "isPublishingSite",
                "isPublishingSite"
              ),
              [sty.newRepoButtonview_existingRepo]: hasVariant(
                $state,
                "view",
                "existingRepo"
              ),
            })}
            endIcon={
              <ChevronDownSvgIcon
                className={classNames(projectcss.all, sty.svg__o2NZu)}
                role={"img"}
              />
            }
            font={
              hasVariant($state, "view", "existingRepo") ? undefined : "bold"
            }
            startIcon={
              <ArrowRightSvgIcon
                className={classNames(projectcss.all, sty.svg__cQkfv)}
                role={"img"}
              />
            }
            type={
              hasVariant($state, "view", "existingRepo") ? ["clear"] : undefined
            }
          >
            {"New repo"}
          </Button>
          <Button
            data-plasmic-name={"existingRepoButton"}
            data-plasmic-override={overrides.existingRepoButton}
            className={classNames("__wab_instance", sty.existingRepoButton, {
              [sty.existingRepoButtonisPublishingSite]: hasVariant(
                $state,
                "isPublishingSite",
                "isPublishingSite"
              ),
              [sty.existingRepoButtonview_existingRepo]: hasVariant(
                $state,
                "view",
                "existingRepo"
              ),
            })}
            endIcon={
              <ChevronDownSvgIcon
                className={classNames(projectcss.all, sty.svg__mtq1H)}
                role={"img"}
              />
            }
            font={
              hasVariant($state, "view", "existingRepo") ? "bold" : undefined
            }
            startIcon={
              <ArrowRightSvgIcon
                className={classNames(projectcss.all, sty.svg__vRUjJ)}
                role={"img"}
              />
            }
            type={hasVariant($state, "view", "existingRepo") ? [] : ["clear"]}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__xmTPi,
                {
                  [sty.textview_existingRepo__xmTPieHbf9]: hasVariant(
                    $state,
                    "view",
                    "existingRepo"
                  ),
                }
              )}
            >
              {"Existing repo"}
            </div>
          </Button>
        </div>
        {(hasVariant($state, "view", "existingRepo") ? false : true) ? (
          <Stack__
            as={"div"}
            data-plasmic-name={"newBox"}
            data-plasmic-override={overrides.newBox}
            hasGap={true}
            className={classNames(projectcss.all, sty.newBox, {
              [sty.newBoxerrors_name]: hasVariant($state, "errors", "name"),
              [sty.newBoxhide_action]: hasVariant($state, "hide", "action"),
              [sty.newBoxloading_githubData]: hasVariant(
                $state,
                "loading",
                "githubData"
              ),
              [sty.newBoxview_existingRepo]: hasVariant(
                $state,
                "view",
                "existingRepo"
              ),
            })}
          >
            <Stack__
              as={"div"}
              data-plasmic-name={"orgBox"}
              data-plasmic-override={overrides.orgBox}
              hasGap={true}
              className={classNames(projectcss.all, sty.orgBox, {
                [sty.orgBoxloading_githubData]: hasVariant(
                  $state,
                  "loading",
                  "githubData"
                ),
              })}
            >
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__ymKi2,
                  {
                    [sty.texterrors_name__ymKi2NelV0]: hasVariant(
                      $state,
                      "errors",
                      "name"
                    ),
                    [sty.textloading_githubData__ymKi20KfO]: hasVariant(
                      $state,
                      "loading",
                      "githubData"
                    ),
                    [sty.textview_existingRepo__ymKi2EHbf9]: hasVariant(
                      $state,
                      "view",
                      "existingRepo"
                    ),
                  }
                )}
              >
                {"Organization"}
              </div>
              <Select
                data-plasmic-name={"org"}
                data-plasmic-override={overrides.org}
                className={classNames("__wab_instance", sty.org)}
                icon={
                  <PlusSvgIcon
                    className={classNames(projectcss.all, sty.svg__f2Dnc)}
                    role={"img"}
                  />
                }
                onChange={async (...eventArgs: any) => {
                  ((...eventArgs) => {
                    generateStateOnChangeProp($state, ["org", "value"])(
                      eventArgs[0]
                    );
                  }).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                placeholder={"Select a GitHub organization..."}
                type={"bordered"}
                value={generateStateValueProp($state, ["org", "value"])}
              />
            </Stack__>
            <Stack__
              as={"div"}
              hasGap={true}
              className={classNames(projectcss.all, sty.freeBox__eK0Co, {
                [sty.freeBoxhide_action__eK0CoZcgUd]: hasVariant(
                  $state,
                  "hide",
                  "action"
                ),
                [sty.freeBoxview_existingRepo__eK0CoeHbf9]: hasVariant(
                  $state,
                  "view",
                  "existingRepo"
                ),
              })}
            >
              <div
                className={classNames(projectcss.all, sty.freeBox__bFvxf, {
                  [sty.freeBoxview_existingRepo__bFvxFeHbf9]: hasVariant(
                    $state,
                    "view",
                    "existingRepo"
                  ),
                })}
              />

              <Stack__
                as={"div"}
                hasGap={true}
                className={classNames(projectcss.all, sty.freeBox__m8Hum, {
                  [sty.freeBoxhide_action__m8HumZcgUd]: hasVariant(
                    $state,
                    "hide",
                    "action"
                  ),
                  [sty.freeBoxview_existingRepo__m8HumeHbf9]: hasVariant(
                    $state,
                    "view",
                    "existingRepo"
                  ),
                })}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__vlXbk,
                    {
                      [sty.textview_existingRepo__vlXbkeHbf9]: hasVariant(
                        $state,
                        "view",
                        "existingRepo"
                      ),
                    }
                  )}
                >
                  {"Missing user/org?"}
                </div>
                <Button
                  data-plasmic-name={"missingOrg"}
                  data-plasmic-override={overrides.missingOrg}
                  className={classNames("__wab_instance", sty.missingOrg, {
                    [sty.missingOrghide_action]: hasVariant(
                      $state,
                      "hide",
                      "action"
                    ),
                  })}
                  endIcon={
                    <ArrowUpRightSvgIcon
                      className={classNames(projectcss.all, sty.svg__qlN9J)}
                      role={"img"}
                    />
                  }
                  startIcon={
                    <ArrowRightSvgIcon
                      className={classNames(projectcss.all, sty.svg__nB3Rq)}
                      role={"img"}
                    />
                  }
                  type={["seamless"]}
                  withIcons={["endIcon"]}
                >
                  <div
                    className={classNames(
                      projectcss.all,
                      projectcss.__wab_text,
                      sty.text__n1Zsy,
                      {
                        [sty.textview_existingRepo__n1ZsyeHbf9]: hasVariant(
                          $state,
                          "view",
                          "existingRepo"
                        ),
                      }
                    )}
                  >
                    {"Adjust GitHub App permissions"}
                  </div>
                </Button>
              </Stack__>
            </Stack__>
            <Stack__
              as={"div"}
              data-plasmic-name={"nameBox"}
              data-plasmic-override={overrides.nameBox}
              hasGap={true}
              className={classNames(projectcss.all, sty.nameBox, {
                [sty.nameBoxerrors_name]: hasVariant($state, "errors", "name"),
                [sty.nameBoxview_existingRepo]: hasVariant(
                  $state,
                  "view",
                  "existingRepo"
                ),
              })}
            >
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__fuuks,
                  {
                    [sty.texterrors_name__fuukSnelV0]: hasVariant(
                      $state,
                      "errors",
                      "name"
                    ),
                  }
                )}
              >
                {"Name"}
              </div>
              <input
                data-plasmic-name={"name"}
                data-plasmic-override={overrides.name}
                className={classNames(
                  projectcss.all,
                  projectcss.input,
                  sty.name,
                  {
                    [sty.nameerrors_name]: hasVariant($state, "errors", "name"),
                    [sty.nameloading_githubData]: hasVariant(
                      $state,
                      "loading",
                      "githubData"
                    ),
                  }
                )}
                placeholder={"Enter name…"}
                ref={(ref) => {
                  $refs["name"] = ref;
                }}
                size={1}
                type={"text"}
                value={""}
              />
            </Stack__>
            {(hasVariant($state, "errors", "name") ? true : false) ? (
              <Stack__
                as={"div"}
                hasGap={true}
                className={classNames(projectcss.all, sty.freeBox__rQeIv, {
                  [sty.freeBoxerrors_name__rQeIVnelV0]: hasVariant(
                    $state,
                    "errors",
                    "name"
                  ),
                })}
              >
                {(hasVariant($state, "errors", "name") ? true : false) ? (
                  <div
                    className={classNames(projectcss.all, sty.freeBox__ydj1G, {
                      [sty.freeBoxerrors_name__ydj1GnelV0]: hasVariant(
                        $state,
                        "errors",
                        "name"
                      ),
                    })}
                  />
                ) : null}
                {(hasVariant($state, "errors", "name") ? true : false) ? (
                  <Stack__
                    as={"div"}
                    data-plasmic-name={"nameError"}
                    data-plasmic-override={overrides.nameError}
                    hasGap={true}
                    className={classNames(projectcss.all, sty.nameError, {
                      [sty.nameErrorerrors_name]: hasVariant(
                        $state,
                        "errors",
                        "name"
                      ),
                      [sty.nameErrorview_existingRepo]: hasVariant(
                        $state,
                        "view",
                        "existingRepo"
                      ),
                      [sty.nameErrorview_existingRepo_errors_name]:
                        hasVariant($state, "view", "existingRepo") &&
                        hasVariant($state, "errors", "name"),
                    })}
                  >
                    <div
                      className={classNames(
                        projectcss.all,
                        projectcss.__wab_text,
                        sty.text__stAqz,
                        {
                          [sty.texterrors_name__stAqznelV0]: hasVariant(
                            $state,
                            "errors",
                            "name"
                          ),
                        }
                      )}
                    >
                      {
                        "There is already a repo with this name on this GitHub organization. Change to something that is not in use."
                      }
                    </div>
                  </Stack__>
                ) : null}
              </Stack__>
            ) : null}
            <Stack__
              as={"div"}
              data-plasmic-name={"privateBox"}
              data-plasmic-override={overrides.privateBox}
              hasGap={true}
              className={classNames(projectcss.all, sty.privateBox, {
                [sty.privateBoxisPublishingSite]: hasVariant(
                  $state,
                  "isPublishingSite",
                  "isPublishingSite"
                ),
                [sty.privateBoxview_existingRepo]: hasVariant(
                  $state,
                  "view",
                  "existingRepo"
                ),
              })}
            >
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__ozQaX,
                  {
                    [sty.textisPublishingSite__ozQaXqOy2M]: hasVariant(
                      $state,
                      "isPublishingSite",
                      "isPublishingSite"
                    ),
                  }
                )}
              >
                {"Private?"}
              </div>
              <div
                className={classNames(projectcss.all, sty.freeBox__um2Rp, {
                  [sty.freeBoxisPublishingSite__um2RpQOy2M]: hasVariant(
                    $state,
                    "isPublishingSite",
                    "isPublishingSite"
                  ),
                })}
              >
                <Switch
                  data-plasmic-name={"privateRepo"}
                  data-plasmic-override={overrides.privateRepo}
                  children={null}
                  className={classNames("__wab_instance", sty.privateRepo)}
                  isChecked={
                    generateStateValueProp($state, [
                      "privateRepo",
                      "isChecked",
                    ]) ?? false
                  }
                  onChange={async (...eventArgs: any) => {
                    ((...eventArgs) => {
                      generateStateOnChangeProp($state, [
                        "privateRepo",
                        "isChecked",
                      ])(eventArgs[0]);
                    }).apply(null, eventArgs);

                    if (
                      eventArgs.length > 1 &&
                      eventArgs[1] &&
                      eventArgs[1]._plasmic_state_init_
                    ) {
                      return;
                    }
                  }}
                />
              </div>
            </Stack__>
            {(hasVariant($state, "errors", "name") ? false : false) ? (
              <div
                className={classNames(projectcss.all, sty.freeBox__nkvAb, {
                  [sty.freeBoxerrors_name__nkvAbnelV0]: hasVariant(
                    $state,
                    "errors",
                    "name"
                  ),
                })}
              />
            ) : null}
          </Stack__>
        ) : null}
        {(hasVariant($state, "view", "existingRepo") ? true : false) ? (
          <Stack__
            as={"div"}
            data-plasmic-name={"existingBox"}
            data-plasmic-override={overrides.existingBox}
            hasGap={true}
            className={classNames(projectcss.all, sty.existingBox, {
              [sty.existingBoxview_existingRepo]: hasVariant(
                $state,
                "view",
                "existingRepo"
              ),
            })}
          >
            <Stack__
              as={"div"}
              data-plasmic-name={"repositoryBox"}
              data-plasmic-override={overrides.repositoryBox}
              hasGap={true}
              className={classNames(projectcss.all, sty.repositoryBox, {
                [sty.repositoryBoxview_existingRepo]: hasVariant(
                  $state,
                  "view",
                  "existingRepo"
                ),
              })}
            >
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__ftyWh,
                  {
                    [sty.textview_existingRepo__ftyWheHbf9]: hasVariant(
                      $state,
                      "view",
                      "existingRepo"
                    ),
                  }
                )}
              >
                {"Repository"}
              </div>
              <Select
                data-plasmic-name={"repository"}
                data-plasmic-override={overrides.repository}
                className={classNames("__wab_instance", sty.repository)}
                icon={
                  <PlusSvgIcon
                    className={classNames(projectcss.all, sty.svg__gJtww)}
                    role={"img"}
                  />
                }
                onChange={async (...eventArgs: any) => {
                  ((...eventArgs) => {
                    generateStateOnChangeProp($state, ["repository", "value"])(
                      eventArgs[0]
                    );
                  }).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                value={generateStateValueProp($state, ["repository", "value"])}
              />
            </Stack__>
            <Stack__
              as={"div"}
              hasGap={true}
              className={classNames(projectcss.all, sty.freeBox__u1Ph5, {
                [sty.freeBoxview_existingRepo__u1Ph5EHbf9]: hasVariant(
                  $state,
                  "view",
                  "existingRepo"
                ),
              })}
            >
              {(hasVariant($state, "view", "existingRepo") ? true : false) ? (
                <div
                  className={classNames(projectcss.all, sty.freeBox__mdZzO, {
                    [sty.freeBoxview_existingRepo__mdZzOeHbf9]: hasVariant(
                      $state,
                      "view",
                      "existingRepo"
                    ),
                  })}
                />
              ) : null}
              <Stack__
                as={"div"}
                hasGap={true}
                className={classNames(projectcss.all, sty.freeBox___49QEf, {
                  [sty.freeBoxview_existingRepo___49QEfeHbf9]: hasVariant(
                    $state,
                    "view",
                    "existingRepo"
                  ),
                })}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__ov096,
                    {
                      [sty.textview_existingRepo__ov096EHbf9]: hasVariant(
                        $state,
                        "view",
                        "existingRepo"
                      ),
                    }
                  )}
                >
                  {"Missing repository?"}
                </div>
                <Button
                  data-plasmic-name={"missingRepo"}
                  data-plasmic-override={overrides.missingRepo}
                  className={classNames("__wab_instance", sty.missingRepo)}
                  endIcon={
                    <OpenIcon
                      className={classNames(projectcss.all, sty.svg__s35A)}
                      role={"img"}
                    />
                  }
                  startIcon={
                    <ArrowRightSvgIcon
                      className={classNames(projectcss.all, sty.svg__muAjE)}
                      role={"img"}
                    />
                  }
                  type={["seamless"]}
                  withIcons={["endIcon"]}
                >
                  <div
                    className={classNames(
                      projectcss.all,
                      projectcss.__wab_text,
                      sty.text__ywqDb,
                      {
                        [sty.textview_existingRepo__ywqDbeHbf9]: hasVariant(
                          $state,
                          "view",
                          "existingRepo"
                        ),
                      }
                    )}
                  >
                    {"Adjust GitHub App permissions"}
                  </div>
                </Button>
              </Stack__>
            </Stack__>
            <Stack__
              as={"div"}
              data-plasmic-name={"branchBox"}
              data-plasmic-override={overrides.branchBox}
              hasGap={true}
              className={classNames(projectcss.all, sty.branchBox, {
                [sty.branchBoxview_existingRepo]: hasVariant(
                  $state,
                  "view",
                  "existingRepo"
                ),
              })}
            >
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__voExp,
                  {
                    [sty.textview_existingRepo__voExPeHbf9]: hasVariant(
                      $state,
                      "view",
                      "existingRepo"
                    ),
                  }
                )}
              >
                {"Branch"}
              </div>
              <Select
                data-plasmic-name={"branch"}
                data-plasmic-override={overrides.branch}
                className={classNames("__wab_instance", sty.branch)}
                icon={
                  <PlusSvgIcon
                    className={classNames(projectcss.all, sty.svg__z1BTl)}
                    role={"img"}
                  />
                }
                onChange={async (...eventArgs: any) => {
                  ((...eventArgs) => {
                    generateStateOnChangeProp($state, ["branch", "value"])(
                      eventArgs[0]
                    );
                  }).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                value={generateStateValueProp($state, ["branch", "value"])}
              />
            </Stack__>
            <Stack__
              as={"div"}
              data-plasmic-name={"directoryBox"}
              data-plasmic-override={overrides.directoryBox}
              hasGap={true}
              className={classNames(projectcss.all, sty.directoryBox, {
                [sty.directoryBoxerrors_name]: hasVariant(
                  $state,
                  "errors",
                  "name"
                ),
                [sty.directoryBoxview_existingRepo]: hasVariant(
                  $state,
                  "view",
                  "existingRepo"
                ),
              })}
            >
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__mEoV,
                  {
                    [sty.textview_existingRepo__mEoVeHbf9]: hasVariant(
                      $state,
                      "view",
                      "existingRepo"
                    ),
                  }
                )}
              >
                {"Directory"}
              </div>
              <input
                data-plasmic-name={"directory"}
                data-plasmic-override={overrides.directory}
                className={classNames(
                  projectcss.all,
                  projectcss.input,
                  sty.directory,
                  {
                    [sty.directoryerrors_directory]: hasVariant(
                      $state,
                      "errors",
                      "directory"
                    ),
                    [sty.directoryview_existingRepo]: hasVariant(
                      $state,
                      "view",
                      "existingRepo"
                    ),
                  }
                )}
                placeholder={"Enter directory if not repository root..."}
                ref={(ref) => {
                  $refs["directory"] = ref;
                }}
                size={1}
                type={"text"}
                value={""}
              />
            </Stack__>
            {(hasVariant($state, "errors", "directory") ? true : false) ? (
              <Stack__
                as={"div"}
                data-plasmic-name={"directoryError"}
                data-plasmic-override={overrides.directoryError}
                hasGap={true}
                className={classNames(projectcss.all, sty.directoryError, {
                  [sty.directoryErrorerrors_directory]: hasVariant(
                    $state,
                    "errors",
                    "directory"
                  ),
                  [sty.directoryErrorerrors_name]: hasVariant(
                    $state,
                    "errors",
                    "name"
                  ),
                  [sty.directoryErrorview_existingRepo]: hasVariant(
                    $state,
                    "view",
                    "existingRepo"
                  ),
                  [sty.directoryErrorview_existingRepo_errors_name]:
                    hasVariant($state, "view", "existingRepo") &&
                    hasVariant($state, "errors", "name"),
                })}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__dA8Xr,
                    {
                      [sty.texterrors_directory__dA8XRtUzD]: hasVariant(
                        $state,
                        "errors",
                        "directory"
                      ),
                    }
                  )}
                >
                  {
                    "The specified directory does not contain a valid package.json."
                  }
                </div>
              </Stack__>
            ) : null}
          </Stack__>
        ) : null}
        <div
          className={classNames(projectcss.all, sty.freeBox__w6Skl, {
            [sty.freeBoxview_existingRepo__w6SkLeHbf9]: hasVariant(
              $state,
              "view",
              "existingRepo"
            ),
          })}
        />

        <Stack__
          as={"div"}
          data-plasmic-name={"opts"}
          data-plasmic-override={overrides.opts}
          hasGap={true}
          className={classNames(projectcss.all, sty.opts, {
            [sty.optshide_action]: hasVariant($state, "hide", "action"),
            [sty.optsview_existingRepo]: hasVariant(
              $state,
              "view",
              "existingRepo"
            ),
          })}
        >
          <Stack__
            as={"div"}
            data-plasmic-name={"frameworkBox"}
            data-plasmic-override={overrides.frameworkBox}
            hasGap={true}
            className={classNames(projectcss.all, sty.frameworkBox, {
              [sty.frameworkBoxerrors_directory]: hasVariant(
                $state,
                "errors",
                "directory"
              ),
              [sty.frameworkBoxview_existingRepo]: hasVariant(
                $state,
                "view",
                "existingRepo"
              ),
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__kPvKu,
                {
                  [sty.textloading_githubData__kPvKu0KfO]: hasVariant(
                    $state,
                    "loading",
                    "githubData"
                  ),
                  [sty.textview_existingRepo__kPvKueHbf9]: hasVariant(
                    $state,
                    "view",
                    "existingRepo"
                  ),
                }
              )}
            >
              {"Framework"}
            </div>
            <Select
              data-plasmic-name={"framework"}
              data-plasmic-override={overrides.framework}
              className={classNames("__wab_instance", sty.framework)}
              icon={
                <PlusSvgIcon
                  className={classNames(projectcss.all, sty.svg__hDmgh)}
                  role={"img"}
                />
              }
              onChange={async (...eventArgs: any) => {
                ((...eventArgs) => {
                  generateStateOnChangeProp($state, ["framework", "value"])(
                    eventArgs[0]
                  );
                }).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              type={"bordered"}
              value={generateStateValueProp($state, ["framework", "value"])}
            />
          </Stack__>
          <Stack__
            as={"div"}
            data-plasmic-name={"languageBox"}
            data-plasmic-override={overrides.languageBox}
            hasGap={true}
            className={classNames(projectcss.all, sty.languageBox, {
              [sty.languageBoxview_existingRepo]: hasVariant(
                $state,
                "view",
                "existingRepo"
              ),
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__qGPa,
                {
                  [sty.textview_existingRepo__qGPaeHbf9]: hasVariant(
                    $state,
                    "view",
                    "existingRepo"
                  ),
                }
              )}
            >
              {"Language"}
            </div>
            <Select
              data-plasmic-name={"language"}
              data-plasmic-override={overrides.language}
              className={classNames("__wab_instance", sty.language)}
              icon={
                <PlusSvgIcon
                  className={classNames(projectcss.all, sty.svg__xjZUh)}
                  role={"img"}
                />
              }
              onChange={async (...eventArgs: any) => {
                ((...eventArgs) => {
                  generateStateOnChangeProp($state, ["language", "value"])(
                    eventArgs[0]
                  );
                }).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              type={"bordered"}
              value={generateStateValueProp($state, ["language", "value"])}
            />
          </Stack__>
          <Stack__
            as={"div"}
            data-plasmic-name={"modeBox"}
            data-plasmic-override={overrides.modeBox}
            hasGap={true}
            className={classNames(projectcss.all, sty.modeBox, {
              [sty.modeBoxisPublishingSite]: hasVariant(
                $state,
                "isPublishingSite",
                "isPublishingSite"
              ),
              [sty.modeBoxview_existingRepo]: hasVariant(
                $state,
                "view",
                "existingRepo"
              ),
            })}
          >
            <Stack__
              as={"div"}
              hasGap={true}
              className={classNames(projectcss.all, sty.freeBox__gdLb, {
                [sty.freeBoxisPublishingSite__gdLbqOy2M]: hasVariant(
                  $state,
                  "isPublishingSite",
                  "isPublishingSite"
                ),
                [sty.freeBoxview_existingRepo__gdLBeHbf9]: hasVariant(
                  $state,
                  "view",
                  "existingRepo"
                ),
              })}
            >
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__bcbhS,
                  {
                    [sty.textview_existingRepo__bcbhSeHbf9]: hasVariant(
                      $state,
                      "view",
                      "existingRepo"
                    ),
                  }
                )}
              >
                {"Mode"}
              </div>
              <InfoIcon
                data-plasmic-name={"modeInfo"}
                data-plasmic-override={overrides.modeInfo}
                className={classNames(projectcss.all, sty.modeInfo)}
                role={"img"}
              />
            </Stack__>
            <Select
              data-plasmic-name={"mode"}
              data-plasmic-override={overrides.mode}
              className={classNames("__wab_instance", sty.mode)}
              icon={
                <PlusSvgIcon
                  className={classNames(projectcss.all, sty.svg__tSnk0)}
                  role={"img"}
                />
              }
              onChange={async (...eventArgs: any) => {
                ((...eventArgs) => {
                  generateStateOnChangeProp($state, ["mode", "value"])(
                    eventArgs[0]
                  );
                }).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              type={"bordered"}
              value={generateStateValueProp($state, ["mode", "value"])}
            />
          </Stack__>
          {(hasVariant($state, "hide", "action") ? false : true) ? (
            <Stack__
              as={"div"}
              data-plasmic-name={"actionBox"}
              data-plasmic-override={overrides.actionBox}
              hasGap={true}
              className={classNames(projectcss.all, sty.actionBox, {
                [sty.actionBoxhide_action]: hasVariant(
                  $state,
                  "hide",
                  "action"
                ),
                [sty.actionBoxisPublishingSite]: hasVariant(
                  $state,
                  "isPublishingSite",
                  "isPublishingSite"
                ),
                [sty.actionBoxview_existingRepo]: hasVariant(
                  $state,
                  "view",
                  "existingRepo"
                ),
              })}
            >
              <Stack__
                as={"div"}
                hasGap={true}
                className={classNames(projectcss.all, sty.freeBox__krCQj, {
                  [sty.freeBoxview_existingRepo__krCQjeHbf9]: hasVariant(
                    $state,
                    "view",
                    "existingRepo"
                  ),
                })}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__pOa9G,
                    {
                      [sty.textview_existingRepo__pOa9GeHbf9]: hasVariant(
                        $state,
                        "view",
                        "existingRepo"
                      ),
                    }
                  )}
                >
                  {"Default action"}
                </div>
                <InfoIcon
                  data-plasmic-name={"actionInfo"}
                  data-plasmic-override={overrides.actionInfo}
                  className={classNames(projectcss.all, sty.actionInfo)}
                  role={"img"}
                />
              </Stack__>
              <Select
                data-plasmic-name={"action"}
                data-plasmic-override={overrides.action}
                className={classNames("__wab_instance", sty.action)}
                icon={
                  <PlusSvgIcon
                    className={classNames(projectcss.all, sty.svg___9AFcX)}
                    role={"img"}
                  />
                }
                onChange={async (...eventArgs: any) => {
                  ((...eventArgs) => {
                    generateStateOnChangeProp($state, ["action", "value"])(
                      eventArgs[0]
                    );
                  }).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                type={"bordered"}
                value={generateStateValueProp($state, ["action", "value"])}
              />
            </Stack__>
          ) : null}
        </Stack__>
        {(
          hasVariant($state, "hideGithubPages", "hideGithubPages")
            ? false
            : hasVariant($state, "view", "existingRepo")
            ? false
            : true
        ) ? (
          <div
            className={classNames(projectcss.all, sty.freeBox__r6Rpm, {
              [sty.freeBoxhideGithubPages__r6RpmiRbLd]: hasVariant(
                $state,
                "hideGithubPages",
                "hideGithubPages"
              ),
              [sty.freeBoxview_existingRepo__r6RpMeHbf9]: hasVariant(
                $state,
                "view",
                "existingRepo"
              ),
            })}
          />
        ) : null}
        {(
          hasVariant($state, "hideGithubPages", "hideGithubPages")
            ? false
            : hasVariant($state, "view", "existingRepo")
            ? false
            : true
        ) ? (
          <Stack__
            as={"div"}
            data-plasmic-name={"deployment"}
            data-plasmic-override={overrides.deployment}
            hasGap={true}
            className={classNames(projectcss.all, sty.deployment, {
              [sty.deploymenterrors_hasPublishSiteError]: hasVariant(
                $state,
                "errors",
                "hasPublishSiteError"
              ),
              [sty.deploymenterrors_invalidDomainError_isPublishingSite_errors_hasDomainError]:
                hasVariant($state, "isPublishingSite", "isPublishingSite") &&
                hasVariant($state, "errors", "hasDomainError") &&
                hasVariant($state, "errors", "invalidDomainError"),
              [sty.deploymenterrors_publishSiteWarning_isPublishingSite]:
                hasVariant($state, "errors", "publishSiteWarning") &&
                hasVariant($state, "isPublishingSite", "isPublishingSite"),
              [sty.deploymenthideGithubPages]: hasVariant(
                $state,
                "hideGithubPages",
                "hideGithubPages"
              ),
              [sty.deploymenthide_action]: hasVariant($state, "hide", "action"),
              [sty.deploymentisPublishingSite]: hasVariant(
                $state,
                "isPublishingSite",
                "isPublishingSite"
              ),
              [sty.deploymentisPublishingSite_errors_hasPublishSiteError]:
                hasVariant($state, "errors", "hasPublishSiteError") &&
                hasVariant($state, "isPublishingSite", "isPublishingSite"),
              [sty.deploymentloading_githubData]: hasVariant(
                $state,
                "loading",
                "githubData"
              ),
              [sty.deploymentview_existingRepo]: hasVariant(
                $state,
                "view",
                "existingRepo"
              ),
            })}
          >
            <Stack__
              as={"div"}
              hasGap={true}
              className={classNames(projectcss.all, sty.freeBox__xKatJ, {
                [sty.freeBoxisPublishingSite__xKatJqOy2M]: hasVariant(
                  $state,
                  "isPublishingSite",
                  "isPublishingSite"
                ),
                [sty.freeBoxview_existingRepo__xKatJeHbf9]: hasVariant(
                  $state,
                  "view",
                  "existingRepo"
                ),
              })}
            >
              <Stack__
                as={"div"}
                hasGap={true}
                className={classNames(projectcss.all, sty.freeBox__uBi71, {
                  [sty.freeBoxerrors_directory__uBi71TUzD]: hasVariant(
                    $state,
                    "errors",
                    "directory"
                  ),
                  [sty.freeBoxerrors_hasPublishSiteError__uBi71Zx2KI]:
                    hasVariant($state, "errors", "hasPublishSiteError"),
                  [sty.freeBoxisPublishingSite__uBi71QOy2M]: hasVariant(
                    $state,
                    "isPublishingSite",
                    "isPublishingSite"
                  ),
                  [sty.freeBoxview_existingRepo__uBi71EHbf9]: hasVariant(
                    $state,
                    "view",
                    "existingRepo"
                  ),
                })}
              >
                <div
                  data-plasmic-name={"publishSiteLabel"}
                  data-plasmic-override={overrides.publishSiteLabel}
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.publishSiteLabel,
                    {
                      [sty.publishSiteLabelerrors_directory]: hasVariant(
                        $state,
                        "errors",
                        "directory"
                      ),
                      [sty.publishSiteLabelerrors_hasDomainError]: hasVariant(
                        $state,
                        "errors",
                        "hasDomainError"
                      ),
                      [sty.publishSiteLabelerrors_hasPublishSiteError]:
                        hasVariant($state, "errors", "hasPublishSiteError"),
                      [sty.publishSiteLabelerrors_invalidDomainError]:
                        hasVariant($state, "errors", "invalidDomainError"),
                      [sty.publishSiteLabelerrors_missingDomain]: hasVariant(
                        $state,
                        "errors",
                        "missingDomain"
                      ),
                      [sty.publishSiteLabelerrors_name]: hasVariant(
                        $state,
                        "errors",
                        "name"
                      ),
                      [sty.publishSiteLabelerrors_publishSiteWarning]:
                        hasVariant($state, "errors", "publishSiteWarning"),
                      [sty.publishSiteLabelhideGithubPages]: hasVariant(
                        $state,
                        "hideGithubPages",
                        "hideGithubPages"
                      ),
                      [sty.publishSiteLabelisPublishingSite]: hasVariant(
                        $state,
                        "isPublishingSite",
                        "isPublishingSite"
                      ),
                      [sty.publishSiteLabelloading_branches]: hasVariant(
                        $state,
                        "loading",
                        "branches"
                      ),
                      [sty.publishSiteLabelloading_detectedOptions]: hasVariant(
                        $state,
                        "loading",
                        "detectedOptions"
                      ),
                      [sty.publishSiteLabelloading_githubData]: hasVariant(
                        $state,
                        "loading",
                        "githubData"
                      ),
                      [sty.publishSiteLabelloading_saving]: hasVariant(
                        $state,
                        "loading",
                        "saving"
                      ),
                    }
                  )}
                >
                  {"Publish site?"}
                </div>
                <Stack__
                  as={"div"}
                  hasGap={true}
                  className={classNames(projectcss.all, sty.freeBox__kvwzT, {
                    [sty.freeBoxerrors_hasPublishSiteError__kvwzTZx2KI]:
                      hasVariant($state, "errors", "hasPublishSiteError"),
                    [sty.freeBoxerrors_publishSiteWarning_isPublishingSite__kvwzT2OGsOQOy2M]:
                      hasVariant($state, "errors", "publishSiteWarning") &&
                      hasVariant(
                        $state,
                        "isPublishingSite",
                        "isPublishingSite"
                      ),
                    [sty.freeBoxhide_action__kvwzTZcgUd]: hasVariant(
                      $state,
                      "hide",
                      "action"
                    ),
                    [sty.freeBoxisPublishingSite__kvwzTqOy2M]: hasVariant(
                      $state,
                      "isPublishingSite",
                      "isPublishingSite"
                    ),
                    [sty.freeBoxisPublishingSite_errors_missingDomain__kvwzTqOy2MFkFc]:
                      hasVariant(
                        $state,
                        "isPublishingSite",
                        "isPublishingSite"
                      ) && hasVariant($state, "errors", "missingDomain"),
                  })}
                >
                  <Switch
                    data-plasmic-name={"publishSite"}
                    data-plasmic-override={overrides.publishSite}
                    children={null}
                    className={classNames("__wab_instance", sty.publishSite, {
                      [sty.publishSiteerrors_hasPublishSiteError]: hasVariant(
                        $state,
                        "errors",
                        "hasPublishSiteError"
                      ),
                      [sty.publishSiteisPublishingSite]: hasVariant(
                        $state,
                        "isPublishingSite",
                        "isPublishingSite"
                      ),
                    })}
                    isChecked={
                      generateStateValueProp($state, [
                        "publishSite",
                        "isChecked",
                      ]) ?? false
                    }
                    onChange={async (...eventArgs: any) => {
                      ((...eventArgs) => {
                        generateStateOnChangeProp($state, [
                          "publishSite",
                          "isChecked",
                        ])(eventArgs[0]);
                      }).apply(null, eventArgs);

                      if (
                        eventArgs.length > 1 &&
                        eventArgs[1] &&
                        eventArgs[1]._plasmic_state_init_
                      ) {
                        return;
                      }
                    }}
                  />

                  {(
                    hasVariant($state, "errors", "hasPublishSiteError")
                      ? true
                      : false
                  ) ? (
                    <ErrorFeedback
                      data-plasmic-name={"publishSiteError"}
                      data-plasmic-override={overrides.publishSiteError}
                      className={classNames(
                        "__wab_instance",
                        sty.publishSiteError,
                        {
                          [sty.publishSiteErrorerrors_hasPublishSiteError]:
                            hasVariant($state, "errors", "hasPublishSiteError"),
                          [sty.publishSiteErrorisPublishingSite_errors_hasPublishSiteError]:
                            hasVariant(
                              $state,
                              "errors",
                              "hasPublishSiteError"
                            ) &&
                            hasVariant(
                              $state,
                              "isPublishingSite",
                              "isPublishingSite"
                            ),
                        }
                      )}
                    />
                  ) : null}
                </Stack__>
              </Stack__>
            </Stack__>
            {(
              hasVariant($state, "errors", "publishSiteWarning") &&
              hasVariant($state, "isPublishingSite", "isPublishingSite")
                ? true
                : hasVariant($state, "errors", "hasDomainError")
                ? true
                : false
            ) ? (
              <ErrorFeedback
                data-plasmic-name={"domainError2"}
                data-plasmic-override={overrides.domainError2}
                className={classNames("__wab_instance", sty.domainError2, {
                  [sty.domainError2errors_hasDomainError]: hasVariant(
                    $state,
                    "errors",
                    "hasDomainError"
                  ),
                  [sty.domainError2errors_invalidDomainError_isPublishingSite_errors_hasDomainError]:
                    hasVariant(
                      $state,
                      "isPublishingSite",
                      "isPublishingSite"
                    ) &&
                    hasVariant($state, "errors", "hasDomainError") &&
                    hasVariant($state, "errors", "invalidDomainError"),
                  [sty.domainError2errors_publishSiteWarning_isPublishingSite]:
                    hasVariant($state, "errors", "publishSiteWarning") &&
                    hasVariant($state, "isPublishingSite", "isPublishingSite"),
                })}
                warning={
                  hasVariant($state, "errors", "publishSiteWarning") &&
                  hasVariant($state, "isPublishingSite", "isPublishingSite")
                    ? true
                    : undefined
                }
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__bSfWw,
                    {
                      [sty.texterrors_hasDomainError__bSfWwdbgyX]: hasVariant(
                        $state,
                        "errors",
                        "hasDomainError"
                      ),
                      [sty.texterrors_invalidDomainError_isPublishingSite_errors_hasDomainError__bSfWwAyHpEQOy2MDbgyX]:
                        hasVariant(
                          $state,
                          "isPublishingSite",
                          "isPublishingSite"
                        ) &&
                        hasVariant($state, "errors", "hasDomainError") &&
                        hasVariant($state, "errors", "invalidDomainError"),
                      [sty.texterrors_publishSiteWarning_isPublishingSite__bSfWw2OGsOQOy2M]:
                        hasVariant($state, "errors", "publishSiteWarning") &&
                        hasVariant(
                          $state,
                          "isPublishingSite",
                          "isPublishingSite"
                        ),
                    }
                  )}
                >
                  {hasVariant($state, "isPublishingSite", "isPublishingSite") &&
                  hasVariant($state, "errors", "hasDomainError") &&
                  hasVariant($state, "errors", "invalidDomainError") ? (
                    "Invalid domain. Please use 1-63 alphanumeric characters, with dashes between words."
                  ) : hasVariant($state, "errors", "publishSiteWarning") &&
                    hasVariant(
                      $state,
                      "isPublishingSite",
                      "isPublishingSite"
                    ) ? (
                    <React.Fragment>
                      <span
                        className={"plasmic_default__all plasmic_default__span"}
                        style={{ fontWeight: 700 }}
                      >
                        {
                          "Site publishing does not work with plain React. Choose Next.js instead. "
                        }
                      </span>
                      <React.Fragment>
                        {
                          "Page routes are auto-mounted only for Next.js/Gatsby projects. Proceed with plain React only if you know you want to manually add code to explicitly render pages using a framework like react-router."
                        }
                      </React.Fragment>
                    </React.Fragment>
                  ) : (
                    "This subdomain is currently taken."
                  )}
                </div>
              </ErrorFeedback>
            ) : null}
            {(
              hasVariant($state, "isPublishingSite", "isPublishingSite")
                ? true
                : false
            ) ? (
              <Stack__
                as={"div"}
                hasGap={true}
                className={classNames(projectcss.all, sty.freeBox__lofOa, {
                  [sty.freeBoxerrors_publishSiteWarning_isPublishingSite__lofOa2OGsOQOy2M]:
                    hasVariant($state, "errors", "publishSiteWarning") &&
                    hasVariant($state, "isPublishingSite", "isPublishingSite"),
                  [sty.freeBoxisPublishingSite__lofOaQOy2M]: hasVariant(
                    $state,
                    "isPublishingSite",
                    "isPublishingSite"
                  ),
                })}
              >
                <Stack__
                  as={"div"}
                  hasGap={true}
                  className={classNames(projectcss.all, sty.freeBox__uXh0V, {
                    [sty.freeBoxerrors_publishSiteWarning_isPublishingSite__uXh0V2OGsOQOy2M]:
                      hasVariant($state, "errors", "publishSiteWarning") &&
                      hasVariant(
                        $state,
                        "isPublishingSite",
                        "isPublishingSite"
                      ),
                    [sty.freeBoxisPublishingSite__uXh0VQOy2M]: hasVariant(
                      $state,
                      "isPublishingSite",
                      "isPublishingSite"
                    ),
                  })}
                >
                  <Stack__
                    as={"div"}
                    hasGap={true}
                    className={classNames(projectcss.all, sty.freeBox___8QGx, {
                      [sty.freeBoxerrors_directory___8QGxTUzD]: hasVariant(
                        $state,
                        "errors",
                        "directory"
                      ),
                      [sty.freeBoxerrors_hasDomainError___8QGxDbgyX]:
                        hasVariant($state, "errors", "hasDomainError"),
                      [sty.freeBoxerrors_publishSiteWarning_isPublishingSite___8QGx2OGsOQOy2M]:
                        hasVariant($state, "errors", "publishSiteWarning") &&
                        hasVariant(
                          $state,
                          "isPublishingSite",
                          "isPublishingSite"
                        ),
                      [sty.freeBoxisPublishingSite___8QGxQOy2M]: hasVariant(
                        $state,
                        "isPublishingSite",
                        "isPublishingSite"
                      ),
                      [sty.freeBoxview_existingRepo___8QGxEHbf9]: hasVariant(
                        $state,
                        "view",
                        "existingRepo"
                      ),
                    })}
                  >
                    <div
                      className={classNames(
                        projectcss.all,
                        projectcss.__wab_text,
                        sty.text__u2Xdt,
                        {
                          [sty.texterrors_publishSiteWarning_isPublishingSite__u2Xdt2OGsOQOy2M]:
                            hasVariant(
                              $state,
                              "errors",
                              "publishSiteWarning"
                            ) &&
                            hasVariant(
                              $state,
                              "isPublishingSite",
                              "isPublishingSite"
                            ),
                          [sty.textisPublishingSite__u2XdtQOy2M]: hasVariant(
                            $state,
                            "isPublishingSite",
                            "isPublishingSite"
                          ),
                        }
                      )}
                    >
                      {"Domain"}
                    </div>
                    <div
                      data-plasmic-name={"apparentSubdomainInput"}
                      data-plasmic-override={overrides.apparentSubdomainInput}
                      className={classNames(
                        projectcss.all,
                        sty.apparentSubdomainInput,
                        {
                          [sty.apparentSubdomainInputerrors_missingDomain]:
                            hasVariant($state, "errors", "missingDomain"),
                          [sty.apparentSubdomainInputisPublishingSite]:
                            hasVariant(
                              $state,
                              "isPublishingSite",
                              "isPublishingSite"
                            ),
                        }
                      )}
                    >
                      <input
                        data-plasmic-name={"subdomainInput"}
                        data-plasmic-override={overrides.subdomainInput}
                        className={classNames(
                          projectcss.all,
                          projectcss.input,
                          sty.subdomainInput,
                          {
                            [sty.subdomainInputerrors_missingDomain]:
                              hasVariant($state, "errors", "missingDomain"),
                            [sty.subdomainInputerrors_name]: hasVariant(
                              $state,
                              "errors",
                              "name"
                            ),
                            [sty.subdomainInputerrors_publishSiteWarning_isPublishingSite]:
                              hasVariant(
                                $state,
                                "errors",
                                "publishSiteWarning"
                              ) &&
                              hasVariant(
                                $state,
                                "isPublishingSite",
                                "isPublishingSite"
                              ),
                            [sty.subdomainInputisPublishingSite]: hasVariant(
                              $state,
                              "isPublishingSite",
                              "isPublishingSite"
                            ),
                            [sty.subdomainInputisPublishingSite_errors_missingDomain]:
                              hasVariant(
                                $state,
                                "isPublishingSite",
                                "isPublishingSite"
                              ) &&
                              hasVariant($state, "errors", "missingDomain"),
                          }
                        )}
                        placeholder={"Enter name..."}
                        ref={(ref) => {
                          $refs["subdomainInput"] = ref;
                        }}
                        size={1}
                        type={"text"}
                        value={""}
                      />

                      <div
                        className={classNames(
                          projectcss.all,
                          projectcss.__wab_text,
                          sty.text__hUstb,
                          {
                            [sty.textisPublishingSite__hUstbqOy2M]: hasVariant(
                              $state,
                              "isPublishingSite",
                              "isPublishingSite"
                            ),
                          }
                        )}
                      >
                        {".plasmic.site"}
                      </div>
                    </div>
                  </Stack__>
                  {(
                    hasVariant($state, "errors", "hasDomainError")
                      ? true
                      : false
                  ) ? (
                    <ErrorFeedback
                      data-plasmic-name={"domainError"}
                      data-plasmic-override={overrides.domainError}
                      className={classNames("__wab_instance", sty.domainError, {
                        [sty.domainErrorerrors_hasDomainError]: hasVariant(
                          $state,
                          "errors",
                          "hasDomainError"
                        ),
                        [sty.domainErrorerrors_invalidDomainError_isPublishingSite_errors_hasDomainError]:
                          hasVariant(
                            $state,
                            "isPublishingSite",
                            "isPublishingSite"
                          ) &&
                          hasVariant($state, "errors", "hasDomainError") &&
                          hasVariant($state, "errors", "invalidDomainError"),
                        [sty.domainErrorisPublishingSite]: hasVariant(
                          $state,
                          "isPublishingSite",
                          "isPublishingSite"
                        ),
                      })}
                    >
                      {hasVariant(
                        $state,
                        "isPublishingSite",
                        "isPublishingSite"
                      ) &&
                      hasVariant($state, "errors", "hasDomainError") &&
                      hasVariant($state, "errors", "invalidDomainError")
                        ? "Invalid domain. Please use 1-63 alphanumeric characters, with dashes between words."
                        : "This subdomain is currently taken."}
                    </ErrorFeedback>
                  ) : null}
                </Stack__>
                <div
                  className={classNames(projectcss.all, sty.freeBox__yhpUs, {
                    [sty.freeBoxerrors_hasDomainError__yhpUSdbgyX]: hasVariant(
                      $state,
                      "errors",
                      "hasDomainError"
                    ),
                    [sty.freeBoxerrors_missingDomain__yhpUSfkFc]: hasVariant(
                      $state,
                      "errors",
                      "missingDomain"
                    ),
                    [sty.freeBoxerrors_publishSiteWarning_isPublishingSite__yhpUs2OGsOQOy2M]:
                      hasVariant($state, "errors", "publishSiteWarning") &&
                      hasVariant(
                        $state,
                        "isPublishingSite",
                        "isPublishingSite"
                      ),
                    [sty.freeBoxisPublishingSite__yhpUsqOy2M]: hasVariant(
                      $state,
                      "isPublishingSite",
                      "isPublishingSite"
                    ),
                    [sty.freeBoxisPublishingSite_errors_missingDomain__yhpUsqOy2MFkFc]:
                      hasVariant(
                        $state,
                        "isPublishingSite",
                        "isPublishingSite"
                      ) && hasVariant($state, "errors", "missingDomain"),
                  })}
                >
                  <div
                    className={classNames(
                      projectcss.all,
                      projectcss.__wab_text,
                      sty.text__u5VXk,
                      {
                        [sty.texterrors_invalidDomainError_isPublishingSite_errors_hasDomainError__u5VXkAyHpEQOy2MDbgyX]:
                          hasVariant(
                            $state,
                            "isPublishingSite",
                            "isPublishingSite"
                          ) &&
                          hasVariant($state, "errors", "hasDomainError") &&
                          hasVariant($state, "errors", "invalidDomainError"),
                        [sty.texterrors_publishSiteWarning_isPublishingSite__u5VXk2OGsOQOy2M]:
                          hasVariant($state, "errors", "publishSiteWarning") &&
                          hasVariant(
                            $state,
                            "isPublishingSite",
                            "isPublishingSite"
                          ),
                        [sty.textisPublishingSite__u5VXkQOy2M]: hasVariant(
                          $state,
                          "isPublishingSite",
                          "isPublishingSite"
                        ),
                      }
                    )}
                  >
                    {"Hosted by GitHub Pages. "}
                  </div>
                  <PlasmicLink__
                    data-plasmic-name={"moreProvidersLink"}
                    data-plasmic-override={overrides.moreProvidersLink}
                    className={classNames(
                      projectcss.all,
                      projectcss.a,
                      projectcss.__wab_text,
                      sty.moreProvidersLink,
                      {
                        [sty.moreProvidersLinkerrors_publishSiteWarning_isPublishingSite]:
                          hasVariant($state, "errors", "publishSiteWarning") &&
                          hasVariant(
                            $state,
                            "isPublishingSite",
                            "isPublishingSite"
                          ),
                        [sty.moreProvidersLinkisPublishingSite]: hasVariant(
                          $state,
                          "isPublishingSite",
                          "isPublishingSite"
                        ),
                      }
                    )}
                    href={"https://www.plasmic.app/learn/publishing"}
                    platform={"react"}
                  >
                    {"See more providers"}
                  </PlasmicLink__>
                  <div
                    className={classNames(
                      projectcss.all,
                      projectcss.__wab_text,
                      sty.text__tt3Dz
                    )}
                  >
                    {"."}
                  </div>
                </div>
              </Stack__>
            ) : null}
          </Stack__>
        ) : null}
      </Stack__>
      <div
        className={classNames(projectcss.all, sty.freeBox__esjpg, {
          [sty.freeBoxerrors_hasDomainError__esjpgdbgyX]: hasVariant(
            $state,
            "errors",
            "hasDomainError"
          ),
          [sty.freeBoxview_existingRepo__esjpgeHbf9]: hasVariant(
            $state,
            "view",
            "existingRepo"
          ),
        })}
      >
        <Button
          data-plasmic-name={"pushButton"}
          data-plasmic-override={overrides.pushButton}
          className={classNames("__wab_instance", sty.pushButton, {
            [sty.pushButtonloading_githubData]: hasVariant(
              $state,
              "loading",
              "githubData"
            ),
            [sty.pushButtonloading_saving]: hasVariant(
              $state,
              "loading",
              "saving"
            ),
            [sty.pushButtonview_existingRepo]: hasVariant(
              $state,
              "view",
              "existingRepo"
            ),
          })}
          disabled={hasVariant($state, "loading", "saving") ? true : undefined}
          endIcon={
            <ChevronDownSvgIcon
              className={classNames(projectcss.all, sty.svg__r05Gv)}
              role={"img"}
            />
          }
          size={"wide"}
          startIcon={
            <ArrowRightSvgIcon
              className={classNames(projectcss.all, sty.svg__tmeWd)}
              role={"img"}
            />
          }
          type={["primary"]}
        >
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__cpLb,
              {
                [sty.textloading_saving__cpLbdfB02]: hasVariant(
                  $state,
                  "loading",
                  "saving"
                ),
              }
            )}
          >
            {hasVariant($state, "loading", "saving")
              ? "Setting up..."
              : "Save repository information"}
          </div>
        </Button>
      </div>
    </Stack__>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "newRepoButton",
    "existingRepoButton",
    "newBox",
    "orgBox",
    "org",
    "missingOrg",
    "nameBox",
    "name",
    "nameError",
    "privateBox",
    "privateRepo",
    "existingBox",
    "repositoryBox",
    "repository",
    "missingRepo",
    "branchBox",
    "branch",
    "directoryBox",
    "directory",
    "directoryError",
    "opts",
    "frameworkBox",
    "framework",
    "languageBox",
    "language",
    "modeBox",
    "modeInfo",
    "mode",
    "actionBox",
    "actionInfo",
    "action",
    "deployment",
    "publishSiteLabel",
    "publishSite",
    "publishSiteError",
    "domainError2",
    "apparentSubdomainInput",
    "subdomainInput",
    "domainError",
    "moreProvidersLink",
    "pushButton",
  ],
  newRepoButton: ["newRepoButton"],
  existingRepoButton: ["existingRepoButton"],
  newBox: [
    "newBox",
    "orgBox",
    "org",
    "missingOrg",
    "nameBox",
    "name",
    "nameError",
    "privateBox",
    "privateRepo",
  ],
  orgBox: ["orgBox", "org"],
  org: ["org"],
  missingOrg: ["missingOrg"],
  nameBox: ["nameBox", "name"],
  name: ["name"],
  nameError: ["nameError"],
  privateBox: ["privateBox", "privateRepo"],
  privateRepo: ["privateRepo"],
  existingBox: [
    "existingBox",
    "repositoryBox",
    "repository",
    "missingRepo",
    "branchBox",
    "branch",
    "directoryBox",
    "directory",
    "directoryError",
  ],
  repositoryBox: ["repositoryBox", "repository"],
  repository: ["repository"],
  missingRepo: ["missingRepo"],
  branchBox: ["branchBox", "branch"],
  branch: ["branch"],
  directoryBox: ["directoryBox", "directory"],
  directory: ["directory"],
  directoryError: ["directoryError"],
  opts: [
    "opts",
    "frameworkBox",
    "framework",
    "languageBox",
    "language",
    "modeBox",
    "modeInfo",
    "mode",
    "actionBox",
    "actionInfo",
    "action",
  ],
  frameworkBox: ["frameworkBox", "framework"],
  framework: ["framework"],
  languageBox: ["languageBox", "language"],
  language: ["language"],
  modeBox: ["modeBox", "modeInfo", "mode"],
  modeInfo: ["modeInfo"],
  mode: ["mode"],
  actionBox: ["actionBox", "actionInfo", "action"],
  actionInfo: ["actionInfo"],
  action: ["action"],
  deployment: [
    "deployment",
    "publishSiteLabel",
    "publishSite",
    "publishSiteError",
    "domainError2",
    "apparentSubdomainInput",
    "subdomainInput",
    "domainError",
    "moreProvidersLink",
  ],
  publishSiteLabel: ["publishSiteLabel"],
  publishSite: ["publishSite"],
  publishSiteError: ["publishSiteError"],
  domainError2: ["domainError2"],
  apparentSubdomainInput: ["apparentSubdomainInput", "subdomainInput"],
  subdomainInput: ["subdomainInput"],
  domainError: ["domainError"],
  moreProvidersLink: ["moreProvidersLink"],
  pushButton: ["pushButton"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  newRepoButton: typeof Button;
  existingRepoButton: typeof Button;
  newBox: "div";
  orgBox: "div";
  org: typeof Select;
  missingOrg: typeof Button;
  nameBox: "div";
  name: "input";
  nameError: "div";
  privateBox: "div";
  privateRepo: typeof Switch;
  existingBox: "div";
  repositoryBox: "div";
  repository: typeof Select;
  missingRepo: typeof Button;
  branchBox: "div";
  branch: typeof Select;
  directoryBox: "div";
  directory: "input";
  directoryError: "div";
  opts: "div";
  frameworkBox: "div";
  framework: typeof Select;
  languageBox: "div";
  language: typeof Select;
  modeBox: "div";
  modeInfo: "svg";
  mode: typeof Select;
  actionBox: "div";
  actionInfo: "svg";
  action: typeof Select;
  deployment: "div";
  publishSiteLabel: "div";
  publishSite: typeof Switch;
  publishSiteError: typeof ErrorFeedback;
  domainError2: typeof ErrorFeedback;
  apparentSubdomainInput: "div";
  subdomainInput: "input";
  domainError: typeof ErrorFeedback;
  moreProvidersLink: "a";
  pushButton: typeof Button;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicGithubIntegration__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicGithubIntegration__VariantsArgs;
    args?: PlasmicGithubIntegration__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicGithubIntegration__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicGithubIntegration__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicGithubIntegration__ArgProps,
          internalVariantPropNames: PlasmicGithubIntegration__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicGithubIntegration__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicGithubIntegration";
  } else {
    func.displayName = `PlasmicGithubIntegration.${nodeName}`;
  }
  return func;
}

export const PlasmicGithubIntegration = Object.assign(
  // Top-level PlasmicGithubIntegration renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    newRepoButton: makeNodeComponent("newRepoButton"),
    existingRepoButton: makeNodeComponent("existingRepoButton"),
    newBox: makeNodeComponent("newBox"),
    orgBox: makeNodeComponent("orgBox"),
    org: makeNodeComponent("org"),
    missingOrg: makeNodeComponent("missingOrg"),
    nameBox: makeNodeComponent("nameBox"),
    _name: makeNodeComponent("name"),
    nameError: makeNodeComponent("nameError"),
    privateBox: makeNodeComponent("privateBox"),
    privateRepo: makeNodeComponent("privateRepo"),
    existingBox: makeNodeComponent("existingBox"),
    repositoryBox: makeNodeComponent("repositoryBox"),
    repository: makeNodeComponent("repository"),
    missingRepo: makeNodeComponent("missingRepo"),
    branchBox: makeNodeComponent("branchBox"),
    branch: makeNodeComponent("branch"),
    directoryBox: makeNodeComponent("directoryBox"),
    directory: makeNodeComponent("directory"),
    directoryError: makeNodeComponent("directoryError"),
    opts: makeNodeComponent("opts"),
    frameworkBox: makeNodeComponent("frameworkBox"),
    framework: makeNodeComponent("framework"),
    languageBox: makeNodeComponent("languageBox"),
    language: makeNodeComponent("language"),
    modeBox: makeNodeComponent("modeBox"),
    modeInfo: makeNodeComponent("modeInfo"),
    mode: makeNodeComponent("mode"),
    actionBox: makeNodeComponent("actionBox"),
    actionInfo: makeNodeComponent("actionInfo"),
    action: makeNodeComponent("action"),
    deployment: makeNodeComponent("deployment"),
    publishSiteLabel: makeNodeComponent("publishSiteLabel"),
    publishSite: makeNodeComponent("publishSite"),
    publishSiteError: makeNodeComponent("publishSiteError"),
    domainError2: makeNodeComponent("domainError2"),
    apparentSubdomainInput: makeNodeComponent("apparentSubdomainInput"),
    subdomainInput: makeNodeComponent("subdomainInput"),
    domainError: makeNodeComponent("domainError"),
    moreProvidersLink: makeNodeComponent("moreProvidersLink"),
    pushButton: makeNodeComponent("pushButton"),

    // Metadata about props expected for PlasmicGithubIntegration
    internalVariantProps: PlasmicGithubIntegration__VariantProps,
    internalArgProps: PlasmicGithubIntegration__ArgProps,
  }
);

export default PlasmicGithubIntegration;
/* prettier-ignore-end */
