// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import { SidebarSection } from "@/wab/client/components/sidebar/SidebarSection";
import { LabeledStyleDimItemRow } from "@/wab/client/components/sidebar/sidebar-helpers";
import {
  RshExpsProvider,
  StyleComponent,
  providesStyleComponent,
} from "@/wab/client/components/style-controls/StyleComponent";
import {
  DefaultThemeLayoutPanelProps,
  PlasmicThemeLayoutPanel,
} from "@/wab/client/plasmic/plasmic_kit_left_pane/PlasmicThemeLayoutPanel";
import { useStudioCtx } from "@/wab/client/studio-ctx/StudioCtx";
import { getLengthUnits } from "@/wab/shared/css";
import { RuleSetHelpers } from "@/wab/shared/RuleSetHelpers";
import {
  CONTENT_LAYOUT_DEFAULTS,
  CONTENT_LAYOUT_STANDARD_WIDTH_PROP,
  CONTENT_LAYOUT_VIEWPORT_GAP_PROP,
  CONTENT_LAYOUT_WIDE_WIDTH_PROP,
} from "@/wab/shared/core/style-props";
import { makeExpProxy } from "@/wab/shared/exprs";
import { RuleSet, ThemeLayoutSettings } from "@/wab/shared/model/classes";
import { HTMLElementRefOf } from "@plasmicapp/react-web";
import { observer } from "mobx-react";
import * as React from "react";

export type ThemeLayoutPanelProps = DefaultThemeLayoutPanelProps;

function ThemeLayoutPanel_(
  props: ThemeLayoutPanelProps,
  ref: HTMLElementRefOf<"div">
) {
  const studioCtx = useStudioCtx();
  const theme = studioCtx.site.activeTheme;
  const layout =
    theme?.layout ??
    new ThemeLayoutSettings({ rs: new RuleSet({ values: {}, mixins: [] }) });
  const sc = React.useMemo(() => {
    const baseExpr = new RuleSetHelpers(layout.rs, "div");
    const ensureLayout = () => {
      if (theme && !theme.layout) {
        theme.layout = layout;
      }
    };
    return new StyleComponent({
      expsProvider: new RshExpsProvider(
        makeExpProxy(baseExpr, {
          get: (p: string) => {
            return baseExpr.getRaw(p) ?? CONTENT_LAYOUT_DEFAULTS[p];
          },
          set: (p: string, val: string) => {
            baseExpr.set(p, val);
            ensureLayout();
          },
        }),
        studioCtx,
        []
      ),
    });
  }, [theme, layout, studioCtx]);
  return providesStyleComponent(sc)(
    <PlasmicThemeLayoutPanel
      root={{ ref }}
      {...props}
      children={
        <>
          <SidebarSection title={"Document layout"} className="fill-width">
            <LabeledStyleDimItemRow
              styleName={CONTENT_LAYOUT_STANDARD_WIDTH_PROP}
              label="Standard width"
              dimOpts={{
                allowedUnits: getLengthUnits("px"),
                min: 0,
              }}
              noExtract
            />
            <LabeledStyleDimItemRow
              styleName={CONTENT_LAYOUT_WIDE_WIDTH_PROP}
              label="Wide width"
              dimOpts={{
                allowedUnits: getLengthUnits("px"),
                min: 0,
              }}
              noExtract
            />
            <LabeledStyleDimItemRow
              styleName={CONTENT_LAYOUT_VIEWPORT_GAP_PROP}
              label="Viewport gap"
              dimOpts={{
                allowedUnits: getLengthUnits("px"),
                min: 0,
              }}
              noExtract
            />
          </SidebarSection>
        </>
      }
    />
  );
}

const ThemeLayoutPanel = observer(React.forwardRef(ThemeLayoutPanel_));
export default ThemeLayoutPanel;
