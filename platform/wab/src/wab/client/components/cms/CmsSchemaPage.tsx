// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import { useRRouteMatch, UU } from "@/wab/client/cli-routes";
import { useCmsDatabase } from "@/wab/client/components/cms/cms-contexts";
import {
  DefaultCmsSchemaPageProps,
  PlasmicCmsSchemaPage,
} from "@/wab/client/plasmic/plasmic_kit_cms/PlasmicCmsSchemaPage";
import { HTMLElementRefOf } from "@plasmicapp/react-web";
import * as React from "react";
import { Redirect, Route, Switch } from "react-router";
export type CmsSchemaPageProps = DefaultCmsSchemaPageProps;

function CmsSchemaPage_(
  props: CmsSchemaPageProps,
  ref: HTMLElementRefOf<"div">
) {
  const m = useRRouteMatch(UU.cmsSchemaRoot);
  const database = useCmsDatabase(m?.params.databaseId);
  if (!m || !database) {
    return null;
  }

  // Sort to make archived tables last on the list
  // Sorting by id to make it stable.
  const tables = database.tables.sort((a, b) =>
    a.isArchived == b.isArchived
      ? b.id.localeCompare(a.id)
      : a.isArchived
      ? 1
      : -1
  );
  return (
    <Switch>
      <Route
        path={UU.cmsModelSchema.pattern}
        render={({ match }) => {
          if (!tables.find((t) => t.id === match.params.tableId)) {
            return (
              <Redirect
                to={UU.cmsSchemaRoot.fill({
                  databaseId: match.params.databaseId,
                })}
              />
            );
          } else {
            return (
              <PlasmicCmsSchemaPage
                root={{ ref }}
                {...props}
                cmsModelDetails={{ key: match.params.tableId }}
              />
            );
          }
        }}
      />
      <Route
        path={UU.cmsSchemaRoot.pattern}
        render={({ match }) => {
          if (tables.length > 0) {
            return (
              <Redirect
                to={UU.cmsModelSchema.fill({
                  ...match.params,
                  tableId: tables[0].id,
                })}
              />
            );
          } else {
            return (
              <PlasmicCmsSchemaPage root={{ ref }} {...props} noModels={true} />
            );
          }
        }}
      />
    </Switch>
  );
}

const CmsSchemaPage = React.forwardRef(CmsSchemaPage_);
export default CmsSchemaPage;
