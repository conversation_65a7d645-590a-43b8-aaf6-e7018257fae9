/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: fpbcKyXdMTvY59T4C5fjcC
// Component: mSgnlB96I5A

import * as React from "react";

import {
  Flex as Flex__,
  SingleBooleanChoiceArg,
  Stack as Stack__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  generateStateOnChangeProp,
  generateStateValueProp,
  hasVariant,
  renderPlasmicSlot,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import Checkbox from "../../../widgets/Checkbox"; // plasmic-import: W-rO7NZqPjZ/component
import MenuButton from "../../../widgets/MenuButton"; // plasmic-import: h69wHrrKtL/component
import Select from "../../../widgets/Select"; // plasmic-import: j_4IQyOWK2b/component
import Switch from "../../../widgets/Switch"; // plasmic-import: b35JDgXpbiF/component
import WebhookHeader from "../../WebhookHeader"; // plasmic-import: OkB-fXuJPc/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_css from "../../../../plasmic/PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../../../../plasmic/plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import projectcss from "../../../modals/plasmic/plasmic_kit_project_settings/plasmic_plasmic_kit_project_settings.module.css"; // plasmic-import: fpbcKyXdMTvY59T4C5fjcC/projectcss
import sty from "./PlasmicWebhooksItem.module.css"; // plasmic-import: mSgnlB96I5A/css

import InfoIcon from "../../../../plasmic/plasmic_kit/PlasmicIcon__Info"; // plasmic-import: BjAly3N4fWuWe/icon
import PlusSvgIcon from "../../../../plasmic/plasmic_kit_icons/icons/PlasmicIcon__PlusSvg"; // plasmic-import: sQKgd2GNr/icon

createPlasmicElementProxy;

export type PlasmicWebhooksItem__VariantMembers = {
  expanded: "expanded";
};
export type PlasmicWebhooksItem__VariantsArgs = {
  expanded?: SingleBooleanChoiceArg<"expanded">;
};
type VariantPropType = keyof PlasmicWebhooksItem__VariantsArgs;
export const PlasmicWebhooksItem__VariantProps = new Array<VariantPropType>(
  "expanded"
);

export type PlasmicWebhooksItem__ArgsType = { headers?: React.ReactNode };
type ArgPropType = keyof PlasmicWebhooksItem__ArgsType;
export const PlasmicWebhooksItem__ArgProps = new Array<ArgPropType>("headers");

export type PlasmicWebhooksItem__OverridesType = {
  root?: Flex__<"div">;
  checkbox?: Flex__<typeof Checkbox>;
  method?: Flex__<typeof Select>;
  svg?: Flex__<"svg">;
  url?: Flex__<"input">;
  menuButton?: Flex__<typeof MenuButton>;
  payload?: Flex__<"textarea">;
  sendDataInfo?: Flex__<"svg">;
  sendPlasmicDataSwitch?: Flex__<typeof Switch>;
};

export interface DefaultWebhooksItemProps {
  headers?: React.ReactNode;
  expanded?: SingleBooleanChoiceArg<"expanded">;
  className?: string;
}

const $$ = {};

function PlasmicWebhooksItem__RenderFunc(props: {
  variants: PlasmicWebhooksItem__VariantsArgs;
  args: PlasmicWebhooksItem__ArgsType;
  overrides: PlasmicWebhooksItem__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "expanded",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.expanded,
      },
      {
        path: "checkbox.isChecked",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
      {
        path: "method.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
      {
        path: "sendPlasmicDataSwitch.isChecked",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <Stack__
      as={"div"}
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      hasGap={true}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        { [sty.rootexpanded]: hasVariant($state, "expanded", "expanded") }
      )}
    >
      <Stack__
        as={"div"}
        hasGap={true}
        className={classNames(projectcss.all, sty.freeBox__simNc)}
      >
        <Stack__
          as={"div"}
          hasGap={true}
          className={classNames(projectcss.all, sty.freeBox___0Jy6H)}
        >
          <Stack__
            as={"div"}
            hasGap={true}
            className={classNames(projectcss.all, sty.freeBox__ptuDx)}
          >
            <Checkbox
              data-plasmic-name={"checkbox"}
              data-plasmic-override={overrides.checkbox}
              children={null}
              className={classNames("__wab_instance", sty.checkbox)}
              isChecked={
                generateStateValueProp($state, ["checkbox", "isChecked"]) ??
                false
              }
              onChange={async (...eventArgs: any) => {
                ((...eventArgs) => {
                  generateStateOnChangeProp($state, ["checkbox", "isChecked"])(
                    eventArgs[0]
                  );
                }).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />

            <Select
              data-plasmic-name={"method"}
              data-plasmic-override={overrides.method}
              className={classNames("__wab_instance", sty.method)}
              icon={
                <PlusSvgIcon
                  data-plasmic-name={"svg"}
                  data-plasmic-override={overrides.svg}
                  className={classNames(projectcss.all, sty.svg)}
                  role={"img"}
                />
              }
              onChange={async (...eventArgs: any) => {
                ((...eventArgs) => {
                  generateStateOnChangeProp($state, ["method", "value"])(
                    eventArgs[0]
                  );
                }).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              value={generateStateValueProp($state, ["method", "value"])}
            />
          </Stack__>
          <input
            data-plasmic-name={"url"}
            data-plasmic-override={overrides.url}
            className={classNames(projectcss.all, projectcss.input, sty.url)}
            placeholder={"URL…"}
            ref={(ref) => {
              $refs["url"] = ref;
            }}
            size={1}
            type={"text"}
            value={""}
          />

          <MenuButton
            data-plasmic-name={"menuButton"}
            data-plasmic-override={overrides.menuButton}
            className={classNames("__wab_instance", sty.menuButton)}
          />
        </Stack__>
      </Stack__>
      {(hasVariant($state, "expanded", "expanded") ? true : false) ? (
        <Stack__
          as={"div"}
          hasGap={true}
          className={classNames(projectcss.all, sty.freeBox__bbNTg, {
            [sty.freeBoxexpanded__bbNTgaCjOc]: hasVariant(
              $state,
              "expanded",
              "expanded"
            ),
          })}
        >
          <Stack__
            as={"div"}
            hasGap={true}
            className={classNames(projectcss.all, sty.freeBox__wL5I)}
          >
            {renderPlasmicSlot({
              defaultContents: (
                <React.Fragment>
                  <WebhookHeader showAdd={true} />

                  <WebhookHeader />
                </React.Fragment>
              ),
              value: args.headers,
            })}
          </Stack__>
          <Stack__
            as={"div"}
            hasGap={true}
            className={classNames(projectcss.all, sty.freeBox__wihbF, {
              [sty.freeBoxexpanded__wihbFaCjOc]: hasVariant(
                $state,
                "expanded",
                "expanded"
              ),
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text___0EkOd,
                {
                  [sty.textexpanded___0EkOdaCjOc]: hasVariant(
                    $state,
                    "expanded",
                    "expanded"
                  ),
                }
              )}
            >
              {"Payload"}
            </div>
            <textarea
              data-plasmic-name={"payload"}
              data-plasmic-override={overrides.payload}
              className={classNames(
                projectcss.all,
                projectcss.textarea,
                sty.payload,
                {
                  [sty.payloadexpanded]: hasVariant(
                    $state,
                    "expanded",
                    "expanded"
                  ),
                }
              )}
              placeholder={"Enter payload…"}
              ref={(ref) => {
                $refs["payload"] = ref;
              }}
              rows={5}
              value={""}
            />
          </Stack__>
          <Stack__
            as={"div"}
            hasGap={true}
            className={classNames(projectcss.all, sty.freeBox__paNzq, {
              [sty.freeBoxexpanded__paNzQaCjOc]: hasVariant(
                $state,
                "expanded",
                "expanded"
              ),
            })}
          >
            <Stack__
              as={"div"}
              hasGap={true}
              className={classNames(projectcss.all, sty.freeBox___5X7VS)}
            >
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text___1YZlP,
                  {
                    [sty.textexpanded___1YZlPaCjOc]: hasVariant(
                      $state,
                      "expanded",
                      "expanded"
                    ),
                  }
                )}
              >
                {"Send Data?"}
              </div>
              <InfoIcon
                data-plasmic-name={"sendDataInfo"}
                data-plasmic-override={overrides.sendDataInfo}
                className={classNames(projectcss.all, sty.sendDataInfo)}
                role={"img"}
              />
            </Stack__>
            <Switch
              data-plasmic-name={"sendPlasmicDataSwitch"}
              data-plasmic-override={overrides.sendPlasmicDataSwitch}
              children={null}
              className={classNames(
                "__wab_instance",
                sty.sendPlasmicDataSwitch
              )}
              isChecked={
                generateStateValueProp($state, [
                  "sendPlasmicDataSwitch",
                  "isChecked",
                ]) ?? false
              }
              onChange={async (...eventArgs: any) => {
                ((...eventArgs) => {
                  generateStateOnChangeProp($state, [
                    "sendPlasmicDataSwitch",
                    "isChecked",
                  ])(eventArgs[0]);
                }).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          </Stack__>
        </Stack__>
      ) : null}
      <div className={classNames(projectcss.all, sty.freeBox__uuv93)} />
    </Stack__>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "checkbox",
    "method",
    "svg",
    "url",
    "menuButton",
    "payload",
    "sendDataInfo",
    "sendPlasmicDataSwitch",
  ],
  checkbox: ["checkbox"],
  method: ["method", "svg"],
  svg: ["svg"],
  url: ["url"],
  menuButton: ["menuButton"],
  payload: ["payload"],
  sendDataInfo: ["sendDataInfo"],
  sendPlasmicDataSwitch: ["sendPlasmicDataSwitch"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  checkbox: typeof Checkbox;
  method: typeof Select;
  svg: "svg";
  url: "input";
  menuButton: typeof MenuButton;
  payload: "textarea";
  sendDataInfo: "svg";
  sendPlasmicDataSwitch: typeof Switch;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicWebhooksItem__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicWebhooksItem__VariantsArgs;
    args?: PlasmicWebhooksItem__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicWebhooksItem__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicWebhooksItem__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicWebhooksItem__ArgProps,
          internalVariantPropNames: PlasmicWebhooksItem__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicWebhooksItem__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicWebhooksItem";
  } else {
    func.displayName = `PlasmicWebhooksItem.${nodeName}`;
  }
  return func;
}

export const PlasmicWebhooksItem = Object.assign(
  // Top-level PlasmicWebhooksItem renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    checkbox: makeNodeComponent("checkbox"),
    method: makeNodeComponent("method"),
    svg: makeNodeComponent("svg"),
    url: makeNodeComponent("url"),
    menuButton: makeNodeComponent("menuButton"),
    payload: makeNodeComponent("payload"),
    sendDataInfo: makeNodeComponent("sendDataInfo"),
    sendPlasmicDataSwitch: makeNodeComponent("sendPlasmicDataSwitch"),

    // Metadata about props expected for PlasmicWebhooksItem
    internalVariantProps: PlasmicWebhooksItem__VariantProps,
    internalArgProps: PlasmicWebhooksItem__ArgProps,
  }
);

export default PlasmicWebhooksItem;
/* prettier-ignore-end */
