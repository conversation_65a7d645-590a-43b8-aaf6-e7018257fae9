// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import {
  DefaultSearchInputProps,
  PlasmicSearchInput,
} from "@/wab/client/plasmic/project_panel/PlasmicSearchInput";
import * as React from "react";
import { mergeProps } from "react-aria";

// Your component props start with props for variants and slots you defined
// in Plasmic, but you can add more here, like event handlers that you can
// attach to named nodes in your component.
//
// If you don't want to expose certain variants or slots as a prop, you can use
// Omit to hide them:
//
// interface SearchInputProps extends Omit<DefaultSearchInputProps, "hideProps1"|"hideProp2"> {
//   // etc.
// }
//
// You can also stop extending from DefaultSearchInputProps altogether and have
// total control over the props for your component.
interface SearchInputProps extends DefaultSearchInputProps {
  searchInput?: React.InputHTMLAttributes<HTMLInputElement>;
  clearFieldIcon?: { "data-test-id": string };
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onClear?: () => void;
}

function SearchInput(props: SearchInputProps) {
  const { onClear, onChange, ...rest } = props;
  const [value, setValue] = React.useState("");
  // Use PlasmicSearchInput to render this component as it was
  // designed in Plasmic, by activating the appropriate variants,
  // attaching the appropriate event handlers, etc.  You
  // can also install whatever React hooks you need here to manage state or
  // fetch data.
  //
  // Props you can pass into PlasmicSearchInput are:
  // 1. Variants you want to activate,
  // 2. Contents for slots you want to fill,
  // 3. Overrides for any named node in the component to attach behavior and data,
  // 4. Props to set on the root node.
  //
  // By default, we are just piping all SearchInputProps here, but feel free
  // to do whatever works for you.
  return (
    <PlasmicSearchInput
      {...rest}
      searchInput={mergeProps(props.searchInput, {
        value,
        onChange: (e) => {
          setValue(e.target.value);
          onChange?.(e);
        },
      })}
      clearFieldIcon={mergeProps(props.clearFieldIcon, {
        style: { display: value ? "block" : "none" },
        onClick: () => {
          setValue("");
          onClear?.();
        },
      })}
    />
  );
}

export default SearchInput;
