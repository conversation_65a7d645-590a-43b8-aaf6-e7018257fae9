// @ts-nocheck
/* eslint-disable */
/* tslint:disable */
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: 4B48dRthR8uGgyaBYpWthR
// Component: iSztBTxncH

import * as React from "react";

import {
  Flex as Flex__,
  SingleBooleanChoiceArg,
  Stack as Stack__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
  renderPlasmicSlot,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import sty from "./PlasmicInsertPanelTabItem.module.css"; // plasmic-import: iSztBTxncH/css
import projectcss from "./plasmic_plasmic_kit_insert_panel.module.css"; // plasmic-import: 4B48dRthR8uGgyaBYpWthR/projectcss

createPlasmicElementProxy;

export type PlasmicInsertPanelTabItem__VariantMembers = {
  isSelected: "isSelected";
  hasIcon: "hasIcon";
};
export type PlasmicInsertPanelTabItem__VariantsArgs = {
  isSelected?: SingleBooleanChoiceArg<"isSelected">;
  hasIcon?: SingleBooleanChoiceArg<"hasIcon">;
};
type VariantPropType = keyof PlasmicInsertPanelTabItem__VariantsArgs;
export const PlasmicInsertPanelTabItem__VariantProps =
  new Array<VariantPropType>("isSelected", "hasIcon");

export type PlasmicInsertPanelTabItem__ArgsType = {
  children?: React.ReactNode;
  icon?: React.ReactNode;
};
type ArgPropType = keyof PlasmicInsertPanelTabItem__ArgsType;
export const PlasmicInsertPanelTabItem__ArgProps = new Array<ArgPropType>(
  "children",
  "icon"
);

export type PlasmicInsertPanelTabItem__OverridesType = {
  root?: Flex__<"div">;
};

export interface DefaultInsertPanelTabItemProps {
  children?: React.ReactNode;
  icon?: React.ReactNode;
  isSelected?: SingleBooleanChoiceArg<"isSelected">;
  hasIcon?: SingleBooleanChoiceArg<"hasIcon">;
  className?: string;
}

const $$ = {};

function PlasmicInsertPanelTabItem__RenderFunc(props: {
  variants: PlasmicInsertPanelTabItem__VariantsArgs;
  args: PlasmicInsertPanelTabItem__ArgsType;
  overrides: PlasmicInsertPanelTabItem__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(() => Object.assign({}, props.args), [props.args]);

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "isSelected",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.isSelected,
      },
      {
        path: "hasIcon",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.hasIcon,
      },
    ],

    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <Stack__
      as={"div"}
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      hasGap={true}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        {
          [sty.roothasIcon]: hasVariant($state, "hasIcon", "hasIcon"),
          [sty.rootisSelected]: hasVariant($state, "isSelected", "isSelected"),
        }
      )}
    >
      <div
        className={classNames(projectcss.all, sty.freeBox___7UjJc, {
          [sty.freeBoxhasIcon___7UjJcfhJht]: hasVariant(
            $state,
            "hasIcon",
            "hasIcon"
          ),
        })}
      >
        {renderPlasmicSlot({
          defaultContents: (
            <svg
              className={classNames(projectcss.all, sty.svg__ocCw)}
              role={"img"}
            />
          ),

          value: args.icon,
        })}
      </div>
      <div
        className={classNames(projectcss.all, sty.freeBox__on6ED, {
          [sty.freeBoxisSelected__on6EDsVhai]: hasVariant(
            $state,
            "isSelected",
            "isSelected"
          ),
        })}
      >
        {renderPlasmicSlot({
          defaultContents: "Templates",
          value: args.children,
          className: classNames(sty.slotTargetChildren, {
            [sty.slotTargetChildrenhasIcon]: hasVariant(
              $state,
              "hasIcon",
              "hasIcon"
            ),
            [sty.slotTargetChildrenisSelected]: hasVariant(
              $state,
              "isSelected",
              "isSelected"
            ),
          }),
        })}
      </div>
    </Stack__>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicInsertPanelTabItem__OverridesType,
  DescendantsType<T>
>;

type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicInsertPanelTabItem__VariantsArgs;
    args?: PlasmicInsertPanelTabItem__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicInsertPanelTabItem__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicInsertPanelTabItem__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicInsertPanelTabItem__ArgProps,
          internalVariantPropNames: PlasmicInsertPanelTabItem__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicInsertPanelTabItem__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicInsertPanelTabItem";
  } else {
    func.displayName = `PlasmicInsertPanelTabItem.${nodeName}`;
  }
  return func;
}

export const PlasmicInsertPanelTabItem = Object.assign(
  // Top-level PlasmicInsertPanelTabItem renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements

    // Metadata about props expected for PlasmicInsertPanelTabItem
    internalVariantProps: PlasmicInsertPanelTabItem__VariantProps,
    internalArgProps: PlasmicInsertPanelTabItem__ArgProps,
  }
);

export default PlasmicInsertPanelTabItem;
/* prettier-ignore-end */
