// @ts-nocheck
/* eslint-disable */
/* tslint:disable */
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: ehckhYnyDHgCBbV47m9bkf
// Component: Xx_WsdQKli-S

import * as React from "react";

import {
  Flex as Flex__,
  PlasmicLink as PlasmicLink__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateStateOnChangeProp,
  generateStateValueProp,
  hasVariant,
  renderPlasmicSlot,
  useCurrentUser,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import HoverableIcon from "../../components/pricing/HoverableIcon"; // plasmic-import: 1T4UNMYLSC7u/component
import HoverableText from "../../components/pricing/HoverableText"; // plasmic-import: aVJYhoS8iDMR/component
import PriceTier from "../../components/pricing/PriceTier"; // plasmic-import: P7E8qtNzKrbM/component
import PriceTierFeatureItem from "../../components/pricing/PriceTierFeatureItem"; // plasmic-import: Z40kBWC-Knbn/component
import Switch from "../../components/widgets/Switch"; // plasmic-import: b35JDgXpbiF/component

import { useScreenVariants as useScreenVariantspbV7Vw3AiD6M } from "../plasmic_kit_responsive_breakpoints/PlasmicGlobalVariant__Screen"; // plasmic-import: PbV7vw3AiD6M/globalVariant
import { useEnvironment } from "./PlasmicGlobalVariant__Environment"; // plasmic-import: hIjF9NLAUKG-/globalVariant

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "./plasmic_plasmic_kit_pricing.module.css"; // plasmic-import: ehckhYnyDHgCBbV47m9bkf/projectcss
import sty from "./PlasmicPriceTierPicker.module.css"; // plasmic-import: Xx_WsdQKli-S/css

createPlasmicElementProxy;

export type PlasmicPriceTierPicker__VariantMembers = {
  showOutdatedTier: "showOutdatedTier";
  billingFrequency: "month" | "year";
  showTitle: "showTitle";
  showGrandfatheredTier: "showGrandfatheredTier";
  showBillingFrequency: "showBillingFrequency";
  noScrolling: "noScrolling";
};
export type PlasmicPriceTierPicker__VariantsArgs = {
  showOutdatedTier?: SingleBooleanChoiceArg<"showOutdatedTier">;
  billingFrequency?: SingleChoiceArg<"month" | "year">;
  showTitle?: SingleBooleanChoiceArg<"showTitle">;
  showGrandfatheredTier?: SingleBooleanChoiceArg<"showGrandfatheredTier">;
  showBillingFrequency?: SingleBooleanChoiceArg<"showBillingFrequency">;
  noScrolling?: SingleBooleanChoiceArg<"noScrolling">;
};
type VariantPropType = keyof PlasmicPriceTierPicker__VariantsArgs;
export const PlasmicPriceTierPicker__VariantProps = new Array<VariantPropType>(
  "showOutdatedTier",
  "billingFrequency",
  "showTitle",
  "showGrandfatheredTier",
  "showBillingFrequency",
  "noScrolling"
);

export type PlasmicPriceTierPicker__ArgsType = {
  teamName?: React.ReactNode;
};
type ArgPropType = keyof PlasmicPriceTierPicker__ArgsType;
export const PlasmicPriceTierPicker__ArgProps = new Array<ArgPropType>(
  "teamName"
);

export type PlasmicPriceTierPicker__OverridesType = {
  root?: Flex__<"div">;
  link?: Flex__<"a">;
  billingFrequencyToggle?: Flex__<typeof Switch>;
  newGrandfatheredTier?: Flex__<typeof PriceTier>;
  newOutdatedTier?: Flex__<typeof PriceTier>;
  newFreeTier?: Flex__<typeof PriceTier>;
  newStarterTier?: Flex__<typeof PriceTier>;
  newProTier?: Flex__<typeof PriceTier>;
  newTeamTier?: Flex__<typeof PriceTier>;
  newEnterpriseTier?: Flex__<typeof PriceTier>;
};

export interface DefaultPriceTierPickerProps {
  teamName?: React.ReactNode;
  showOutdatedTier?: SingleBooleanChoiceArg<"showOutdatedTier">;
  billingFrequency?: SingleChoiceArg<"month" | "year">;
  showTitle?: SingleBooleanChoiceArg<"showTitle">;
  showGrandfatheredTier?: SingleBooleanChoiceArg<"showGrandfatheredTier">;
  showBillingFrequency?: SingleBooleanChoiceArg<"showBillingFrequency">;
  noScrolling?: SingleBooleanChoiceArg<"noScrolling">;
  className?: string;
}

const $$ = {};

function PlasmicPriceTierPicker__RenderFunc(props: {
  variants: PlasmicPriceTierPicker__VariantsArgs;
  args: PlasmicPriceTierPicker__ArgsType;
  overrides: PlasmicPriceTierPicker__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(() => Object.assign({}, props.args), [props.args]);

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const currentUser = useCurrentUser?.() || {};

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "showOutdatedTier",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.showOutdatedTier,
      },
      {
        path: "billingFrequency",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.billingFrequency,
      },
      {
        path: "showTitle",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.showTitle,
      },
      {
        path: "showGrandfatheredTier",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.showGrandfatheredTier,
      },
      {
        path: "showBillingFrequency",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.showBillingFrequency,
      },
      {
        path: "noScrolling",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.noScrolling,
      },
      {
        path: "billingFrequencyToggle.isChecked",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) => "isChecked",
      },
    ],

    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  const globalVariants = ensureGlobalVariants({
    environment: useEnvironment(),
    screen: useScreenVariantspbV7Vw3AiD6M(),
  });

  return (
    <Stack__
      as={"div"}
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      hasGap={true}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        {
          [projectcss.global_environment_website]: hasVariant(
            globalVariants,
            "environment",
            "website"
          ),
          [sty.rootglobal_environment_website]: hasVariant(
            globalVariants,
            "environment",
            "website"
          ),
          [sty.rootnoScrolling]: hasVariant(
            $state,
            "noScrolling",
            "noScrolling"
          ),
          [sty.rootshowGrandfatheredTier]: hasVariant(
            $state,
            "showGrandfatheredTier",
            "showGrandfatheredTier"
          ),
          [sty.rootshowOutdatedTier]: hasVariant(
            $state,
            "showOutdatedTier",
            "showOutdatedTier"
          ),
          [sty.rootshowTitle]: hasVariant($state, "showTitle", "showTitle"),
        }
      )}
    >
      <div
        className={classNames(projectcss.all, sty.freeBox__ru3Bl, {
          [sty.freeBoxshowGrandfatheredTier__ru3BlIBuS4]: hasVariant(
            $state,
            "showGrandfatheredTier",
            "showGrandfatheredTier"
          ),
          [sty.freeBoxshowTitle__ru3Bloxoiu]: hasVariant(
            $state,
            "showTitle",
            "showTitle"
          ),
        })}
      >
        <Stack__
          as={"div"}
          hasGap={true}
          className={classNames(projectcss.all, sty.freeBox___5QXt7, {
            [sty.freeBoxshowGrandfatheredTier___5QXt7IBuS4]: hasVariant(
              $state,
              "showGrandfatheredTier",
              "showGrandfatheredTier"
            ),
          })}
        >
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__u9Ppd
            )}
          >
            {"Upgrade "}
          </div>
          <div className={classNames(projectcss.all, sty.freeBox__pSqi)}>
            {renderPlasmicSlot({
              defaultContents: "TEAM NAME",
              value: args.teamName,
              className: classNames(sty.slotTargetTeamName),
            })}
          </div>
        </Stack__>
        <div
          className={classNames(projectcss.all, sty.freeBox___8Z6, {
            [sty.freeBoxshowGrandfatheredTier___8Z6IBuS4]: hasVariant(
              $state,
              "showGrandfatheredTier",
              "showGrandfatheredTier"
            ),
          })}
        >
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__gc5Ft
            )}
          >
            <React.Fragment>
              <React.Fragment>
                {"Select a new plan to upgrade. "}
              </React.Fragment>
              {
                <PlasmicLink__
                  data-plasmic-name={"link"}
                  data-plasmic-override={overrides.link}
                  className={classNames(
                    projectcss.all,
                    projectcss.a,
                    projectcss.__wab_text,
                    projectcss.plasmic_default__inline,
                    sty.link
                  )}
                  href={"https://www.plasmic.app/pricing/"}
                  platform={"react"}
                  target={"_blank"}
                >
                  {"Learn more."}
                </PlasmicLink__>
              }

              <React.Fragment>{""}</React.Fragment>
            </React.Fragment>
          </div>
        </div>
      </div>
      <div
        className={classNames(projectcss.all, sty.freeBox__zlCvn, {
          [sty.freeBoxshowBillingFrequency__zlCvnAgbEi]: hasVariant(
            $state,
            "showBillingFrequency",
            "showBillingFrequency"
          ),
        })}
      >
        <Switch
          data-plasmic-name={"billingFrequencyToggle"}
          data-plasmic-override={overrides.billingFrequencyToggle}
          className={classNames("__wab_instance", sty.billingFrequencyToggle)}
          isChecked={
            generateStateValueProp($state, [
              "billingFrequencyToggle",
              "isChecked",
            ]) ?? false
          }
          onChange={(...eventArgs) => {
            generateStateOnChangeProp($state, [
              "billingFrequencyToggle",
              "isChecked",
            ])(eventArgs[0]);
          }}
        >
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__b6Uf2
            )}
          >
            {"Annual Billing (Save 20%)"}
          </div>
        </Switch>
      </div>
      <PriceTier
        data-plasmic-name={"newGrandfatheredTier"}
        data-plasmic-override={overrides.newGrandfatheredTier}
        className={classNames("__wab_instance", sty.newGrandfatheredTier, {
          [sty.newGrandfatheredTiershowGrandfatheredTier]: hasVariant(
            $state,
            "showGrandfatheredTier",
            "showGrandfatheredTier"
          ),
        })}
        status={"current"}
        tier={"grandfathered"}
        valueProps={
          <PriceTierFeatureItem
            className={classNames(
              "__wab_instance",
              sty.priceTierFeatureItem__b1Itg
            )}
            tier={"starter"}
          >
            {"You are grandfathered in"}
          </PriceTierFeatureItem>
        }
      />

      <Stack__
        as={"div"}
        hasGap={true}
        className={classNames(projectcss.all, sty.freeBox__uhZsz, {
          [sty.freeBoxbillingFrequency_month__uhZsZb25Ec]: hasVariant(
            $state,
            "billingFrequency",
            "month"
          ),
          [sty.freeBoxbillingFrequency_year__uhZsZgSc2D]: hasVariant(
            $state,
            "billingFrequency",
            "year"
          ),
          [sty.freeBoxglobal_environment_website__uhZsZmVgSv]: hasVariant(
            globalVariants,
            "environment",
            "website"
          ),
          [sty.freeBoxnoScrolling__uhZszyq2Sg]: hasVariant(
            $state,
            "noScrolling",
            "noScrolling"
          ),
          [sty.freeBoxshowGrandfatheredTier__uhZsziBuS4]: hasVariant(
            $state,
            "showGrandfatheredTier",
            "showGrandfatheredTier"
          ),
          [sty.freeBoxshowOutdatedTier__uhZsZmJjp]: hasVariant(
            $state,
            "showOutdatedTier",
            "showOutdatedTier"
          ),
        })}
      >
        <PriceTier
          data-plasmic-name={"newOutdatedTier"}
          data-plasmic-override={overrides.newOutdatedTier}
          className={classNames("__wab_instance", sty.newOutdatedTier, {
            [sty.newOutdatedTiershowOutdatedTier]: hasVariant(
              $state,
              "showOutdatedTier",
              "showOutdatedTier"
            ),
          })}
          tier={"legacy"}
          valueProps={
            <React.Fragment>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__jjbWy
                )}
              >
                {"Includes:"}
              </div>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__opp0X
                )}
              />

              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__kEwPb
                )}
              />

              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__lHrrv
                )}
              />
            </React.Fragment>
          }
        />

        <PriceTier
          data-plasmic-name={"newFreeTier"}
          data-plasmic-override={overrides.newFreeTier}
          className={classNames("__wab_instance", sty.newFreeTier, {
            [sty.newFreeTierbillingFrequency_year]: hasVariant(
              $state,
              "billingFrequency",
              "year"
            ),
            [sty.newFreeTierglobal_environment_website]: hasVariant(
              globalVariants,
              "environment",
              "website"
            ),
          })}
          expandableBody={
            <React.Fragment>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text___7NhFa
                )}
              >
                {"Free includes (monthly):"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__qZrbE
                )}
              >
                {"Unlimited projects"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__nMt01
                )}
              >
                {"Unlimited wrokspaces"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___2M3Xe
                )}
              >
                {"Unlimited publishes & syncs"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__sIdY4
                )}
              >
                {"Unlimited cross-project imports"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__yZGi5
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__l2Try
                  )}
                >
                  {"Component library"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__tiAs6
                  )}
                  forceOverlay={false}
                  popover={
                    "Components are the building blocks for Plasmic projects. Access a growing library of production-ready components, from basic UI elements to major common app views, including table, list, grid, inbox, calendar, map, kanban and more. "
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__aqgX
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___8XziC
                  )}
                >
                  {"Integration library"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__xEp8D
                  )}
                  popover={
                    "Choose from a wide range of pre-built integrations to power your project."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__fGido
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__e4Jg5
                  )}
                >
                  {"Template library"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__pv5F
                  )}
                  popover={
                    "Browse a library of pre-built projects and sections that you can copy and customize in minutes.   "
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__ikC
                )}
              >
                {"Hosting"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__duIme
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__cob6F
                  )}
                >
                  {"Custom domains"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__amjoU
                  )}
                  popover={
                    "All Plasmic projects come with a default Plasmic subdomain, but you can also choose to use your own custom domain."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__nkn2H
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__nLScj
                  )}
                >
                  {"Plasmic hosting"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__nTPoJ
                  )}
                  popover={
                    "We provide reliable and secure hosting for your app or website."
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__dj5IP
                )}
              >
                {"Building"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__buEtf
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__oa3WG
                  )}
                >
                  {"Components"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___2Mmy5
                  )}
                  popover={
                    "Create your own reusable components within Plasmic. Design sections or atomic design system elements that can be used across multiple projects."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__krHbL
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__gjk9I
                  )}
                >
                  {"Interactions"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__p9NQv
                  )}
                  popover={
                    "Add free-form logic and behavior to your designs. Manage state with ease without worrying about low-level details."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__niXag
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__gwmOh
                  )}
                >
                  {"Style tokens"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__cc1Ua
                  )}
                  popover={
                    "Apply colors, fonts, spacing, and more. Design with speed and consistency."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__jkdzD
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___6J4UO
                  )}
                >
                  {"Image optimization"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__t2UOg
                  )}
                  popover={
                    "Built-in image optimization, which ensures images are served at optimal sizes and at the proper resolution for the device screen."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__mHnCh
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___3X0P
                  )}
                >
                  {"Figma import"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___7GT6R
                  )}
                  popover={
                    "If you have existing Figma designs, you can import them into Plasmic using our best-in-class Figma-to-web converter."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__jiT9H
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___1Zzc
                  )}
                >
                  {"Theming & branding"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__hwq1O
                  )}
                  popover={
                    "Easily control the global styles across your app or website. Display your own branding on your app or website. "
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text___1RAR
                )}
              >
                {"Customization"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__woLui
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__sEjW
                  )}
                >
                  {"Code components"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___7R9S7
                  )}
                  popover={
                    "Use custom React components\u2014from your own codebase or imported from npm libraries\u2014directly as building blocks in Plasmic Studio."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___43Ult
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__zXlnn
                  )}
                >
                  {"Integrate with codebase"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__cevhz
                  )}
                  popover={
                    "Build within your existing applications. Use your own components, data, and code, and compose new screens and components."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___6Kjr5
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___6Uuqs
                  )}
                >
                  {"Extensions"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__rSpqY
                  )}
                  popover={
                    "Customize the Plasmic visual builder to fit your applications and domain. Bring your own custom controls, components, data sources, auth, interactions, and other extensions to Plasmic."
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__hIjo
                )}
              >
                {"CMS"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__k1VI5
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__gg6Me
                  )}
                >
                  {"Headless CMS"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__gyozc
                  )}
                  popover={
                    "A headless API-driven structured content management system."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__lzl94
                )}
              >
                {"Unlimited CMS types"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__iP3T1
                )}
              >
                {"Unlimited CMS entries"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__eXvBn
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__eWym3
                  )}
                >
                  {"Image optimization"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___0HEas
                  )}
                  popover={
                    "Reduces the file size of your images, without sacrificing quality, to improve overall performance."
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__lZFm5
                )}
              >
                {"Collaboration"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__tWv3V
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__k2XXh
                  )}
                >
                  {"Multiplayer"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__c9Mqs
                  )}
                  popover={
                    "Real-time collaborative editing\u2014have multiple users editing the same project at the same time."
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__dNqyn
                )}
              >
                {"Growth & Optimization"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__bjNjU
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__meilQ
                  )}
                >
                  {"Analytics"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__vugwK
                  )}
                  popover={"Privacy-first analytics with page views."}
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text___7OLdd
                )}
              >
                {"Security & Controls"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__iQawI
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__ruEvu
                  )}
                >
                  {"Backup & export"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__wJwuj
                  )}
                  popover={
                    "API access to programmatically create full snapshot representations of any project."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__cMTtv
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___5MMsk
                  )}
                >
                  {"Code generation"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__mR5C6
                  )}
                  popover={
                    "Generate human-manageable source code. Eject at any time with your code, or continuously sync code for your components and interface with them via an API."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___3Gu8
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__mId4R
                  )}
                >
                  {"Transfer ownership"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__zuOvS
                  )}
                  popover={
                    "Fully transfer your Plasmic project to another Plasmic user."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___0EskV
                )}
              >
                {"14-day version history"}
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__qmDdm
                )}
              >
                {"Resources"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__fnKDl
                )}
              >
                {"Community forum support"}
              </PriceTierFeatureItem>
            </React.Fragment>
          }
          price={"$0"}
          subprice={
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__fu7Tc,
                {
                  [sty.textbillingFrequency_month__fu7Tcb25Ec]: hasVariant(
                    $state,
                    "billingFrequency",
                    "month"
                  ),
                  [sty.textbillingFrequency_year__fu7TcgSc2D]: hasVariant(
                    $state,
                    "billingFrequency",
                    "year"
                  ),
                }
              )}
            >
              {hasVariant($state, "billingFrequency", "year")
                ? "Free forever"
                : hasVariant($state, "billingFrequency", "month")
                ? "Free forever"
                : "Free forever"}
            </div>
          }
          tier={"free"}
          valueProps={
            <React.Fragment>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__pryqP,
                  {
                    [sty.textbillingFrequency_month__pryqPb25Ec]: hasVariant(
                      $state,
                      "billingFrequency",
                      "month"
                    ),
                    [sty.textglobal_environment_website__pryqPmVgSv]:
                      hasVariant(globalVariants, "environment", "website"),
                  }
                )}
              >
                {"Free plan includes:"}
              </div>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__nX2Fw
                )}
                tier={"free"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__iqpZ8,
                    {
                      [sty.textglobal_environment_website__iqpZ8MVgSv]:
                        hasVariant(globalVariants, "environment", "website"),
                      [sty.textshowGrandfatheredTier__iqpZ8IBuS4]: hasVariant(
                        $state,
                        "showGrandfatheredTier",
                        "showGrandfatheredTier"
                      ),
                    }
                  )}
                >
                  {"Unlimited apps + websites"}
                </div>
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___5ZkA
                )}
                tier={"free"}
              >
                <HoverableText
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableText__aOtMl,
                    {
                      [sty.hoverableTextglobal_environment_website__aOtMLmVgSv]:
                        hasVariant(globalVariants, "environment", "website"),
                    }
                  )}
                  popover={
                    "The maximum number of editors that can make changes to your projects"
                  }
                >
                  {"3 collaborators"}
                </HoverableText>
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__aZrZe
                )}
                tier={"free"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__j2Zwc
                  )}
                >
                  {"10k monthly page views"}
                </div>
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__yVk1M
                )}
                tier={"free"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__sUpik
                  )}
                >
                  {"Unlimited headless CMS"}
                </div>
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___315Jc
                )}
                tier={"free"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__urAx
                  )}
                >
                  {"Community forum support"}
                </div>
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__eslK
                )}
                tier={"free"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__h0QYr
                  )}
                >
                  {"Plasmic badge on every project"}
                </div>
              </PriceTierFeatureItem>
            </React.Fragment>
          }
        />

        <PriceTier
          data-plasmic-name={"newStarterTier"}
          data-plasmic-override={overrides.newStarterTier}
          className={classNames("__wab_instance", sty.newStarterTier, {
            [sty.newStarterTierglobal_environment_website]: hasVariant(
              globalVariants,
              "environment",
              "website"
            ),
            [sty.newStarterTiernoScrolling]: hasVariant(
              $state,
              "noScrolling",
              "noScrolling"
            ),
          })}
          expandableBody={
            <React.Fragment>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text___94C8F
                )}
              >
                {hasVariant(globalVariants, "screen", "mobileOnly")
                  ? "Starter includes (monthly):"
                  : "Free includes (monthly):"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__luSa
                )}
              >
                {"Unlimited projects"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___1Rkzi
                )}
              >
                {"Unlimited wrokspaces"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__yNuRy
                )}
              >
                {"Unlimited publishes & syncs"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__ye9Jn
                )}
              >
                {"Unlimited cross-project imports"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__v7JW
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__u8GPl
                  )}
                >
                  {"Component library"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__ge3Po
                  )}
                  forceOverlay={false}
                  popover={
                    "Components are the building blocks for Plasmic projects. Access a growing library of production-ready components, from basic UI elements to major common app views, including table, list, grid, inbox, calendar, map, kanban and more. "
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__byDVl
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__eyn4R
                  )}
                >
                  {"Integration library"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___7Ktv
                  )}
                  popover={
                    "Choose from a wide range of pre-built integrations to power your project."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__juckx
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__mwPp4
                  )}
                >
                  {"Template library"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__bOXq
                  )}
                  popover={
                    "Browse a library of pre-built projects and sections that you can copy and customize in minutes.   "
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__ntHsg
                )}
              >
                {"Hosting"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__oju6W
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__hSo
                  )}
                >
                  {"Custom domains"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__lLz6
                  )}
                  popover={
                    "All Plasmic projects come with a default Plasmic subdomain, but you can also choose to use your own custom domain."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__lRcuy
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__cIoxV
                  )}
                >
                  {"Plasmic hosting"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__g7PC
                  )}
                  popover={
                    "We provide reliable and secure hosting for your app or website."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__dhudp
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___6Yyeu
                  )}
                >
                  {"Remove Plasmic branding"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__vgU5
                  )}
                  popover={
                    "Remove the Plasmic badge from all published projects."
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__igeUt
                )}
              >
                {"Building"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__f4Yjz
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__uC1L3
                  )}
                >
                  {"Components"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__aMwVh
                  )}
                  popover={
                    "Create your own reusable components within Plasmic. Design sections or atomic design system elements that can be used across multiple projects."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__lu1Wl
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__kVodx
                  )}
                >
                  {"Interactions"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__bE3Cs
                  )}
                  popover={
                    "Add free-form logic and behavior to your designs. Manage state with ease without worrying about low-level details."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__zBqR
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__jVXwd
                  )}
                >
                  {"Style tokens"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__jo7J
                  )}
                  popover={
                    "Apply colors, fonts, spacing, and more. Design with speed and consistency."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__bwZ3O
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__fcA
                  )}
                >
                  {"Image optimization"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___3N5W
                  )}
                  popover={
                    "Built-in image optimization, which ensures images are served at optimal sizes and at the proper resolution for the device screen."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__aqRa
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__j30Sj
                  )}
                >
                  {"Figma import"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__ssYA
                  )}
                  popover={
                    "If you have existing Figma designs, you can import them into Plasmic using our best-in-class Figma-to-web converter."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__tpQg
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__za1F6
                  )}
                >
                  {"Theming & branding"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__jghPf
                  )}
                  popover={
                    "Easily control the global styles across your app or website. Display your own branding on your app or website. "
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__lWfh9
                )}
              >
                {"Customization"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__usIv6
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___7Moln
                  )}
                >
                  {"Code components"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___27Ibt
                  )}
                  popover={
                    "Use custom React components\u2014from your own codebase or imported from npm libraries\u2014directly as building blocks in Plasmic Studio."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__ldeJh
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__laRqY
                  )}
                >
                  {"Integrate with codebase"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__rqHcN
                  )}
                  popover={
                    "Build within your existing applications. Use your own components, data, and code, and compose new screens and components."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__oSf0X
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__veThT
                  )}
                >
                  {"Extensions"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__d7F3Q
                  )}
                  popover={
                    "Customize the Plasmic visual builder to fit your applications and domain. Bring your own custom controls, components, data sources, auth, interactions, and other extensions to Plasmic."
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__d2RlD
                )}
              >
                {"CMS"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__b0PRb
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__qmg3L
                  )}
                >
                  {"Headless CMS"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__x90ZX
                  )}
                  popover={
                    "A headless API-driven structured content management system."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__v6Mx9
                )}
              >
                {"Unlimited CMS types"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__s7ZZ7
                )}
              >
                {"Unlimited CMS entries"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__jmzeG
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__tvf0R
                  )}
                >
                  {"Image optimization"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__x7Ra0
                  )}
                  popover={
                    "Reduces the file size of your images, without sacrificing quality, to improve overall performance."
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__kj2W3
                )}
              >
                {"Collaboration"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__f651
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__tkLn1
                  )}
                >
                  {"Multiplayer"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__wwrjC
                  )}
                  popover={
                    "Real-time collaborative editing\u2014have multiple users editing the same project at the same time."
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__jzGgP
                )}
              >
                {"Growth & Optimization"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__pkvw0
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___6MN9
                  )}
                >
                  {"Analytics"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__kjFzm
                  )}
                  popover={"Privacy-first analytics with page views."}
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__pNEuB
                )}
              >
                {"Security & Controls"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__yUbeY
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__gjsCn
                  )}
                >
                  {"Backup & export"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__i5Md9
                  )}
                  popover={
                    "API access to programmatically create full snapshot representations of any project."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__r8Dmh
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__s586
                  )}
                >
                  {"Code generation"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__mf0Td
                  )}
                  popover={
                    "Generate human-manageable source code. Eject at any time with your code, or continuously sync code for your components and interface with them via an API."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__kV0W
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__biz18
                  )}
                >
                  {"Transfer ownership"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___0CtXw
                  )}
                  popover={
                    "Fully transfer your Plasmic project to another Plasmic user."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__dlOya
                )}
              >
                {"30-day version history"}
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__dbKvl
                )}
              >
                {"Resources"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__gvAyO
                )}
              >
                {"Community forum support"}
              </PriceTierFeatureItem>
            </React.Fragment>
          }
          price={
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__uzeUx,
                {
                  [sty.textbillingFrequency_year__uzeUxgSc2D]: hasVariant(
                    $state,
                    "billingFrequency",
                    "year"
                  ),
                  [sty.textglobal_environment_website__uzeUxmVgSv]: hasVariant(
                    globalVariants,
                    "environment",
                    "website"
                  ),
                }
              )}
            >
              {hasVariant($state, "billingFrequency", "year") ? "$39" : "$49"}
            </div>
          }
          subprice={
            hasVariant($state, "billingFrequency", "year")
              ? "Billed yearly"
              : "Billed monthly"
          }
          tier={"starter"}
          valueProps={
            <React.Fragment>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text___9K5Oc,
                  {
                    [sty.textglobal_environment_website___9K5OcmVgSv]:
                      hasVariant(globalVariants, "environment", "website"),
                  }
                )}
              >
                {"Everything in Free, plus:"}
              </div>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__wNoC1,
                  {
                    [sty.priceTierFeatureItemglobal_environment_website__wNoC1MVgSv]:
                      hasVariant(globalVariants, "environment", "website"),
                  }
                )}
                tier={"free"}
              >
                <HoverableText
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableText__tOxj2
                  )}
                  popover={
                    "The maximum number of editors that can make changes to your projects"
                  }
                >
                  <div
                    className={classNames(
                      projectcss.all,
                      projectcss.__wab_text,
                      sty.text__rvSe
                    )}
                  >
                    {"3 collaborators"}
                  </div>
                </HoverableText>
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__zeLm
                )}
                tier={"starter"}
              >
                {"100k monthly page views"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__olwl6
                )}
                tier={"starter"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__igIci
                  )}
                >
                  {"Remove Plasmic badge"}
                </div>
              </PriceTierFeatureItem>
            </React.Fragment>
          }
        />

        <PriceTier
          data-plasmic-name={"newProTier"}
          data-plasmic-override={overrides.newProTier}
          className={classNames("__wab_instance", sty.newProTier, {
            [sty.newProTierglobal_environment_website]: hasVariant(
              globalVariants,
              "environment",
              "website"
            ),
          })}
          expandableBody={
            <React.Fragment>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text___71AdJ
                )}
              >
                {hasVariant(globalVariants, "screen", "mobileOnly")
                  ? "Pro includes (monthly):"
                  : "Free includes (monthly):"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__rp9Xj
                )}
              >
                {"Unlimited projects"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__iMmpz
                )}
              >
                {"Unlimited wrokspaces"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__yO4Yt
                )}
              >
                {"Unlimited publishes & syncs"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__ydsQ7
                )}
              >
                {"Unlimited cross-project imports"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__ktdi6
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__mdilp
                  )}
                >
                  {"Component library"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__tb5Mh
                  )}
                  forceOverlay={false}
                  popover={
                    "Components are the building blocks for Plasmic projects. Access a growing library of production-ready components, from basic UI elements to major common app views, including table, list, grid, inbox, calendar, map, kanban and more. "
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__hzbin
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__bW72W
                  )}
                >
                  {"Integration library"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__h2ECl
                  )}
                  popover={
                    "Choose from a wide range of pre-built integrations to power your project."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__izHik
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__cOfLp
                  )}
                >
                  {"Template library"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__foi95
                  )}
                  popover={
                    "Browse a library of pre-built projects and sections that you can copy and customize in minutes.   "
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__rY5Id
                )}
              >
                {"Hosting"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__st60X
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__bdWr9
                  )}
                >
                  {"Custom domains"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__l3N6U
                  )}
                  popover={
                    "All Plasmic projects come with a default Plasmic subdomain, but you can also choose to use your own custom domain."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__bXzd
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___2M9Fy
                  )}
                >
                  {"Plasmic hosting"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___5Hv0B
                  )}
                  popover={
                    "We provide reliable and secure hosting for your app or website."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__lSfDs
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__nZ1JJ
                  )}
                >
                  {"Remove Plasmic branding"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__yeZSk
                  )}
                  popover={
                    "Remove the Plasmic badge from all published projects."
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text___1OdUs
                )}
              >
                {"Building"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__z416P
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__lAeb
                  )}
                >
                  {"Components"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__ncKx1
                  )}
                  popover={
                    "Create your own reusable components within Plasmic. Design sections or atomic design system elements that can be used across multiple projects."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___0VF3G
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__mChN
                  )}
                >
                  {"Interactions"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__az2MB
                  )}
                  popover={
                    "Add free-form logic and behavior to your designs. Manage state with ease without worrying about low-level details."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__nc1OH
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__yWoWg
                  )}
                >
                  {"Style tokens"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__qm2Hv
                  )}
                  popover={
                    "Apply colors, fonts, spacing, and more. Design with speed and consistency."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__mpAaz
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__cxRml
                  )}
                >
                  {"Image optimization"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___0GVtB
                  )}
                  popover={
                    "Built-in image optimization, which ensures images are served at optimal sizes and at the proper resolution for the device screen."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__mA3G
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__uFrsT
                  )}
                >
                  {"Figma import"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___1X4Uw
                  )}
                  popover={
                    "If you have existing Figma designs, you can import them into Plasmic using our best-in-class Figma-to-web converter."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___90C8Y
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__rYmDb
                  )}
                >
                  {"Theming & branding"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__tjqyB
                  )}
                  popover={
                    "Easily control the global styles across your app or website. Display your own branding on your app or website. "
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__kU1N3
                )}
              >
                {"Customization"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__mibvl
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__iKkbB
                  )}
                >
                  {"Code components"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__saTiw
                  )}
                  popover={
                    "Use custom React components\u2014from your own codebase or imported from npm libraries\u2014directly as building blocks in Plasmic Studio."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___4171W
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__onGl7
                  )}
                >
                  {"Integrate with codebase"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__xSh8
                  )}
                  popover={
                    "Build within your existing applications. Use your own components, data, and code, and compose new screens and components."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__qynUs
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__zdMal
                  )}
                >
                  {"Extensions"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__rDv4O
                  )}
                  popover={
                    "Customize the Plasmic visual builder to fit your applications and domain. Bring your own custom controls, components, data sources, auth, interactions, and other extensions to Plasmic."
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__bgwde
                )}
              >
                {"CMS"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__mMLgX
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__vmGdm
                  )}
                >
                  {"Headless CMS"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__umeof
                  )}
                  popover={
                    "A headless API-driven structured content management system."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__p2UDp
                )}
              >
                {"Unlimited CMS types"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__a2Cud
                )}
              >
                {"Unlimited CMS entries"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__jdNev
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__ufb8M
                  )}
                >
                  {"Image optimization"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__kdUl7
                  )}
                  popover={
                    "Reduces the file size of your images, without sacrificing quality, to improve overall performance."
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__lKyts
                )}
              >
                {"Collaboration"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__rhLL
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__eFuYw
                  )}
                >
                  {"Multiplayer"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__uAekr
                  )}
                  popover={
                    "Real-time collaborative editing\u2014have multiple users editing the same project at the same time."
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__clgKr
                )}
              >
                {"Growth & Optimization"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__yhBz2
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__jpqHw
                  )}
                >
                  {"Analytics"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__qr1Mt
                  )}
                  popover={"Privacy-first analytics with page views."}
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__kmisn
                )}
              >
                {"Security & Controls"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___10IzR
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__ok9SN
                  )}
                >
                  {"Backup & export"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__o1TjN
                  )}
                  popover={
                    "API access to programmatically create full snapshot representations of any project."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__fhd6E
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__q3Hi1
                  )}
                >
                  {"Code generation"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__i9Zq
                  )}
                  popover={
                    "Generate human-manageable source code. Eject at any time with your code, or continuously sync code for your components and interface with them via an API."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__pmaCn
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__f1Dx0
                  )}
                >
                  {"Transfer ownership"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__xzqB
                  )}
                  popover={
                    "Fully transfer your Plasmic project to another Plasmic user."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__sTduc
                )}
              >
                {"90-day version history"}
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__nOyyf
                )}
              >
                {"Resources"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__w2YGd
                )}
              >
                {"Community forum support"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__eMvJo
                )}
              >
                {"Email & chat support"}
              </PriceTierFeatureItem>
            </React.Fragment>
          }
          mostPopular={
            hasVariant(globalVariants, "environment", "website") &&
            hasVariant(globalVariants, "screen", "mobileOnly")
              ? undefined
              : hasVariant(globalVariants, "environment", "website")
              ? true
              : undefined
          }
          price={
            hasVariant($state, "billingFrequency", "year") ? "$103" : "$129"
          }
          subprice={
            hasVariant($state, "billingFrequency", "year")
              ? "Billed yearly"
              : "Billed monthly"
          }
          tier={"pro"}
          valueProps={
            <React.Fragment>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__qmzG3
                )}
              >
                {"Everything in Starter, plus:"}
              </div>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__c6WZ,
                  {
                    [sty.priceTierFeatureItemglobal_environment_website__c6WZmVgSv]:
                      hasVariant(globalVariants, "environment", "website"),
                  }
                )}
                tier={"free"}
              >
                <HoverableText
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableText__bbMgq,
                    {
                      [sty.hoverableTextglobal_environment_website__bbMgQmVgSv]:
                        hasVariant(globalVariants, "environment", "website"),
                    }
                  )}
                  popover={
                    "The maximum number of editors that can make changes to your projects"
                  }
                >
                  {"4 collaborators"}
                </HoverableText>
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__d9B99
                )}
                tier={"starter"}
              >
                {"250k monthly page views"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__gos58,
                  {
                    [sty.priceTierFeatureItemglobal_environment_website__gos58MVgSv]:
                      hasVariant(globalVariants, "environment", "website"),
                  }
                )}
                tier={"pro"}
              >
                {"Priority support"}
              </PriceTierFeatureItem>
            </React.Fragment>
          }
        />

        <PriceTier
          data-plasmic-name={"newTeamTier"}
          data-plasmic-override={overrides.newTeamTier}
          className={classNames("__wab_instance", sty.newTeamTier, {
            [sty.newTeamTierglobal_environment_website]: hasVariant(
              globalVariants,
              "environment",
              "website"
            ),
          })}
          expandableBody={
            <React.Fragment>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__wfssn
                )}
              >
                {hasVariant(globalVariants, "screen", "mobileOnly")
                  ? "Scale includes (monthly):"
                  : "Free includes (monthly):"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__cuLlr
                )}
              >
                {"Unlimited projects"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__ckcDz
                )}
              >
                {"Unlimited wrokspaces"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__q1K
                )}
              >
                {"Unlimited publishes & syncs"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___1WqcQ
                )}
              >
                {"Unlimited cross-project imports"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___8X489
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__kVomX
                  )}
                >
                  {"Component library"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__hjLdP
                  )}
                  forceOverlay={false}
                  popover={
                    "Components are the building blocks for Plasmic projects. Access a growing library of production-ready components, from basic UI elements to major common app views, including table, list, grid, inbox, calendar, map, kanban and more. "
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__upG8G
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__emYts
                  )}
                >
                  {"Integration library"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___8C9OV
                  )}
                  popover={
                    "Choose from a wide range of pre-built integrations to power your project."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___3S5P8
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__gdNzg
                  )}
                >
                  {"Template library"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__llW2G
                  )}
                  popover={
                    "Browse a library of pre-built projects and sections that you can copy and customize in minutes.   "
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__kx43Y
                )}
              >
                {"Hosting"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__t0SDt
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__lGxPo
                  )}
                >
                  {"Custom domains"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__t8IVf
                  )}
                  popover={
                    "All Plasmic projects come with a default Plasmic subdomain, but you can also choose to use your own custom domain."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__ebxS
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__v4QR
                  )}
                >
                  {"Plasmic hosting"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__rewdy
                  )}
                  popover={
                    "We provide reliable and secure hosting for your app or website."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__g9K83
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___62QpX
                  )}
                >
                  {"Remove Plasmic branding"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__wEsez
                  )}
                  popover={
                    "Remove the Plasmic badge from all published projects."
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text___4HWwM
                )}
              >
                {"Building"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__bZxq4
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__aIla
                  )}
                >
                  {"Components"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__wrNcO
                  )}
                  popover={
                    "Create your own reusable components within Plasmic. Design sections or atomic design system elements that can be used across multiple projects."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__jLwsp
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__dp845
                  )}
                >
                  {"Interactions"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__f6Wzn
                  )}
                  popover={
                    "Add free-form logic and behavior to your designs. Manage state with ease without worrying about low-level details."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__lpGj1
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__si5Jv
                  )}
                >
                  {"Style tokens"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__n3JXp
                  )}
                  popover={
                    "Apply colors, fonts, spacing, and more. Design with speed and consistency."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__pn7Q3
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__tzy5P
                  )}
                >
                  {"Image optimization"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__cT1Gf
                  )}
                  popover={
                    "Built-in image optimization, which ensures images are served at optimal sizes and at the proper resolution for the device screen."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__d7ED1
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__w06Cc
                  )}
                >
                  {"Figma import"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__wotPu
                  )}
                  popover={
                    "If you have existing Figma designs, you can import them into Plasmic using our best-in-class Figma-to-web converter."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___83Fa
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__iwwdS
                  )}
                >
                  {"Theming & branding"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___8BrJa
                  )}
                  popover={
                    "Easily control the global styles across your app or website. Display your own branding on your app or website. "
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text___6Nefj
                )}
              >
                {"Customization"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___5UJnB
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__gr9Sl
                  )}
                >
                  {"Code components"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__ciqTn
                  )}
                  popover={
                    "Use custom React components\u2014from your own codebase or imported from npm libraries\u2014directly as building blocks in Plasmic Studio."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___7Qp3S
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__p5CGx
                  )}
                >
                  {"Integrate with codebase"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__zdEl7
                  )}
                  popover={
                    "Build within your existing applications. Use your own components, data, and code, and compose new screens and components."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__zp9WG
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___8LkAr
                  )}
                >
                  {"Extensions"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__fDQg
                  )}
                  popover={
                    "Customize the Plasmic visual builder to fit your applications and domain. Bring your own custom controls, components, data sources, auth, interactions, and other extensions to Plasmic."
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__ekbum
                )}
              >
                {"CMS"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___99Q0N
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__ggqhz
                  )}
                >
                  {"Headless CMS"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__y5Gb9
                  )}
                  popover={
                    "A headless API-driven structured content management system."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__kYem7
                )}
              >
                {"Unlimited CMS types"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__ric5E
                )}
              >
                {"Unlimited CMS entries"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__adusl
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__e4CpE
                  )}
                >
                  {"Image optimization"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__tdq0M
                  )}
                  popover={
                    "Reduces the file size of your images, without sacrificing quality, to improve overall performance."
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__yc5Yq
                )}
              >
                {"Collaboration"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__x2KeB
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___1KgmD
                  )}
                >
                  {"Multiplayer"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__jpqlD
                  )}
                  popover={
                    "Real-time collaborative editing\u2014have multiple users editing the same project at the same time."
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__nMrPv
                )}
              >
                {"Growth & Optimization"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__aNnVc
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___2O8V8
                  )}
                >
                  {"Analytics"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__dtVEa
                  )}
                  popover={"Privacy-first analytics with page views."}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__v9Sld
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__o6Yhr
                  )}
                >
                  {"A/B Testing"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__tdd3N
                  )}
                  popover={
                    "Easily experiment with multiple variations of a page to measure which is more effective. Promote the winning edits as the main copy. Support multiple concurrent A/B tests at the same time."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__o6ByN
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__opnHh
                  )}
                >
                  {"Scheduled content"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__yyGb
                  )}
                  popover={
                    "Create a different variation of a page that is activated during a time interval, such as a holiday promotion."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__fcs1T
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__mYAo7
                  )}
                >
                  {"Custom targeting & segmentation"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___8V2Bj
                  )}
                  popover={
                    "Create a different variation of a page that is activated under certain conditions, such as when the user has a certain item in their shopping cart."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__fn2X
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__lbIQe
                  )}
                >
                  {"Localization"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__i1Vu6
                  )}
                  popover={
                    "Integrate with popular localization frameworks like Lingui, or react-i18next."
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__r9MkW
                )}
              >
                {"Security & Controls"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__s6Pcg
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__lJfOw
                  )}
                >
                  {"Backup & export"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__rq2N3
                  )}
                  popover={
                    "API access to programmatically create full snapshot representations of any project."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__qnpMb
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__xdhCe
                  )}
                >
                  {"Code generation"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__lu6Kt
                  )}
                  popover={
                    "Generate human-manageable source code. Eject at any time with your code, or continuously sync code for your components and interface with them via an API."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__moLPp
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___7IZ1
                  )}
                >
                  {"Transfer ownership"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__vwOb
                  )}
                  popover={
                    "Fully transfer your Plasmic project to another Plasmic user."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__nom1M
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__vXg1J
                  )}
                >
                  {"180-day version history"}
                </div>
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__kStrP
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__cXzp
                  )}
                >
                  {"Team roles & permissions"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__pwP81
                  )}
                  popover={
                    "Assign permission levels for pre-set roles (admin, developer, designer, editor)."
                  }
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__tY1Hm
                )}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___5CQg
                  )}
                >
                  {"Content creator mode"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__vzsne
                  )}
                  popover={
                    <div
                      className={classNames(
                        projectcss.all,
                        projectcss.__wab_text,
                        sty.text__wtzsj
                      )}
                    >
                      {
                        "Enables a streamlined editing experience with full content control, but allows admin to limit styling options and predefine the building blocks, customizable per workspace and project."
                      }
                    </div>
                  }
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__czXsI
                )}
              >
                {"Resources"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__yvMJp
                )}
              >
                {"Community forum support"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__buGwq
                )}
              >
                {"Email & chat support"}
              </PriceTierFeatureItem>
            </React.Fragment>
          }
          price={
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text___6KKCm,
                {
                  [sty.textbillingFrequency_year___6KKCmgSc2D]: hasVariant(
                    $state,
                    "billingFrequency",
                    "year"
                  ),
                }
              )}
            >
              {hasVariant($state, "billingFrequency", "year") ? "$399" : "$499"}
            </div>
          }
          subprice={
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__hNyrb,
                {
                  [sty.textbillingFrequency_year__hNyrbgSc2D]: hasVariant(
                    $state,
                    "billingFrequency",
                    "year"
                  ),
                }
              )}
            >
              {hasVariant($state, "billingFrequency", "year")
                ? "Billed yearly"
                : "Billed monthly"}
            </div>
          }
          tier={"team"}
          valueProps={
            <React.Fragment>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__lp2Yi,
                  {
                    [sty.textglobal_environment_website__lp2YimVgSv]:
                      hasVariant(globalVariants, "environment", "website"),
                  }
                )}
              >
                {"Everything in Pro, plus:"}
              </div>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___0SrWq
                )}
                tier={"free"}
              >
                <HoverableText
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableText___1Ynqj
                  )}
                  popover={
                    "The maximum number of editors that can make changes to your projects"
                  }
                >
                  {"8 collaborators"}
                </HoverableText>
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__rsp0V
                )}
                tier={"starter"}
              >
                {"500k monthly page views"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__mZbOl
                )}
                tier={"team"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___4Iy2
                  )}
                >
                  {"Content creator mode"}
                </div>
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__mDkX6
                )}
                tier={"team"}
              >
                {"A/B testing"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__xxybs
                )}
                tier={"team"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__a1AFi
                  )}
                >
                  {"Scheduled content"}
                </div>
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__mJV
                )}
                tier={"team"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__tTk9C
                  )}
                >
                  {"Custom targeting"}
                </div>
              </PriceTierFeatureItem>
            </React.Fragment>
          }
        />

        <PriceTier
          data-plasmic-name={"newEnterpriseTier"}
          data-plasmic-override={overrides.newEnterpriseTier}
          className={classNames("__wab_instance", sty.newEnterpriseTier, {
            [sty.newEnterpriseTierglobal_environment_website]: hasVariant(
              globalVariants,
              "environment",
              "website"
            ),
            [sty.newEnterpriseTiershowOutdatedTier]: hasVariant(
              $state,
              "showOutdatedTier",
              "showOutdatedTier"
            ),
          })}
          expandableBody={
            <React.Fragment>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text___7ZlfK
                )}
              >
                {"Enterprise includes (monthly):"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__f6Joq
                )}
                tier={"enterprise"}
              >
                {"Unlimited projects"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__irP83
                )}
                tier={"enterprise"}
              >
                {"Unlimited wrokspaces"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__qYq6S
                )}
                tier={"enterprise"}
              >
                {"Unlimited publishes & syncs"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__itvJj
                )}
                tier={"enterprise"}
              >
                {"Unlimited cross-project imports"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___5ScDk
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__uIJq
                  )}
                >
                  {"Component library"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__aPM
                  )}
                  forceOverlay={false}
                  popover={
                    "Components are the building blocks for Plasmic projects. Access a growing library of production-ready components, from basic UI elements to major common app views, including table, list, grid, inbox, calendar, map, kanban and more. "
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__nQrEr
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__hwyPh
                  )}
                >
                  {"Integration library"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___11KZk
                  )}
                  popover={
                    "Choose from a wide range of pre-built integrations to power your project."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___3HLw4
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__dLeQy
                  )}
                >
                  {"Template library"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__fCqxk
                  )}
                  popover={
                    "Browse a library of pre-built projects and sections that you can copy and customize in minutes.   "
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__sNu1J
                )}
              >
                {"Hosting"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___5Pf2
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___8N39
                  )}
                >
                  {"Custom domains"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__inlKa
                  )}
                  popover={
                    "All Plasmic projects come with a default Plasmic subdomain, but you can also choose to use your own custom domain."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__lO5UX
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__ytjlI
                  )}
                >
                  {"Plasmic hosting"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___9JKsx
                  )}
                  popover={
                    "We provide reliable and secure hosting for your app or website."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___3EQt1
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___8T5Ay
                  )}
                >
                  {"Remove Plasmic branding"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__ysShe
                  )}
                  popover={
                    "Remove the Plasmic badge from all published projects."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__zj1O7
                )}
              >
                {"Building"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__iSvNt
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__fOq7Q
                  )}
                >
                  {"Components"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__iAgCv
                  )}
                  popover={
                    "Create your own reusable components within Plasmic. Design sections or atomic design system elements that can be used across multiple projects."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__kTrMo
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__qeIWi
                  )}
                >
                  {"Interactions"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__xwxfz
                  )}
                  popover={
                    "Add free-form logic and behavior to your designs. Manage state with ease without worrying about low-level details."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___6JrKe
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__sOvy
                  )}
                >
                  {"Style tokens"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___46Cdi
                  )}
                  popover={
                    "Apply colors, fonts, spacing, and more. Design with speed and consistency."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__k073
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__kFfXv
                  )}
                >
                  {"Image optimization"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__kvJle
                  )}
                  popover={
                    "Built-in image optimization, which ensures images are served at optimal sizes and at the proper resolution for the device screen."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__vBeP
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__snMBh
                  )}
                >
                  {"Figma import"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__qWeGy
                  )}
                  popover={
                    "If you have existing Figma designs, you can import them into Plasmic using our best-in-class Figma-to-web converter."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__fOkTo
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__s0NLb
                  )}
                >
                  {"Theming & branding"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__ytp3V
                  )}
                  popover={
                    "Easily control the global styles across your app or website. Display your own branding on your app or website. "
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__kyAK
                )}
              >
                {"Customization"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__s3SHi
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__gQz
                  )}
                >
                  {"Code components"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__x8Eih
                  )}
                  popover={
                    "Use custom React components\u2014from your own codebase or imported from npm libraries\u2014directly as building blocks in Plasmic Studio."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__j9IYw
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__srlsk
                  )}
                >
                  {"Integrate with codebase"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__nh4Qc
                  )}
                  popover={
                    "Build within your existing applications. Use your own components, data, and code, and compose new screens and components."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__sdSqI
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___5Vi55
                  )}
                >
                  {"Extensions"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__gO2Cz
                  )}
                  popover={
                    "Customize the Plasmic visual builder to fit your applications and domain. Bring your own custom controls, components, data sources, auth, interactions, and other extensions to Plasmic."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__yU8Ul
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___6ZXoP
                  )}
                >
                  {"White labelling"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__wTxOf
                  )}
                  popover={"Add your own branding to the Plasmic studio."}
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__gYKc
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___1UF9
                  )}
                >
                  {"Embedding"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__jOg3U
                  )}
                  popover={
                    "Integrate Plasmic as a visual builder within your product."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__tgv3K
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__jSi7N
                  )}
                >
                  {"Custom integrations priority"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__hNbEb
                  )}
                  popover={
                    "If you need an integration that doesn't exist yet, we'll work with you to get it built."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__gdx4K
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___0MTnT
                  )}
                >
                  {"Custom components priority"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__yEhte
                  )}
                  popover={
                    "If you need a component that doesn't exist yet, we'll work with you to get it built."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__vj5T3
                )}
                tier={"enterprise"}
              >
                {"Extended platform APIs"}
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__ns3Lt
                )}
              >
                {"CMS"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__wC5Du
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__yb5K6
                  )}
                >
                  {"Headless CMS"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__wuzse
                  )}
                  popover={
                    "A headless API-driven structured content management system."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__se0Qk
                )}
                tier={"enterprise"}
              >
                {"Unlimited CMS types"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__wei
                )}
                tier={"enterprise"}
              >
                {"Unlimited CMS entries"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__l7Pra
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__kNeRw
                  )}
                >
                  {"Image optimization"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___0SFkE
                  )}
                  popover={
                    "Reduces the file size of your images, without sacrificing quality, to improve overall performance."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text___08CEc
                )}
              >
                {"Collaboration"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__z6JZ1
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__wJjt
                  )}
                >
                  {"Multiplayer"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__jh0GB
                  )}
                  popover={
                    "Real-time collaborative editing\u2014have multiple users editing the same project at the same time."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__jKxc
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__u24Oj
                  )}
                >
                  {"Branching (Beta)"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__lQ43F
                  )}
                  popover={
                    "Allow editors to make changes on their own copies of the project. When ready, merge their changes back into the main copy. Review and approve the changes as desired, or resolve conflicts."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__ij6H9
                )}
              >
                {"Growth & Optimization"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___5D5Ei
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__kiaSh
                  )}
                >
                  {"Analytics"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__qbYvd
                  )}
                  popover={"Privacy-first analytics with page views."}
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__wDLa
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__c0WRn
                  )}
                >
                  {"A/B Testing"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__uWbIp
                  )}
                  popover={
                    "Easily experiment with multiple variations of a page to measure which is more effective. Promote the winning edits as the main copy. Support multiple concurrent A/B tests at the same time."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__zn4Ir
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__kMAe
                  )}
                >
                  {"Scheduled content"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__w2CWx
                  )}
                  popover={
                    "Create a different variation of a page that is activated during a time interval, such as a holiday promotion."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___7DOhz
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__t2Uez
                  )}
                >
                  {"Custom targeting & segmentation"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__p3RIp
                  )}
                  popover={
                    "Create a different variation of a page that is activated under certain conditions, such as when the user has a certain item in their shopping cart."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__mqno6
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___0NLlu
                  )}
                >
                  {"Localization"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__tq8Lu
                  )}
                  popover={
                    "Integrate with popular localization frameworks like Lingui, or react-i18next."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__c9DHw
                )}
              >
                {"Security & Controls"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__nIczi
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__flqSh
                  )}
                >
                  {"Backup & export"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__uhTd
                  )}
                  popover={
                    "API access to programmatically create full snapshot representations of any project."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__fl5Dn
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__qPzee
                  )}
                >
                  {"Code generation"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__mEtTw
                  )}
                  popover={
                    "Generate human-manageable source code. Eject at any time with your code, or continuously sync code for your components and interface with them via an API."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__amDgy
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__auCpe
                  )}
                >
                  {"Transfer ownership"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__jPrQ
                  )}
                  popover={
                    "Fully transfer your Plasmic project to another Plasmic user."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__bgqQ0
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__eYCud
                  )}
                >
                  {"Custom version history"}
                </div>
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___04ZRv
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___0UUvh
                  )}
                >
                  {"Team roles & permissions"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__xj4Em
                  )}
                  popover={
                    "Assign permission levels for pre-set roles (admin, developer, designer, editor)."
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__g3Bm4
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__n67Y1
                  )}
                >
                  {"Content creator mode"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__la69L
                  )}
                  popover={
                    <div
                      className={classNames(
                        projectcss.all,
                        projectcss.__wab_text,
                        sty.text__weEuy
                      )}
                    >
                      {
                        "Enables a streamlined editing experience with full content control, but allows admin to limit styling options and predefine the building blocks, customizable per workspace and project."
                      }
                    </div>
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__c8EY
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___37Gge
                  )}
                >
                  {"Custom content creator mode"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__w3Nl2
                  )}
                  popover={
                    <div
                      className={classNames(
                        projectcss.all,
                        projectcss.__wab_text,
                        sty.text__cgI8E
                      )}
                    >
                      {
                        "Customize what is enabled or disabled for the content creator role to control the Plasmic visual editing experience."
                      }
                    </div>
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__mwIpq
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__nfpPq
                  )}
                >
                  {"Domain capture"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__kiOkO
                  )}
                  popover={
                    <div
                      className={classNames(
                        projectcss.all,
                        projectcss.__wab_text,
                        sty.text__fbGiY
                      )}
                    >
                      {
                        "Ensure that all Plasmic users who sign up with @yourcompany.com are automatically managed as part of your team."
                      }
                    </div>
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__nY3T
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__xo6Ky
                  )}
                >
                  {"SSO"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__q3IL
                  )}
                  popover={
                    <div
                      className={classNames(
                        projectcss.all,
                        projectcss.__wab_text,
                        sty.text___2FziP
                      )}
                    >
                      {"Support for Okta, Google, and custom SSO providers."}
                    </div>
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__b4Gk
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__y6T2E
                  )}
                >
                  {"Availability SLAs"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___3JMnt
                  )}
                  popover={
                    <div
                      className={classNames(
                        projectcss.all,
                        projectcss.__wab_text,
                        sty.text__ksnEv
                      )}
                    >
                      {"99.5% API and Plasmic hosting availability guarantees."}
                    </div>
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__oi9Ug
                )}
              >
                {"Resources"}
              </div>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__xoVfy
                )}
                tier={"enterprise"}
              >
                {"Community forum support"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__sB8Fb
                )}
                tier={"enterprise"}
              >
                {"Email & chat support"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__cirZs
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__ceFt
                  )}
                >
                  {"Response time SLAs"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon___6ERr
                  )}
                  popover={
                    <div
                      className={classNames(
                        projectcss.all,
                        projectcss.__wab_text,
                        sty.text__r3M3C
                      )}
                    >
                      {"24-hour response time guarantee."}
                    </div>
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__cWlz
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__kp70J
                  )}
                >
                  {"Onboarding"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__tGDp
                  )}
                  popover={
                    <div
                      className={classNames(
                        projectcss.all,
                        projectcss.__wab_text,
                        sty.text__tlfXa
                      )}
                    >
                      {
                        "Training and integration to ensure you are able to successfully get started with Plasmic."
                      }
                    </div>
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                checkIcon={true}
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__eYlye
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__mxLXz
                  )}
                >
                  {"Private Slack channel"}
                </div>
                <HoverableIcon
                  className={classNames(
                    "__wab_instance",
                    sty.hoverableIcon__nStuX
                  )}
                  popover={
                    <div
                      className={classNames(
                        projectcss.all,
                        projectcss.__wab_text,
                        sty.text__x7HK
                      )}
                    >
                      {
                        "Direct line to our engineering and product teams for questions and feedback."
                      }
                    </div>
                  }
                  white={true}
                />
              </PriceTierFeatureItem>
            </React.Fragment>
          }
          price={"Custom"}
          tier={"enterprise"}
          valueProps={
            <React.Fragment>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__u9AGd
                )}
              >
                {"Everything in Scale, plus:"}
              </div>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__fSmAi
                )}
                tier={"enterprise"}
              >
                {"Custom collaborators"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__eqk5
                )}
                tier={"enterprise"}
              >
                {"Custom page views"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__sc8E9
                )}
                tier={"enterprise"}
              >
                {"Custom integrations"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__dTp5B
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__brufQ
                  )}
                >
                  {"Custom roles & permissions"}
                </div>
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__nqhYu
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__taizU
                  )}
                >
                  {"SSO & domain capture"}
                </div>
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__ja5Pk
                )}
                tier={"enterprise"}
              >
                {"Whitelabeling & embedding"}
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem__q1T0H
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__rsSYq
                  )}
                >
                  {"Availability & response time SLAs"}
                </div>
              </PriceTierFeatureItem>
              <PriceTierFeatureItem
                className={classNames(
                  "__wab_instance",
                  sty.priceTierFeatureItem___23Gzj
                )}
                tier={"enterprise"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___0VqYr
                  )}
                >
                  {"Dedicated support & onboarding"}
                </div>
              </PriceTierFeatureItem>
            </React.Fragment>
          }
        />
      </Stack__>
    </Stack__>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "link",
    "billingFrequencyToggle",
    "newGrandfatheredTier",
    "newOutdatedTier",
    "newFreeTier",
    "newStarterTier",
    "newProTier",
    "newTeamTier",
    "newEnterpriseTier",
  ],

  link: ["link"],
  billingFrequencyToggle: ["billingFrequencyToggle"],
  newGrandfatheredTier: ["newGrandfatheredTier"],
  newOutdatedTier: ["newOutdatedTier"],
  newFreeTier: ["newFreeTier"],
  newStarterTier: ["newStarterTier"],
  newProTier: ["newProTier"],
  newTeamTier: ["newTeamTier"],
  newEnterpriseTier: ["newEnterpriseTier"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  link: "a";
  billingFrequencyToggle: typeof Switch;
  newGrandfatheredTier: typeof PriceTier;
  newOutdatedTier: typeof PriceTier;
  newFreeTier: typeof PriceTier;
  newStarterTier: typeof PriceTier;
  newProTier: typeof PriceTier;
  newTeamTier: typeof PriceTier;
  newEnterpriseTier: typeof PriceTier;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicPriceTierPicker__OverridesType,
  DescendantsType<T>
>;

type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicPriceTierPicker__VariantsArgs;
    args?: PlasmicPriceTierPicker__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicPriceTierPicker__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicPriceTierPicker__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicPriceTierPicker__ArgProps,
          internalVariantPropNames: PlasmicPriceTierPicker__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicPriceTierPicker__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicPriceTierPicker";
  } else {
    func.displayName = `PlasmicPriceTierPicker.${nodeName}`;
  }
  return func;
}

export const PlasmicPriceTierPicker = Object.assign(
  // Top-level PlasmicPriceTierPicker renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    link: makeNodeComponent("link"),
    billingFrequencyToggle: makeNodeComponent("billingFrequencyToggle"),
    newGrandfatheredTier: makeNodeComponent("newGrandfatheredTier"),
    newOutdatedTier: makeNodeComponent("newOutdatedTier"),
    newFreeTier: makeNodeComponent("newFreeTier"),
    newStarterTier: makeNodeComponent("newStarterTier"),
    newProTier: makeNodeComponent("newProTier"),
    newTeamTier: makeNodeComponent("newTeamTier"),
    newEnterpriseTier: makeNodeComponent("newEnterpriseTier"),

    // Metadata about props expected for PlasmicPriceTierPicker
    internalVariantProps: PlasmicPriceTierPicker__VariantProps,
    internalArgProps: PlasmicPriceTierPicker__ArgProps,
  }
);

export default PlasmicPriceTierPicker;
/* prettier-ignore-end */
