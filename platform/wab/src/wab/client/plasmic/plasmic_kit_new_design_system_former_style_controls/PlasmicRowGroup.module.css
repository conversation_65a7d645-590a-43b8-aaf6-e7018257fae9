.root {
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  height: 40px;
  transition-property: all;
  transition-duration: 0.3s;
  min-width: 0;
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.3s;
  padding: 8px 12px;
}
.root > :global(.__wab_flex-container) {
  flex-direction: row;
  justify-content: center;
  align-items: center;
  min-width: 0;
  margin-left: calc(0px - 4px);
  width: calc(100% + 4px);
}
.root > :global(.__wab_flex-container) > *,
.root > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.root > :global(.__wab_flex-container) > picture > img,
.root > :global(.__wab_flex-container) > :global(.__wab_slot) > picture > img {
  margin-left: 4px;
}
.root:hover {
  background: var(--token-bV4cCeIniS6);
}
.root:hover > :global(.__wab_flex-container) {
  justify-content: flex-start;
  align-items: center;
}
.iconContainer {
  display: flex;
  flex-direction: row;
  position: relative;
  align-items: center;
  justify-content: flex-start;
}
.svg {
  object-fit: cover;
  width: 16px;
  height: 16px;
  color: var(--token-UunsGa2Y3t3);
  transition-property: all;
  transition-duration: 0.5s;
  flex-shrink: 0;
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.5s;
}
.svgisOpen {
  transform: rotateX(0deg) rotateY(0deg) rotateZ(90deg);
}
.labelContainer {
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  height: auto;
  min-width: 0;
}
.labelContainer > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  min-width: 0;
  margin-left: calc(0px - 8px);
  width: calc(100% + 8px);
}
.labelContainer > :global(.__wab_flex-container) > *,
.labelContainer > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.labelContainer > :global(.__wab_flex-container) > picture > img,
.labelContainer
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: 8px;
}
.slotTargetChildren {
  white-space: pre;
}
.slotTargetChildren > :global(.__wab_text),
.slotTargetChildren > :global(.__wab_expr_html_text),
.slotTargetChildren > :global(.__wab_slot-string-wrapper),
.slotTargetChildren > :global(.__wab_slot) > :global(.__wab_text),
.slotTargetChildren > :global(.__wab_slot) > :global(.__wab_expr_html_text),
.slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot-string-wrapper),
.slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_text),
.slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_expr_html_text),
.slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_slot-string-wrapper),
.slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_text),
.slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_expr_html_text),
.slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_slot-string-wrapper) {
  text-overflow: ellipsis;
}
.slotTargetChildren > *,
.slotTargetChildren > :global(.__wab_slot) > *,
.slotTargetChildren > :global(.__wab_slot) > :global(.__wab_slot) > *,
.slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > *,
.slotTargetChildren > picture > img,
.slotTargetChildren > :global(.__wab_slot) > picture > img,
.slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > picture
  > img,
.slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > picture
  > img {
  overflow: hidden;
}
.sizeContainer {
  display: flex;
  flex-direction: row;
  position: relative;
  align-items: center;
  justify-content: center;
  width: auto;
  height: auto;
  max-width: 100%;
  background: var(--token-bV4cCeIniS6);
  border-radius: 4px;
  padding: 0px 6px;
}
.slotTargetGroupSize {
  color: var(--token-UunsGa2Y3t3);
  user-select: none;
}
.actionsContainer {
  display: flex;
  flex-direction: row;
  position: relative;
  height: auto;
}
.actionsContainer > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  margin-left: calc(0px - 8px);
  width: calc(100% + 8px);
}
.actionsContainer > :global(.__wab_flex-container) > *,
.actionsContainer > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.actionsContainer > :global(.__wab_flex-container) > picture > img,
.actionsContainer
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: 8px;
}
.actionsContainershowActions {
  display: flex;
}
.slotTargetActions {
  font-size: 20px;
}
.svg___7Gen4 {
  object-fit: cover;
  max-width: 100%;
  height: 1em;
}
.menuButton:global(.__wab_instance):global(.__wab_instance) {
  max-width: 100%;
  flex-shrink: 0;
}
.menuButtonhasMenu:global(.__wab_instance):global(.__wab_instance) {
  flex-shrink: 0;
  display: none;
}
.root:hover .menuButton:global(.__wab_instance):global(.__wab_instance) {
  flex-shrink: 0;
}
.roothasMenu:hover
  .menuButtonhasMenu:global(.__wab_instance):global(.__wab_instance) {
  flex-shrink: 0;
  display: block;
}
