// @ts-nocheck
/* eslint-disable */
/* tslint:disable */
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: dyzP6dbCdycwJpqiR2zkwe
// Component: mJFZOBWGNu
import * as React from "react";

import * as p from "@plasmicapp/react-web";

import {
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  hasVariant,
} from "@plasmicapp/react-web";

import { useCodegenType } from "./PlasmicGlobalVariant__CodegenType"; // plasmic-import: IFgLgWglLv/globalVariant

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_design_system_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "./plasmic_plasmic_kit_docs_portal.module.css"; // plasmic-import: dyzP6dbCdycwJpqiR2zkwe/projectcss
import sty from "./PlasmicDocsPortalBranch.module.css"; // plasmic-import: mJFZOBWGNu/css

export type PlasmicDocsPortalBranch__VariantMembers = {};

export type PlasmicDocsPortalBranch__VariantsArgs = {};
type VariantPropType = keyof PlasmicDocsPortalBranch__VariantsArgs;
export const PlasmicDocsPortalBranch__VariantProps =
  new Array<VariantPropType>();

export type PlasmicDocsPortalBranch__ArgsType = {
  children?: React.ReactNode;
  slot?: React.ReactNode;
  destination?: string;
};

type ArgPropType = keyof PlasmicDocsPortalBranch__ArgsType;
export const PlasmicDocsPortalBranch__ArgProps = new Array<ArgPropType>(
  "children",
  "slot",
  "destination"
);

export type PlasmicDocsPortalBranch__OverridesType = {
  root?: p.Flex<"a">;
  freeBox?: p.Flex<"div">;
};

export interface DefaultDocsPortalBranchProps {
  children?: React.ReactNode;
  slot?: React.ReactNode;
  destination?: string;
  className?: string;
}

export const defaultDocsPortalBranch__Args: Partial<PlasmicDocsPortalBranch__ArgsType> =
  {};

function PlasmicDocsPortalBranch__RenderFunc(props: {
  variants: PlasmicDocsPortalBranch__VariantsArgs;
  args: PlasmicDocsPortalBranch__ArgsType;
  overrides: PlasmicDocsPortalBranch__OverridesType;

  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;
  const args = Object.assign({}, defaultDocsPortalBranch__Args, props.args);
  const $props = args;

  const globalVariants = ensureGlobalVariants({
    codegenType: useCodegenType(),
  });

  return (
    <p.Stack
      as={"a"}
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      hasGap={true}
      className={classNames(
        projectcss.all,
        projectcss.a,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        {
          [sty.rootglobal_codegenType_codegen]: hasVariant(
            globalVariants,
            "codegenType",
            "codegen"
          ),
        }
      )}
      href={args.destination}
    >
      {p.renderPlasmicSlot({
        defaultContents: "Codegen",
        value: args.children,
        className: classNames(sty.slotTargetChildren),
      })}

      <p.Stack
        as={"div"}
        data-plasmic-name={"freeBox"}
        data-plasmic-override={overrides.freeBox}
        hasGap={true}
        className={classNames(projectcss.all, sty.freeBox, {
          [sty.freeBoxglobal_codegenType_loader]: hasVariant(
            globalVariants,
            "codegenType",
            "loader"
          ),
        })}
      >
        {p.renderPlasmicSlot({
          defaultContents:
            "Generate presentational components into your codebase.",
          value: args.slot,
          className: classNames(sty.slotTargetSlot, {
            [sty.slotTargetSlotglobal_codegenType_loader]: hasVariant(
              globalVariants,
              "codegenType",
              "loader"
            ),
          }),
        })}
      </p.Stack>
    </p.Stack>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "freeBox"],
  freeBox: ["freeBox"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "a";
  freeBox: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicDocsPortalBranch__OverridesType,
  DescendantsType<T>
>;

type NodeComponentProps<T extends NodeNameType> = {
  // Explicitly specify variants, args, and overrides as objects
  variants?: PlasmicDocsPortalBranch__VariantsArgs;
  args?: PlasmicDocsPortalBranch__ArgsType;
  overrides?: NodeOverridesType<T>;
} & Omit<PlasmicDocsPortalBranch__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
  // Specify args directly as props
  Omit<PlasmicDocsPortalBranch__ArgsType, ReservedPropsType> &
  // Specify overrides for each element directly as props
  Omit<
    NodeOverridesType<T>,
    ReservedPropsType | VariantPropType | ArgPropType
  > &
  // Specify props for the root element
  Omit<
    Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
    ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
  >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = deriveRenderOpts(props, {
      name: nodeName,
      descendantNames: [...PlasmicDescendants[nodeName]],
      internalArgPropNames: PlasmicDocsPortalBranch__ArgProps,
      internalVariantPropNames: PlasmicDocsPortalBranch__VariantProps,
    });

    return PlasmicDocsPortalBranch__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicDocsPortalBranch";
  } else {
    func.displayName = `PlasmicDocsPortalBranch.${nodeName}`;
  }
  return func;
}

export const PlasmicDocsPortalBranch = Object.assign(
  // Top-level PlasmicDocsPortalBranch renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    freeBox: makeNodeComponent("freeBox"),

    // Metadata about props expected for PlasmicDocsPortalBranch
    internalVariantProps: PlasmicDocsPortalBranch__VariantProps,
    internalArgProps: PlasmicDocsPortalBranch__ArgProps,
  }
);

export default PlasmicDocsPortalBranch;
/* prettier-ignore-end */
