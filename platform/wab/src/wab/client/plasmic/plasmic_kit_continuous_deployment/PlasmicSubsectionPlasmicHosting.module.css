.root {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  min-width: 0;
}
.root > :global(.__wab_flex-container) {
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  min-width: 0;
  margin-top: calc(0px - 16px);
  height: calc(100% + 16px);
}
.root > :global(.__wab_flex-container) > *,
.root > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.root > :global(.__wab_flex-container) > picture > img,
.root > :global(.__wab_flex-container) > :global(.__wab_slot) > picture > img {
  margin-top: 16px;
}
.freeBox___9QCvn {
  display: block;
  position: absolute;
  width: 1px;
  height: 110%;
  left: 15.5px;
  top: 0px;
  background: var(--token-0vHxN11ixM);
}
.freeBox___9MQmz {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.freeBox__qZ5J8 {
  display: flex;
  flex-direction: row;
  position: relative;
  left: auto;
  top: auto;
}
.freeBox__qZ5J8 > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  margin-left: calc(0px - 8px);
  width: calc(100% + 8px);
}
.freeBox__qZ5J8 > :global(.__wab_flex-container) > *,
.freeBox__qZ5J8 > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox__qZ5J8 > :global(.__wab_flex-container) > picture > img,
.freeBox__qZ5J8
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: 8px;
}
.freeBox__hq1LL {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  overflow: hidden;
  background: var(--token-O4S7RMTqZ3);
  flex-shrink: 0;
  border-radius: 16px;
}
.checkbox:global(.__wab_instance) {
  position: relative;
}
.img {
  position: relative;
  object-fit: cover;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}
.img > picture > img {
  object-fit: cover;
}
.text__oARo5 {
  position: relative;
  font-weight: 600;
}
.svg___1TkF4 {
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}
.freeBox__pzVnq {
  display: flex;
  position: relative;
  flex-direction: row;
}
.freeBox__pzVnq > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  margin-left: calc(0px - 0px);
  width: calc(100% + 0px);
}
.freeBox__pzVnq > :global(.__wab_flex-container) > *,
.freeBox__pzVnq > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox__pzVnq > :global(.__wab_flex-container) > picture > img,
.freeBox__pzVnq
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: 0px;
}
.svg__kbdb {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 16px;
}
.text__uXdi {
  color: var(--token-t68i-MG5Bw);
}
.svg__sAuG {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 1em;
}
.viewGithubButton:global(.__wab_instance) {
  position: relative;
}
.svg__t3Mr3 {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 16px;
}
.text___43JWs {
  font-weight: 400;
}
.svg__rOsJo {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 16px;
}
.freeBox__xJMn {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  overflow: hidden;
  background: var(--token-iR8SeEwQZ);
  border-radius: 4px;
  border: 1px solid var(--token-hoA5qaM-91G);
}
.freeBoxview_status__xJMn2JIHl {
  display: none;
}
.freeBox__ndh {
  display: flex;
  position: relative;
  flex-direction: column;
  background: var(--token-p-rw5DRJTx);
  padding: 1rem;
  border-bottom: 1px solid var(--token-hoA5qaM-91G);
}
.freeBox__ndh > :global(.__wab_flex-container) {
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: calc(0px - 4px);
  height: calc(100% + 4px);
}
.freeBox__ndh > :global(.__wab_flex-container) > *,
.freeBox__ndh > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox__ndh > :global(.__wab_flex-container) > picture > img,
.freeBox__ndh
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-top: 4px;
}
.text__wYnuA {
  position: relative;
}
.learnMoreLink {
  position: relative;
}
.warning {
  display: flex;
  position: relative;
  flex-direction: column;
  width: 100%;
  background: var(--token-WsutfVbnQWpY);
  min-width: 0;
  border-radius: 4px;
  padding: 0.5rem;
}
.warning > :global(.__wab_flex-container) {
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  min-width: 0;
  margin-top: calc(0px - 8px);
  height: calc(100% + 8px);
}
.warning > :global(.__wab_flex-container) > *,
.warning > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.warning > :global(.__wab_flex-container) > picture > img,
.warning
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-top: 8px;
}
.text__jDjSw {
  position: relative;
  color: var(--token-N-GFU-C_NPxa);
}
.freeBox__pnvxx {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.slotTargetChangesSummary {
  font-size: 10px;
  color: var(--token-pCMcQv3xBc);
  line-height: 12px;
}
.reviewChangesButton:global(.__wab_instance) {
  position: relative;
}
.svg__fhc3P {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 16px;
}
.svg___6Rgi {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 1em;
}
.freeBox__usSde {
  display: flex;
  position: relative;
  flex-direction: column;
  padding: 1rem;
}
.freeBox__usSde > :global(.__wab_flex-container) {
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  margin-top: calc(0px - 8px);
  height: calc(100% + 8px);
}
.freeBox__usSde > :global(.__wab_flex-container) > *,
.freeBox__usSde > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox__usSde > :global(.__wab_flex-container) > picture > img,
.freeBox__usSde
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-top: 8px;
}
.freeBox___9NuEy {
  display: flex;
  flex-direction: row;
}
.freeBox___9NuEy > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-left: calc(0px - 8px);
  width: calc(100% + 8px);
}
.freeBox___9NuEy > :global(.__wab_flex-container) > *,
.freeBox___9NuEy > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox___9NuEy > :global(.__wab_flex-container) > picture > img,
.freeBox___9NuEy
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: 8px;
}
.freeBox__whjUu {
  display: flex;
  position: relative;
  flex-direction: row;
}
.freeBox__whjUu > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  margin-left: calc(0px - 8px);
  width: calc(100% + 8px);
}
.freeBox__whjUu > :global(.__wab_flex-container) > *,
.freeBox__whjUu > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox__whjUu > :global(.__wab_flex-container) > picture > img,
.freeBox__whjUu
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: 8px;
}
.svg__gwk1 {
  object-fit: cover;
  width: 1.5rem;
  height: 1.5rem;
  color: var(--token-UunsGa2Y3t3);
  flex-shrink: 0;
}
.domain {
  position: relative;
  color: var(--token-UunsGa2Y3t3);
  width: auto;
}
.retryButton:global(.__wab_instance) {
  position: relative;
}
.svg__x8NOs {
  display: flex;
  position: relative;
  object-fit: cover;
  height: 1em;
}
.svg__neiw {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 1em;
}
.removeGithubButton:global(.__wab_instance) {
  position: relative;
}
.svg__fdyfh {
  display: flex;
  position: relative;
  object-fit: cover;
  height: 1em;
}
.svg__qpbxf {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 1em;
}
.setupButton:global(.__wab_instance) {
  position: relative;
}
.svg__w5QLi {
  display: flex;
  position: relative;
  object-fit: cover;
  max-width: 100%;
  max-height: 100%;
  width: 16px;
  height: 16px;
}
.text__wx7K8 {
  height: auto;
}
.svg__dB19 {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 1em;
}
.freeBox__av8T2 {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
}
.freeBox__ePpd {
  display: flex;
  position: relative;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}
.text__q3S4W {
  position: relative;
  font-size: 11px;
  font-weight: 600;
  line-height: 34px;
}
.freeBox__sTqXg {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.svg__sgEkn {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 16px;
}
.text__flkoU {
  color: var(--token-t68i-MG5Bw);
}
.svg__oGuul {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 1em;
}
.showOptionsIconButton:global(.__wab_instance):global(.__wab_instance) {
  position: relative;
}
.freeBox__p2Og6 {
  display: flex;
  position: relative;
  flex-direction: column;
}
.freeBox__p2Og6 > :global(.__wab_flex-container) {
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  margin-top: calc(0px - 8px);
  height: calc(100% + 8px);
}
.freeBox__p2Og6 > :global(.__wab_flex-container) > *,
.freeBox__p2Og6 > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox__p2Og6 > :global(.__wab_flex-container) > picture > img,
.freeBox__p2Og6
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-top: 8px;
}
.freeBox__eKj17 {
  display: flex;
  position: relative;
}
.freeBox__eKj17 > :global(.__wab_flex-container) {
  margin-left: calc(0px - 8px);
  width: calc(100% + 8px);
}
.freeBox__eKj17 > :global(.__wab_flex-container) > *,
.freeBox__eKj17 > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox__eKj17 > :global(.__wab_flex-container) > picture > img,
.freeBox__eKj17
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: 8px;
}
.text__kz772 {
  position: relative;
  color: var(--token-t68i-MG5Bw);
  padding-top: 8px;
  padding-bottom: 8px;
  font-size: 11px;
  line-height: 16px;
  min-width: 33.33%;
}
.pushAs:global(.__wab_instance) {
  position: relative;
  width: 100%;
  min-width: 0;
}
.option__ysLtk:global(.__wab_instance) {
  position: relative;
}
.option__dq5TT:global(.__wab_instance) {
  position: relative;
}
.optionGroup__lo216:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.option__jOr50:global(.__wab_instance) {
  position: relative;
}
.option__ddNVd:global(.__wab_instance) {
  position: relative;
}
.optionGroup__zoj5V:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.option__fOoy:global(.__wab_instance) {
  position: relative;
}
.option__u7IR5:global(.__wab_instance) {
  position: relative;
}
.option__eQi5S:global(.__wab_instance) {
  position: relative;
}
.svg__qtaBp {
  position: relative;
  object-fit: cover;
  min-width: 16px;
  min-height: 16px;
  width: 16px;
  height: 16px;
}
.freeBox__fzd2 {
  display: flex;
  position: relative;
  flex-direction: column;
}
.freeBox__fzd2 > :global(.__wab_flex-container) {
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  margin-top: calc(0px - 8px);
  height: calc(100% + 8px);
}
.freeBox__fzd2 > :global(.__wab_flex-container) > *,
.freeBox__fzd2 > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox__fzd2 > :global(.__wab_flex-container) > picture > img,
.freeBox__fzd2
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-top: 8px;
}
.freeBox__iPgR7 {
  display: flex;
  position: relative;
}
.freeBox__iPgR7 > :global(.__wab_flex-container) {
  margin-left: calc(0px - 8px);
  width: calc(100% + 8px);
}
.freeBox__iPgR7 > :global(.__wab_flex-container) > *,
.freeBox__iPgR7 > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox__iPgR7 > :global(.__wab_flex-container) > picture > img,
.freeBox__iPgR7
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: 8px;
}
.text__fMrn6 {
  position: relative;
  color: var(--token-t68i-MG5Bw);
  padding-top: 8px;
  padding-bottom: 8px;
  font-size: 11px;
  line-height: 16px;
  min-width: 33.33%;
}
.title {
  position: relative;
  width: 100%;
  line-height: 14px;
  min-width: 0;
  border-radius: 4px;
  padding: 8px;
  border: 1px solid var(--token-0vHxN11ixM);
}
.freeBox__gakKe {
  display: flex;
  position: relative;
}
.freeBox__gakKe > :global(.__wab_flex-container) {
  margin-left: calc(0px - 8px);
  width: calc(100% + 8px);
}
.freeBox__gakKe > :global(.__wab_flex-container) > *,
.freeBox__gakKe > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox__gakKe > :global(.__wab_flex-container) > picture > img,
.freeBox__gakKe
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: 8px;
}
.text__eYbzm {
  position: relative;
  color: var(--token-t68i-MG5Bw);
  padding-top: 8px;
  padding-bottom: 8px;
  font-size: 11px;
  line-height: 16px;
  min-width: 33.33%;
}
.description {
  position: relative;
  width: 100%;
  line-height: 16px;
  min-width: 0;
  border-radius: 4px;
  padding: 8px;
  border: 1px solid var(--token-0vHxN11ixM);
}
.freeBox__daTyt {
  display: flex;
  position: relative;
  background: var(--token-iR8SeEwQZ);
  border-radius: 4px;
  padding: 0.5rem;
  border: 1px solid var(--token-hoA5qaM-91G);
}
.freeBoxview_status__daTyt2JIHl {
  display: flex;
}
.steps {
  display: flex;
  position: relative;
  flex-direction: column;
}
.steps > :global(.__wab_flex-container) {
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  margin-top: calc(0px - 8px);
  height: calc(100% + 8px);
}
.steps > :global(.__wab_flex-container) > *,
.steps > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.steps > :global(.__wab_flex-container) > picture > img,
.steps > :global(.__wab_flex-container) > :global(.__wab_slot) > picture > img {
  margin-top: 8px;
}
.gitJobStep__u7Srv:global(.__wab_instance) {
  position: relative;
}
.gitJobStep__y2Fyr:global(.__wab_instance) {
  position: relative;
}
.gitJobStep__kX6Oh:global(.__wab_instance) {
  position: relative;
}
.gitJobStep__mbLw0:global(.__wab_instance) {
  position: relative;
}
.gitJobStep__gOzj4:global(.__wab_instance) {
  position: relative;
}
.gitJobStep__nxWc0:global(.__wab_instance) {
  position: relative;
}
.text__kGJg {
  height: auto;
}
.gitJobStep___3Ntiv:global(.__wab_instance) {
  position: relative;
}
.gitJobStep___4D1Y:global(.__wab_instance) {
  position: relative;
}
.githubPagesDelayNotice {
  position: relative;
}
