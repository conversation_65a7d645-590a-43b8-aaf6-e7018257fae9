/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: fpbcKyXdMTvY59T4C5fjcC
// Component: oo-lLDZ5qnA

import * as React from "react";

import {
  Flex as Flex__,
  SingleChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "../../components/modals/plasmic/plasmic_kit_project_settings/plasmic_plasmic_kit_project_settings.module.css"; // plasmic-import: fpbcKyXdMTvY59T4C5fjcC/projectcss
import plasmic_plasmic_kit_design_system_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import sty from "./PlasmicSpinner.module.css"; // plasmic-import: oo-lLDZ5qnA/css

import Spinner1S200PxSvgIcon from "./icons/PlasmicIcon__Spinner1S200PxSvg"; // plasmic-import: mwpa_Gia6i/icon

createPlasmicElementProxy;

export type PlasmicSpinner__VariantMembers = {
  customDomain: "preliminaryError" | "added" | "invalid" | "loading";
};
export type PlasmicSpinner__VariantsArgs = {
  customDomain?: SingleChoiceArg<
    "preliminaryError" | "added" | "invalid" | "loading"
  >;
};
type VariantPropType = keyof PlasmicSpinner__VariantsArgs;
export const PlasmicSpinner__VariantProps = new Array<VariantPropType>(
  "customDomain"
);

export type PlasmicSpinner__ArgsType = {};
type ArgPropType = keyof PlasmicSpinner__ArgsType;
export const PlasmicSpinner__ArgProps = new Array<ArgPropType>();

export type PlasmicSpinner__OverridesType = {
  root?: Flex__<"svg">;
};

export interface DefaultSpinnerProps {
  customDomain?: SingleChoiceArg<
    "preliminaryError" | "added" | "invalid" | "loading"
  >;
  className?: string;
}

const $$ = {};

function PlasmicSpinner__RenderFunc(props: {
  variants: PlasmicSpinner__VariantsArgs;
  args: PlasmicSpinner__ArgsType;
  overrides: PlasmicSpinner__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "customDomain",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.customDomain,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <Spinner1S200PxSvgIcon
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        {
          [sty.rootcustomDomain_loading]: hasVariant(
            $state,
            "customDomain",
            "loading"
          ),
        }
      )}
      role={"img"}
    />
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "svg";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSpinner__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSpinner__VariantsArgs;
    args?: PlasmicSpinner__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSpinner__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicSpinner__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSpinner__ArgProps,
          internalVariantPropNames: PlasmicSpinner__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicSpinner__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicSpinner";
  } else {
    func.displayName = `PlasmicSpinner.${nodeName}`;
  }
  return func;
}

export const PlasmicSpinner = Object.assign(
  // Top-level PlasmicSpinner renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements

    // Metadata about props expected for PlasmicSpinner
    internalVariantProps: PlasmicSpinner__VariantProps,
    internalArgProps: PlasmicSpinner__ArgProps,
  }
);

export default PlasmicSpinner;
/* prettier-ignore-end */
