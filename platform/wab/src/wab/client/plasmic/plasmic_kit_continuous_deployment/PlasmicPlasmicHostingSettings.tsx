/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: fpbcKyXdMTvY59T4C5fjcC
// Component: aFapl-YUjv9

import * as React from "react";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicLink as PlasmicLink__,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  generateStateOnChangeProp,
  generateStateValueProp,
  hasVariant,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import ErrorFeedback from "../../components/github/ErrorFeedback"; // plasmic-import: 6ztKJ9-EG9Y/component
import DomainCard from "../../components/TopFrame/TopBar/DomainCard"; // plasmic-import: eqF_n5a1-6b/component
import Spinner from "../../components/TopFrame/TopBar/Spinner"; // plasmic-import: oo-lLDZ5qnA/component
import Button from "../../components/widgets/Button"; // plasmic-import: SEF-sRmSoqV5c/component
import IconButton from "../../components/widgets/IconButton"; // plasmic-import: LPry-TF4j22a/component
import Switch from "../../components/widgets/Switch"; // plasmic-import: b35JDgXpbiF/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "../../components/modals/plasmic/plasmic_kit_project_settings/plasmic_plasmic_kit_project_settings.module.css"; // plasmic-import: fpbcKyXdMTvY59T4C5fjcC/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_design_system_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import sty from "./PlasmicPlasmicHostingSettings.module.css"; // plasmic-import: aFapl-YUjv9/css

import ArrowRightSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ArrowRightSvg"; // plasmic-import: 9Jv8jb253/icon
import ChevronDownSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg"; // plasmic-import: xZrB9_0ir/icon
import EditSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__EditSvg"; // plasmic-import: _Qa2gdunG/icon
import ShareSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ShareSvg"; // plasmic-import: vRB2dtcKk/icon
import Trash2SvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__Trash2Svg"; // plasmic-import: nS4_I75qv/icon

createPlasmicElementProxy;

export type PlasmicPlasmicHostingSettings__VariantMembers = {
  customDomain: "preliminaryError" | "added" | "invalid" | "loading";
  subdomain: "success" | "error" | "invalid" | "loading";
};
export type PlasmicPlasmicHostingSettings__VariantsArgs = {
  customDomain?: SingleChoiceArg<
    "preliminaryError" | "added" | "invalid" | "loading"
  >;
  subdomain?: MultiChoiceArg<"success" | "error" | "invalid" | "loading">;
};
type VariantPropType = keyof PlasmicPlasmicHostingSettings__VariantsArgs;
export const PlasmicPlasmicHostingSettings__VariantProps =
  new Array<VariantPropType>("customDomain", "subdomain");

export type PlasmicPlasmicHostingSettings__ArgsType = {};
type ArgPropType = keyof PlasmicPlasmicHostingSettings__ArgsType;
export const PlasmicPlasmicHostingSettings__ArgProps = new Array<ArgPropType>();

export type PlasmicPlasmicHostingSettings__OverridesType = {
  root?: Flex__<"div">;
  subdomainForm?: Flex__<"form">;
  subdomainLabel?: Flex__<"div">;
  iconButton?: Flex__<typeof IconButton>;
  subdomainInput?: Flex__<"input">;
  subdomainSuffix?: Flex__<"div">;
  messageActions4?: Flex__<"div">;
  subdomainErrorFeedback?: Flex__<typeof ErrorFeedback>;
  domainErrorMessage7?: Flex__<"div">;
  saveSubdomainButton?: Flex__<typeof Button>;
  messageActions5?: Flex__<"div">;
  errorFeedback?: Flex__<typeof ErrorFeedback>;
  domainErrorMessage8?: Flex__<"div">;
  refreshButton7?: Flex__<typeof Button>;
  refreshButton8?: Flex__<typeof Button>;
  customDomainForm?: Flex__<"form">;
  customDomainInput?: Flex__<"input">;
  customDomainPreliminaryErrorFeedback?: Flex__<typeof ErrorFeedback>;
  domainErrorMessage9?: Flex__<"div">;
  addCustomDomainButton?: Flex__<typeof Button>;
  domainCard?: Flex__<typeof DomainCard>;
  paidFeaturesInfoText?: Flex__<"div">;
  link?: Flex__<"a">;
  upgradeNowLink?: Flex__<"a">;
  badgeForm?: Flex__<"div">;
  showBadge?: Flex__<typeof Switch>;
  faviconForm?: Flex__<"div">;
  faviconControlContainer?: Flex__<"div">;
};

export interface DefaultPlasmicHostingSettingsProps {
  customDomain?: SingleChoiceArg<
    "preliminaryError" | "added" | "invalid" | "loading"
  >;
  subdomain?: MultiChoiceArg<"success" | "error" | "invalid" | "loading">;
  className?: string;
}

const $$ = {};

function PlasmicPlasmicHostingSettings__RenderFunc(props: {
  variants: PlasmicPlasmicHostingSettings__VariantsArgs;
  args: PlasmicPlasmicHostingSettings__ArgsType;
  overrides: PlasmicPlasmicHostingSettings__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "customDomain",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.customDomain,
      },
      {
        path: "subdomain",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.subdomain,
      },
      {
        path: "showBadge.isChecked",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        {
          [sty.rootcustomDomain_preliminaryError]: hasVariant(
            $state,
            "customDomain",
            "preliminaryError"
          ),
        }
      )}
    >
      <Stack__
        as={"div"}
        hasGap={true}
        className={classNames(projectcss.all, sty.freeBox__spuop, {
          [sty.freeBoxcustomDomain_added__spuopqFnX7]: hasVariant(
            $state,
            "customDomain",
            "added"
          ),
          [sty.freeBoxcustomDomain_invalid__spuopvm3Gj]: hasVariant(
            $state,
            "customDomain",
            "invalid"
          ),
          [sty.freeBoxcustomDomain_loading__spuopO257B]: hasVariant(
            $state,
            "customDomain",
            "loading"
          ),
          [sty.freeBoxcustomDomain_preliminaryError__spuop7Zc9L]: hasVariant(
            $state,
            "customDomain",
            "preliminaryError"
          ),
          [sty.freeBoxsubdomain_loading__spuop2Axxv]: hasVariant(
            $state,
            "subdomain",
            "loading"
          ),
        })}
      >
        <Stack__
          as={"form"}
          data-plasmic-name={"subdomainForm"}
          data-plasmic-override={overrides.subdomainForm}
          hasGap={true}
          className={classNames(projectcss.all, sty.subdomainForm, {
            [sty.subdomainFormcustomDomain_added]: hasVariant(
              $state,
              "customDomain",
              "added"
            ),
            [sty.subdomainFormcustomDomain_preliminaryError]: hasVariant(
              $state,
              "customDomain",
              "preliminaryError"
            ),
            [sty.subdomainFormsubdomain_error]: hasVariant(
              $state,
              "subdomain",
              "error"
            ),
          })}
        >
          <Stack__
            as={"div"}
            hasGap={true}
            className={classNames(projectcss.all, sty.freeBox__vElBp, {
              [sty.freeBoxcustomDomain_preliminaryError__vElBp7Zc9L]:
                hasVariant($state, "customDomain", "preliminaryError"),
              [sty.freeBoxsubdomain_error__vElBpJZbv6]: hasVariant(
                $state,
                "subdomain",
                "error"
              ),
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__r1Gc,
                {
                  [sty.textsubdomain_error__r1GcJZbv6]: hasVariant(
                    $state,
                    "subdomain",
                    "error"
                  ),
                }
              )}
            >
              {"Subdomain"}
            </div>
            {(hasVariant($state, "subdomain", "success") ? true : false) ? (
              <div
                data-plasmic-name={"subdomainLabel"}
                data-plasmic-override={overrides.subdomainLabel}
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.subdomainLabel,
                  {
                    [sty.subdomainLabelsubdomain_success]: hasVariant(
                      $state,
                      "subdomain",
                      "success"
                    ),
                  }
                )}
              >
                {"foobar.plasmic.website"}
              </div>
            ) : null}
            {(hasVariant($state, "subdomain", "success") ? true : false) ? (
              <IconButton
                data-plasmic-name={"iconButton"}
                data-plasmic-override={overrides.iconButton}
                className={classNames("__wab_instance", sty.iconButton, {
                  [sty.iconButtonsubdomain_success]: hasVariant(
                    $state,
                    "subdomain",
                    "success"
                  ),
                })}
                withBackgroundHover={true}
              >
                <ShareSvgIcon
                  className={classNames(projectcss.all, sty.svg__itTbv)}
                  role={"img"}
                />
              </IconButton>
            ) : null}
            <div
              className={classNames(projectcss.all, sty.freeBox___0TBzB, {
                [sty.freeBoxsubdomain_success___0TBzB2P0Cq]: hasVariant(
                  $state,
                  "subdomain",
                  "success"
                ),
              })}
            >
              <input
                data-plasmic-name={"subdomainInput"}
                data-plasmic-override={overrides.subdomainInput}
                className={classNames(
                  projectcss.all,
                  projectcss.input,
                  sty.subdomainInput,
                  {
                    [sty.subdomainInputcustomDomain_added]: hasVariant(
                      $state,
                      "customDomain",
                      "added"
                    ),
                    [sty.subdomainInputcustomDomain_preliminaryError]:
                      hasVariant($state, "customDomain", "preliminaryError"),
                    [sty.subdomainInputsubdomain_loading]: hasVariant(
                      $state,
                      "subdomain",
                      "loading"
                    ),
                    [sty.subdomainInputsubdomain_success]: hasVariant(
                      $state,
                      "subdomain",
                      "success"
                    ),
                  }
                )}
                placeholder={"my-subdomain"}
                ref={(ref) => {
                  $refs["subdomainInput"] = ref;
                }}
                size={1}
                type={"text"}
                value={""}
              />

              <div
                data-plasmic-name={"subdomainSuffix"}
                data-plasmic-override={overrides.subdomainSuffix}
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.subdomainSuffix,
                  {
                    [sty.subdomainSuffixsubdomain_success]: hasVariant(
                      $state,
                      "subdomain",
                      "success"
                    ),
                  }
                )}
              >
                {".plasmic.website"}
              </div>
            </div>
          </Stack__>
          {(
            hasVariant($state, "subdomain", "error")
              ? true
              : hasVariant($state, "customDomain", "preliminaryError")
              ? true
              : false
          ) ? (
            <Stack__
              as={"div"}
              data-plasmic-name={"messageActions4"}
              data-plasmic-override={overrides.messageActions4}
              hasGap={true}
              className={classNames(projectcss.all, sty.messageActions4, {
                [sty.messageActions4customDomain_preliminaryError]: hasVariant(
                  $state,
                  "customDomain",
                  "preliminaryError"
                ),
                [sty.messageActions4subdomain_error]: hasVariant(
                  $state,
                  "subdomain",
                  "error"
                ),
              })}
            >
              <ErrorFeedback
                data-plasmic-name={"subdomainErrorFeedback"}
                data-plasmic-override={overrides.subdomainErrorFeedback}
                className={classNames(
                  "__wab_instance",
                  sty.subdomainErrorFeedback,
                  {
                    [sty.subdomainErrorFeedbackcustomDomain_preliminaryError]:
                      hasVariant($state, "customDomain", "preliminaryError"),
                  }
                )}
              >
                <div
                  data-plasmic-name={"domainErrorMessage7"}
                  data-plasmic-override={overrides.domainErrorMessage7}
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.domainErrorMessage7
                  )}
                >
                  {"{yoursite.com} is already taken."}
                </div>
              </ErrorFeedback>
            </Stack__>
          ) : null}
          <Stack__
            as={"div"}
            hasGap={true}
            className={classNames(projectcss.all, sty.freeBox__j9GqN, {
              [sty.freeBoxcustomDomain_loading__j9GqNo257B]: hasVariant(
                $state,
                "customDomain",
                "loading"
              ),
              [sty.freeBoxcustomDomain_preliminaryError__j9GqN7Zc9L]:
                hasVariant($state, "customDomain", "preliminaryError"),
              [sty.freeBoxsubdomain_error__j9GqNjZbv6]: hasVariant(
                $state,
                "subdomain",
                "error"
              ),
              [sty.freeBoxsubdomain_invalid__j9GqNBxTlI]: hasVariant(
                $state,
                "subdomain",
                "invalid"
              ),
              [sty.freeBoxsubdomain_loading__j9GqN2Axxv]: hasVariant(
                $state,
                "subdomain",
                "loading"
              ),
              [sty.freeBoxsubdomain_success__j9GqN2P0Cq]: hasVariant(
                $state,
                "subdomain",
                "success"
              ),
            })}
          >
            {(hasVariant($state, "subdomain", "loading") ? true : false) ? (
              <Spinner
                className={classNames("__wab_instance", sty.spinner__cJdh, {
                  [sty.spinnercustomDomain_loading__cJdhO257B]: hasVariant(
                    $state,
                    "customDomain",
                    "loading"
                  ),
                  [sty.spinnersubdomain_loading__cJdh2Axxv]: hasVariant(
                    $state,
                    "subdomain",
                    "loading"
                  ),
                })}
                customDomain={
                  hasVariant($state, "customDomain", "loading")
                    ? "loading"
                    : undefined
                }
              />
            ) : null}
            <Button
              data-plasmic-name={"saveSubdomainButton"}
              data-plasmic-override={overrides.saveSubdomainButton}
              caption={"Caption"}
              disabled={
                hasVariant($state, "subdomain", "loading")
                  ? true
                  : hasVariant($state, "subdomain", "invalid")
                  ? true
                  : undefined
              }
              endIcon={
                <ChevronDownSvgIcon
                  className={classNames(projectcss.all, sty.svg__zyJq4)}
                  role={"img"}
                />
              }
              size={"wide"}
              startIcon={
                <ArrowRightSvgIcon
                  className={classNames(projectcss.all, sty.svg__wqrFn)}
                  role={"img"}
                />
              }
              type={["primary"]}
            >
              {"Save subdomain"}
            </Button>
          </Stack__>
          {(hasVariant($state, "subdomain", "success") ? true : false) ? (
            <Stack__
              as={"div"}
              data-plasmic-name={"messageActions5"}
              data-plasmic-override={overrides.messageActions5}
              hasGap={true}
              className={classNames(projectcss.all, sty.messageActions5, {
                [sty.messageActions5subdomain_success]: hasVariant(
                  $state,
                  "subdomain",
                  "success"
                ),
              })}
            >
              {(
                hasVariant($state, "customDomain", "preliminaryError")
                  ? true
                  : false
              ) ? (
                <ErrorFeedback
                  data-plasmic-name={"errorFeedback"}
                  data-plasmic-override={overrides.errorFeedback}
                  className={classNames("__wab_instance", sty.errorFeedback, {
                    [sty.errorFeedbackcustomDomain_preliminaryError]:
                      hasVariant($state, "customDomain", "preliminaryError"),
                  })}
                >
                  <div
                    data-plasmic-name={"domainErrorMessage8"}
                    data-plasmic-override={overrides.domainErrorMessage8}
                    className={classNames(
                      projectcss.all,
                      projectcss.__wab_text,
                      sty.domainErrorMessage8
                    )}
                  >
                    <React.Fragment>
                      <React.Fragment>
                        {"{yoursite.com} is already owned by another team. "}
                      </React.Fragment>
                      <span
                        className={"plasmic_default__all plasmic_default__span"}
                        style={{ textDecorationLine: "underline" }}
                      >
                        {"Click here to request access"}
                      </span>
                      <React.Fragment>{"."}</React.Fragment>
                    </React.Fragment>
                  </div>
                </ErrorFeedback>
              ) : null}
              <Stack__
                as={"div"}
                hasGap={true}
                className={classNames(projectcss.all, sty.freeBox__av12)}
              >
                <Button
                  data-plasmic-name={"refreshButton7"}
                  data-plasmic-override={overrides.refreshButton7}
                  caption={"Caption"}
                  endIcon={
                    <ChevronDownSvgIcon
                      className={classNames(projectcss.all, sty.svg__vFRdg)}
                      role={"img"}
                    />
                  }
                  size={"wide"}
                  startIcon={
                    <EditSvgIcon
                      className={classNames(projectcss.all, sty.svg__onUd8)}
                      role={"img"}
                    />
                  }
                  type={["clear"]}
                  withIcons={["startIcon"]}
                >
                  {"Edit"}
                </Button>
                <Button
                  data-plasmic-name={"refreshButton8"}
                  data-plasmic-override={overrides.refreshButton8}
                  caption={"Caption"}
                  color={"red"}
                  endIcon={
                    <ChevronDownSvgIcon
                      className={classNames(projectcss.all, sty.svg__aAjVi)}
                      role={"img"}
                    />
                  }
                  size={"wide"}
                  startIcon={
                    <Trash2SvgIcon
                      className={classNames(projectcss.all, sty.svg__by8Za)}
                      role={"img"}
                    />
                  }
                  type={["clear"]}
                  withIcons={["startIcon"]}
                >
                  <div
                    className={classNames(
                      projectcss.all,
                      projectcss.__wab_text,
                      sty.text__iHfQr
                    )}
                  >
                    {"Remove"}
                  </div>
                </Button>
              </Stack__>
            </Stack__>
          ) : null}
        </Stack__>
        <div
          className={classNames(projectcss.all, sty.freeBox__nRjqO, {
            [sty.freeBoxcustomDomain_preliminaryError__nRjqO7Zc9L]: hasVariant(
              $state,
              "customDomain",
              "preliminaryError"
            ),
          })}
        />

        <Stack__
          as={"form"}
          data-plasmic-name={"customDomainForm"}
          data-plasmic-override={overrides.customDomainForm}
          hasGap={true}
          className={classNames(projectcss.all, sty.customDomainForm, {
            [sty.customDomainFormcustomDomain_added]: hasVariant(
              $state,
              "customDomain",
              "added"
            ),
            [sty.customDomainFormcustomDomain_invalid]: hasVariant(
              $state,
              "customDomain",
              "invalid"
            ),
            [sty.customDomainFormcustomDomain_loading]: hasVariant(
              $state,
              "customDomain",
              "loading"
            ),
            [sty.customDomainFormcustomDomain_preliminaryError]: hasVariant(
              $state,
              "customDomain",
              "preliminaryError"
            ),
          })}
        >
          <Stack__
            as={"div"}
            hasGap={true}
            className={classNames(projectcss.all, sty.freeBox__usp8U, {
              [sty.freeBoxcustomDomain_preliminaryError__usp8U7Zc9L]:
                hasVariant($state, "customDomain", "preliminaryError"),
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text___0D1Dl,
                {
                  [sty.textcustomDomain_preliminaryError___0D1Dl7Zc9L]:
                    hasVariant($state, "customDomain", "preliminaryError"),
                }
              )}
            >
              {"Custom domain"}
            </div>
            <input
              data-plasmic-name={"customDomainInput"}
              data-plasmic-override={overrides.customDomainInput}
              className={classNames(
                projectcss.all,
                projectcss.input,
                sty.customDomainInput,
                {
                  [sty.customDomainInputcustomDomain_preliminaryError]:
                    hasVariant($state, "customDomain", "preliminaryError"),
                  [sty.customDomainInputsubdomain_error]: hasVariant(
                    $state,
                    "subdomain",
                    "error"
                  ),
                }
              )}
              placeholder={"my-domain.com"}
              ref={(ref) => {
                $refs["customDomainInput"] = ref;
              }}
              size={1}
              type={"text"}
              value={""}
            />
          </Stack__>
          {(
            hasVariant($state, "customDomain", "preliminaryError")
              ? true
              : false
          ) ? (
            <ErrorFeedback
              data-plasmic-name={"customDomainPreliminaryErrorFeedback"}
              data-plasmic-override={
                overrides.customDomainPreliminaryErrorFeedback
              }
              className={classNames(
                "__wab_instance",
                sty.customDomainPreliminaryErrorFeedback,
                {
                  [sty.customDomainPreliminaryErrorFeedbackcustomDomain_preliminaryError]:
                    hasVariant($state, "customDomain", "preliminaryError"),
                }
              )}
            >
              <div
                data-plasmic-name={"domainErrorMessage9"}
                data-plasmic-override={overrides.domainErrorMessage9}
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.domainErrorMessage9
                )}
              >
                <React.Fragment>
                  <React.Fragment>
                    {"{yoursite.com} is already owned by another team. "}
                  </React.Fragment>
                  <span
                    className={"plasmic_default__all plasmic_default__span"}
                    style={{ textDecorationLine: "underline" }}
                  >
                    {"Click here to request access"}
                  </span>
                  <React.Fragment>{"."}</React.Fragment>
                </React.Fragment>
              </div>
            </ErrorFeedback>
          ) : null}
          <Stack__
            as={"div"}
            hasGap={true}
            className={classNames(projectcss.all, sty.freeBox__m52, {
              [sty.freeBoxcustomDomain_invalid__m52Vm3Gj]: hasVariant(
                $state,
                "customDomain",
                "invalid"
              ),
              [sty.freeBoxcustomDomain_loading__m52O257B]: hasVariant(
                $state,
                "customDomain",
                "loading"
              ),
              [sty.freeBoxcustomDomain_preliminaryError__m527Zc9L]: hasVariant(
                $state,
                "customDomain",
                "preliminaryError"
              ),
              [sty.freeBoxsubdomain_error__m52JZbv6]: hasVariant(
                $state,
                "subdomain",
                "error"
              ),
              [sty.freeBoxsubdomain_loading__m522Axxv]: hasVariant(
                $state,
                "subdomain",
                "loading"
              ),
            })}
          >
            {(hasVariant($state, "customDomain", "loading") ? true : false) ? (
              <Spinner
                className={classNames("__wab_instance", sty.spinner__r8GS, {
                  [sty.spinnercustomDomain_loading__r8GSo257B]: hasVariant(
                    $state,
                    "customDomain",
                    "loading"
                  ),
                })}
                customDomain={
                  hasVariant($state, "customDomain", "loading")
                    ? "loading"
                    : undefined
                }
              />
            ) : null}
            <Button
              data-plasmic-name={"addCustomDomainButton"}
              data-plasmic-override={overrides.addCustomDomainButton}
              caption={"Caption"}
              disabled={
                hasVariant($state, "customDomain", "loading")
                  ? true
                  : hasVariant($state, "customDomain", "invalid")
                  ? true
                  : undefined
              }
              endIcon={
                <ChevronDownSvgIcon
                  className={classNames(projectcss.all, sty.svg__v6ZT7)}
                  role={"img"}
                />
              }
              size={"wide"}
              startIcon={
                <ArrowRightSvgIcon
                  className={classNames(projectcss.all, sty.svg___5Gb8V)}
                  role={"img"}
                />
              }
              type={["primary"]}
            >
              {"Add custom domain"}
            </Button>
          </Stack__>
        </Stack__>
        {(hasVariant($state, "customDomain", "added") ? true : false) ? (
          <DomainCard
            data-plasmic-name={"domainCard"}
            data-plasmic-override={overrides.domainCard}
          />
        ) : null}
        <div
          className={classNames(projectcss.all, sty.freeBox__nUd8P, {
            [sty.freeBoxcustomDomain_preliminaryError__nUd8P7Zc9L]: hasVariant(
              $state,
              "customDomain",
              "preliminaryError"
            ),
          })}
        />

        <Stack__
          as={"div"}
          hasGap={true}
          className={classNames(projectcss.all, sty.freeBox__a7Yu5)}
        >
          <Stack__
            as={"div"}
            data-plasmic-name={"paidFeaturesInfoText"}
            data-plasmic-override={overrides.paidFeaturesInfoText}
            hasGap={true}
            className={classNames(projectcss.all, sty.paidFeaturesInfoText)}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__arLZl
              )}
            >
              <React.Fragment>
                <React.Fragment>
                  {"Below features are exclusive to paid plans. "}
                </React.Fragment>
                {
                  <PlasmicLink__
                    data-plasmic-name={"link"}
                    data-plasmic-override={overrides.link}
                    className={classNames(
                      projectcss.all,
                      projectcss.a,
                      projectcss.__wab_text,
                      projectcss.plasmic_default__inline,
                      sty.link
                    )}
                    href={"https://www.plasmic.app/pricing#pricing-table"}
                    platform={"react"}
                    target={"_blank"}
                  >
                    <React.Fragment>
                      <span
                        className={"plasmic_default__all plasmic_default__span"}
                        style={{ textDecorationLine: "underline" }}
                      >
                        {"Learn more"}
                      </span>
                    </React.Fragment>
                  </PlasmicLink__>
                }
                <React.Fragment>{" or "}</React.Fragment>
                {
                  <PlasmicLink__
                    data-plasmic-name={"upgradeNowLink"}
                    data-plasmic-override={overrides.upgradeNowLink}
                    className={classNames(
                      projectcss.all,
                      projectcss.a,
                      projectcss.__wab_text,
                      projectcss.plasmic_default__inline,
                      sty.upgradeNowLink
                    )}
                    platform={"react"}
                  >
                    <React.Fragment>
                      <span
                        className={"plasmic_default__all plasmic_default__span"}
                        style={{ textDecorationLine: "underline" }}
                      >
                        {"upgrade now"}
                      </span>
                    </React.Fragment>
                  </PlasmicLink__>
                }
                <React.Fragment>{"."}</React.Fragment>
              </React.Fragment>
            </div>
          </Stack__>
          <Stack__
            as={"div"}
            data-plasmic-name={"badgeForm"}
            data-plasmic-override={overrides.badgeForm}
            hasGap={true}
            className={classNames(projectcss.all, sty.badgeForm, {
              [sty.badgeFormcustomDomain_added]: hasVariant(
                $state,
                "customDomain",
                "added"
              ),
              [sty.badgeFormcustomDomain_preliminaryError]: hasVariant(
                $state,
                "customDomain",
                "preliminaryError"
              ),
              [sty.badgeFormsubdomain_error]: hasVariant(
                $state,
                "subdomain",
                "error"
              ),
              [sty.badgeFormsubdomain_success]: hasVariant(
                $state,
                "subdomain",
                "success"
              ),
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__t6S3E,
                {
                  [sty.textcustomDomain_added__t6S3EqFnX7]: hasVariant(
                    $state,
                    "customDomain",
                    "added"
                  ),
                  [sty.textsubdomain_error__t6S3EJZbv6]: hasVariant(
                    $state,
                    "subdomain",
                    "error"
                  ),
                }
              )}
            >
              {'Show "Made in Plasmic" badge?'}
            </div>
            <Switch
              data-plasmic-name={"showBadge"}
              data-plasmic-override={overrides.showBadge}
              children={null}
              className={classNames("__wab_instance", sty.showBadge)}
              isChecked={
                generateStateValueProp($state, ["showBadge", "isChecked"]) ??
                false
              }
              onChange={async (...eventArgs: any) => {
                ((...eventArgs) => {
                  generateStateOnChangeProp($state, ["showBadge", "isChecked"])(
                    eventArgs[0]
                  );
                }).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          </Stack__>
          <Stack__
            as={"div"}
            data-plasmic-name={"faviconForm"}
            data-plasmic-override={overrides.faviconForm}
            hasGap={true}
            className={classNames(projectcss.all, sty.faviconForm, {
              [sty.faviconFormcustomDomain_added]: hasVariant(
                $state,
                "customDomain",
                "added"
              ),
              [sty.faviconFormcustomDomain_preliminaryError]: hasVariant(
                $state,
                "customDomain",
                "preliminaryError"
              ),
              [sty.faviconFormsubdomain_error]: hasVariant(
                $state,
                "subdomain",
                "error"
              ),
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__weJ2D,
                {
                  [sty.textcustomDomain_added__weJ2DqFnX7]: hasVariant(
                    $state,
                    "customDomain",
                    "added"
                  ),
                  [sty.textsubdomain_error__weJ2DjZbv6]: hasVariant(
                    $state,
                    "subdomain",
                    "error"
                  ),
                }
              )}
            >
              {"Favicon"}
            </div>
            <div
              data-plasmic-name={"faviconControlContainer"}
              data-plasmic-override={overrides.faviconControlContainer}
              className={classNames(
                projectcss.all,
                sty.faviconControlContainer,
                {
                  [sty.faviconControlContainersubdomain_success]: hasVariant(
                    $state,
                    "subdomain",
                    "success"
                  ),
                }
              )}
            >
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text___6BGaf
                )}
              >
                {"hi"}
              </div>
            </div>
          </Stack__>
        </Stack__>
      </Stack__>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "subdomainForm",
    "subdomainLabel",
    "iconButton",
    "subdomainInput",
    "subdomainSuffix",
    "messageActions4",
    "subdomainErrorFeedback",
    "domainErrorMessage7",
    "saveSubdomainButton",
    "messageActions5",
    "errorFeedback",
    "domainErrorMessage8",
    "refreshButton7",
    "refreshButton8",
    "customDomainForm",
    "customDomainInput",
    "customDomainPreliminaryErrorFeedback",
    "domainErrorMessage9",
    "addCustomDomainButton",
    "domainCard",
    "paidFeaturesInfoText",
    "link",
    "upgradeNowLink",
    "badgeForm",
    "showBadge",
    "faviconForm",
    "faviconControlContainer",
  ],
  subdomainForm: [
    "subdomainForm",
    "subdomainLabel",
    "iconButton",
    "subdomainInput",
    "subdomainSuffix",
    "messageActions4",
    "subdomainErrorFeedback",
    "domainErrorMessage7",
    "saveSubdomainButton",
    "messageActions5",
    "errorFeedback",
    "domainErrorMessage8",
    "refreshButton7",
    "refreshButton8",
  ],
  subdomainLabel: ["subdomainLabel"],
  iconButton: ["iconButton"],
  subdomainInput: ["subdomainInput"],
  subdomainSuffix: ["subdomainSuffix"],
  messageActions4: [
    "messageActions4",
    "subdomainErrorFeedback",
    "domainErrorMessage7",
  ],
  subdomainErrorFeedback: ["subdomainErrorFeedback", "domainErrorMessage7"],
  domainErrorMessage7: ["domainErrorMessage7"],
  saveSubdomainButton: ["saveSubdomainButton"],
  messageActions5: [
    "messageActions5",
    "errorFeedback",
    "domainErrorMessage8",
    "refreshButton7",
    "refreshButton8",
  ],
  errorFeedback: ["errorFeedback", "domainErrorMessage8"],
  domainErrorMessage8: ["domainErrorMessage8"],
  refreshButton7: ["refreshButton7"],
  refreshButton8: ["refreshButton8"],
  customDomainForm: [
    "customDomainForm",
    "customDomainInput",
    "customDomainPreliminaryErrorFeedback",
    "domainErrorMessage9",
    "addCustomDomainButton",
  ],
  customDomainInput: ["customDomainInput"],
  customDomainPreliminaryErrorFeedback: [
    "customDomainPreliminaryErrorFeedback",
    "domainErrorMessage9",
  ],
  domainErrorMessage9: ["domainErrorMessage9"],
  addCustomDomainButton: ["addCustomDomainButton"],
  domainCard: ["domainCard"],
  paidFeaturesInfoText: ["paidFeaturesInfoText", "link", "upgradeNowLink"],
  link: ["link"],
  upgradeNowLink: ["upgradeNowLink"],
  badgeForm: ["badgeForm", "showBadge"],
  showBadge: ["showBadge"],
  faviconForm: ["faviconForm", "faviconControlContainer"],
  faviconControlContainer: ["faviconControlContainer"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  subdomainForm: "form";
  subdomainLabel: "div";
  iconButton: typeof IconButton;
  subdomainInput: "input";
  subdomainSuffix: "div";
  messageActions4: "div";
  subdomainErrorFeedback: typeof ErrorFeedback;
  domainErrorMessage7: "div";
  saveSubdomainButton: typeof Button;
  messageActions5: "div";
  errorFeedback: typeof ErrorFeedback;
  domainErrorMessage8: "div";
  refreshButton7: typeof Button;
  refreshButton8: typeof Button;
  customDomainForm: "form";
  customDomainInput: "input";
  customDomainPreliminaryErrorFeedback: typeof ErrorFeedback;
  domainErrorMessage9: "div";
  addCustomDomainButton: typeof Button;
  domainCard: typeof DomainCard;
  paidFeaturesInfoText: "div";
  link: "a";
  upgradeNowLink: "a";
  badgeForm: "div";
  showBadge: typeof Switch;
  faviconForm: "div";
  faviconControlContainer: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicPlasmicHostingSettings__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicPlasmicHostingSettings__VariantsArgs;
    args?: PlasmicPlasmicHostingSettings__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicPlasmicHostingSettings__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicPlasmicHostingSettings__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicPlasmicHostingSettings__ArgProps,
          internalVariantPropNames: PlasmicPlasmicHostingSettings__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicPlasmicHostingSettings__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicPlasmicHostingSettings";
  } else {
    func.displayName = `PlasmicPlasmicHostingSettings.${nodeName}`;
  }
  return func;
}

export const PlasmicPlasmicHostingSettings = Object.assign(
  // Top-level PlasmicPlasmicHostingSettings renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    subdomainForm: makeNodeComponent("subdomainForm"),
    subdomainLabel: makeNodeComponent("subdomainLabel"),
    iconButton: makeNodeComponent("iconButton"),
    subdomainInput: makeNodeComponent("subdomainInput"),
    subdomainSuffix: makeNodeComponent("subdomainSuffix"),
    messageActions4: makeNodeComponent("messageActions4"),
    subdomainErrorFeedback: makeNodeComponent("subdomainErrorFeedback"),
    domainErrorMessage7: makeNodeComponent("domainErrorMessage7"),
    saveSubdomainButton: makeNodeComponent("saveSubdomainButton"),
    messageActions5: makeNodeComponent("messageActions5"),
    errorFeedback: makeNodeComponent("errorFeedback"),
    domainErrorMessage8: makeNodeComponent("domainErrorMessage8"),
    refreshButton7: makeNodeComponent("refreshButton7"),
    refreshButton8: makeNodeComponent("refreshButton8"),
    customDomainForm: makeNodeComponent("customDomainForm"),
    customDomainInput: makeNodeComponent("customDomainInput"),
    customDomainPreliminaryErrorFeedback: makeNodeComponent(
      "customDomainPreliminaryErrorFeedback"
    ),
    domainErrorMessage9: makeNodeComponent("domainErrorMessage9"),
    addCustomDomainButton: makeNodeComponent("addCustomDomainButton"),
    domainCard: makeNodeComponent("domainCard"),
    paidFeaturesInfoText: makeNodeComponent("paidFeaturesInfoText"),
    link: makeNodeComponent("link"),
    upgradeNowLink: makeNodeComponent("upgradeNowLink"),
    badgeForm: makeNodeComponent("badgeForm"),
    showBadge: makeNodeComponent("showBadge"),
    faviconForm: makeNodeComponent("faviconForm"),
    faviconControlContainer: makeNodeComponent("faviconControlContainer"),

    // Metadata about props expected for PlasmicPlasmicHostingSettings
    internalVariantProps: PlasmicPlasmicHostingSettings__VariantProps,
    internalArgProps: PlasmicPlasmicHostingSettings__ArgProps,
  }
);

export default PlasmicPlasmicHostingSettings;
/* prettier-ignore-end */
