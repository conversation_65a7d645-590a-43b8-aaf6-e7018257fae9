/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: fpbcKyXdMTvY59T4C5fjcC
// Component: aeDQsBfp-eA

import * as React from "react";

import {
  Flex as Flex__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  generateStateOnChangeProp,
  generateStateValueProp,
  hasVariant,
  renderPlasmicSlot,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import Button from "../../components/widgets/Button"; // plasmic-import: SEF-sRmSoqV5c/component
import Checkbox from "../../components/widgets/Checkbox"; // plasmic-import: W-rO7NZqPjZ/component
import ExpandButton from "../../components/widgets/ExpandButton"; // plasmic-import: JJhv0MV9DH/component
import GitJobStep from "../../components/widgets/GitJobStep"; // plasmic-import: JzpEJAQTjPX/component
import Select from "../../components/widgets/Select"; // plasmic-import: j_4IQyOWK2b/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "../../components/modals/plasmic/plasmic_kit_project_settings/plasmic_plasmic_kit_project_settings.module.css"; // plasmic-import: fpbcKyXdMTvY59T4C5fjcC/projectcss
import plasmic_plasmic_kit_design_system_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import sty from "./PlasmicSubsectionPlasmicHosting.module.css"; // plasmic-import: aeDQsBfp-eA/css

import CloseIcon from "../plasmic_kit/PlasmicIcon__Close"; // plasmic-import: hy7vKrgdAZwW4/icon
import OpenIcon from "../plasmic_kit/PlasmicIcon__Open"; // plasmic-import: 7D0GDLdF72udM/icon
import TriangleBottomIcon from "../plasmic_kit/PlasmicIcon__TriangleBottom"; // plasmic-import: A8NQUZ7Lg1OHO/icon
import ArrowRightSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ArrowRightSvg"; // plasmic-import: 9Jv8jb253/icon
import ChevronDownSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg"; // plasmic-import: xZrB9_0ir/icon
import GlobeSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__GlobeSvg"; // plasmic-import: gcxY0Mwvj/icon
import PlusSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__PlusSvg"; // plasmic-import: sQKgd2GNr/icon
import image2DAmIiITwd from "./images/image2.svg"; // plasmic-import: dAMIiI_twd/picture

createPlasmicElementProxy;

export type PlasmicSubsectionPlasmicHosting__VariantMembers = {
  collapse: "collapse";
  view: "setup" | "status";
};
export type PlasmicSubsectionPlasmicHosting__VariantsArgs = {
  collapse?: SingleBooleanChoiceArg<"collapse">;
  view?: SingleChoiceArg<"setup" | "status">;
};
type VariantPropType = keyof PlasmicSubsectionPlasmicHosting__VariantsArgs;
export const PlasmicSubsectionPlasmicHosting__VariantProps =
  new Array<VariantPropType>("collapse", "view");

export type PlasmicSubsectionPlasmicHosting__ArgsType = {
  changesSummary?: React.ReactNode;
};
type ArgPropType = keyof PlasmicSubsectionPlasmicHosting__ArgsType;
export const PlasmicSubsectionPlasmicHosting__ArgProps = new Array<ArgPropType>(
  "changesSummary"
);

export type PlasmicSubsectionPlasmicHosting__OverridesType = {
  root?: Flex__<"div">;
  checkbox?: Flex__<typeof Checkbox>;
  img?: Flex__<typeof PlasmicImg__>;
  removeButton?: Flex__<typeof Button>;
  viewGithubButton?: Flex__<typeof Button>;
  learnMoreLink?: Flex__<"a">;
  warning?: Flex__<"div">;
  reviewChangesButton?: Flex__<typeof Button>;
  domain?: Flex__<"a">;
  retryButton?: Flex__<typeof Button>;
  removeGithubButton?: Flex__<typeof Button>;
  setupButton?: Flex__<typeof Button>;
  showOptionsButton?: Flex__<typeof Button>;
  showOptionsIconButton?: Flex__<typeof ExpandButton>;
  pushAs?: Flex__<typeof Select>;
  title?: Flex__<"input">;
  description?: Flex__<"textarea">;
  steps?: Flex__<"ul">;
  githubPagesDelayNotice?: Flex__<"div">;
};

export interface DefaultSubsectionPlasmicHostingProps {
  changesSummary?: React.ReactNode;
  collapse?: SingleBooleanChoiceArg<"collapse">;
  view?: SingleChoiceArg<"setup" | "status">;
  className?: string;
}

const $$ = {};

function PlasmicSubsectionPlasmicHosting__RenderFunc(props: {
  variants: PlasmicSubsectionPlasmicHosting__VariantsArgs;
  args: PlasmicSubsectionPlasmicHosting__ArgsType;
  overrides: PlasmicSubsectionPlasmicHosting__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "collapse",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.collapse,
      },
      {
        path: "view",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.view,
      },
      {
        path: "checkbox.isChecked",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
      {
        path: "pushAs.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <Stack__
      as={"div"}
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      hasGap={true}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        { [sty.rootcollapse]: hasVariant($state, "collapse", "collapse") }
      )}
    >
      <div className={classNames(projectcss.all, sty.freeBox___9QCvn)} />

      <div
        className={classNames(projectcss.all, sty.freeBox___9MQmz, {
          [sty.freeBoxview_status___9MQmz2JIHl]: hasVariant(
            $state,
            "view",
            "status"
          ),
        })}
      >
        <Stack__
          as={"div"}
          hasGap={true}
          className={classNames(projectcss.all, sty.freeBox__qZ5J8, {
            [sty.freeBoxcollapse__qZ5J8ShwF8]: hasVariant(
              $state,
              "collapse",
              "collapse"
            ),
          })}
        >
          <div className={classNames(projectcss.all, sty.freeBox__hq1LL)}>
            <Checkbox
              data-plasmic-name={"checkbox"}
              data-plasmic-override={overrides.checkbox}
              children={null}
              className={classNames("__wab_instance", sty.checkbox)}
              isChecked={
                generateStateValueProp($state, ["checkbox", "isChecked"]) ??
                false
              }
              onChange={async (...eventArgs: any) => {
                ((...eventArgs) => {
                  generateStateOnChangeProp($state, ["checkbox", "isChecked"])(
                    eventArgs[0]
                  );
                }).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />

            {false ? (
              <PlasmicImg__
                data-plasmic-name={"img"}
                data-plasmic-override={overrides.img}
                alt={""}
                className={classNames(sty.img)}
                displayHeight={"20px"}
                displayMaxHeight={"none"}
                displayMaxWidth={"none"}
                displayMinHeight={"0"}
                displayMinWidth={"0"}
                displayWidth={"20px"}
                src={{
                  src: image2DAmIiITwd,
                  fullWidth: 150,
                  fullHeight: 150,
                  aspectRatio: 1,
                }}
              />
            ) : null}
          </div>
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__oARo5,
              {
                [sty.textcollapse__oARo5ShwF8]: hasVariant(
                  $state,
                  "collapse",
                  "collapse"
                ),
              }
            )}
          >
            {"Publish to Plasmic Hosting"}
          </div>
          {false ? (
            <svg
              className={classNames(projectcss.all, sty.svg___1TkF4)}
              role={"img"}
            />
          ) : null}
        </Stack__>
        <Stack__
          as={"div"}
          hasGap={true}
          className={classNames(projectcss.all, sty.freeBox__pzVnq)}
        >
          <Button
            data-plasmic-name={"removeButton"}
            data-plasmic-override={overrides.removeButton}
            endIcon={
              <ChevronDownSvgIcon
                className={classNames(projectcss.all, sty.svg__sAuG)}
                role={"img"}
              />
            }
            size={"small"}
            startIcon={
              <ArrowRightSvgIcon
                className={classNames(projectcss.all, sty.svg__kbdb)}
                role={"img"}
              />
            }
            type={["clear"]}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__uXdi,
                {
                  [sty.textcollapse__uXdIshwF8]: hasVariant(
                    $state,
                    "collapse",
                    "collapse"
                  ),
                }
              )}
            >
              {hasVariant($state, "collapse", "collapse") ? "Show" : "Remove"}
            </div>
          </Button>
          {false ? (
            <Button
              data-plasmic-name={"viewGithubButton"}
              data-plasmic-override={overrides.viewGithubButton}
              className={classNames("__wab_instance", sty.viewGithubButton)}
              endIcon={
                <TriangleBottomIcon
                  className={classNames(projectcss.all, sty.svg__rOsJo)}
                  role={"img"}
                />
              }
              startIcon={
                <ArrowRightSvgIcon
                  className={classNames(projectcss.all, sty.svg__t3Mr3)}
                  role={"img"}
                />
              }
            >
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text___43JWs
                )}
              >
                {"Button"}
              </div>
            </Button>
          ) : null}
        </Stack__>
      </div>
      {(
        hasVariant($state, "view", "status")
          ? true
          : hasVariant($state, "collapse", "collapse")
          ? false
          : true
      ) ? (
        <div
          className={classNames(projectcss.all, sty.freeBox__xJMn, {
            [sty.freeBoxcollapse__xJMnshwF8]: hasVariant(
              $state,
              "collapse",
              "collapse"
            ),
            [sty.freeBoxview_status__xJMn2JIHl]: hasVariant(
              $state,
              "view",
              "status"
            ),
          })}
        >
          <Stack__
            as={"div"}
            hasGap={true}
            className={classNames(projectcss.all, sty.freeBox__ndh, {
              [sty.freeBoxview_status__ndh2JIHl]: hasVariant(
                $state,
                "view",
                "status"
              ),
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__wYnuA
              )}
            >
              {
                "Publish your app or website to Plasmic's built-in hosting platform."
              }
            </div>
            <PlasmicLink__
              data-plasmic-name={"learnMoreLink"}
              data-plasmic-override={overrides.learnMoreLink}
              className={classNames(
                projectcss.all,
                projectcss.a,
                projectcss.__wab_text,
                sty.learnMoreLink
              )}
              href={
                "https://docs.plasmic.app/learn/publishing/#plasmic-hosting"
              }
              platform={"react"}
              target={"_blank"}
            >
              {"Learn more."}
            </PlasmicLink__>
            <Stack__
              as={"div"}
              data-plasmic-name={"warning"}
              data-plasmic-override={overrides.warning}
              hasGap={true}
              className={classNames(projectcss.all, sty.warning)}
            >
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__jDjSw
                )}
              >
                <React.Fragment>
                  <span
                    className={"plasmic_default__all plasmic_default__span"}
                    style={{ fontWeight: 700 }}
                  >
                    {"Warning"}
                  </span>
                  <React.Fragment>
                    {
                      ": you have a custom application host. Plasmic Hosting does not have access to your code components. Deploy to your own hosting environment instead, and remove Plasmic Hosting!"
                    }
                  </React.Fragment>
                </React.Fragment>
              </div>
              {false ? (
                <div className={classNames(projectcss.all, sty.freeBox__pnvxx)}>
                  {renderPlasmicSlot({
                    defaultContents: "",
                    value: args.changesSummary,
                    className: classNames(sty.slotTargetChangesSummary),
                  })}
                </div>
              ) : null}
              {false ? (
                <Button
                  data-plasmic-name={"reviewChangesButton"}
                  data-plasmic-override={overrides.reviewChangesButton}
                  className={classNames(
                    "__wab_instance",
                    sty.reviewChangesButton
                  )}
                  endIcon={
                    <ChevronDownSvgIcon
                      className={classNames(projectcss.all, sty.svg___6Rgi)}
                      role={"img"}
                    />
                  }
                  size={"small"}
                  startIcon={
                    <ArrowRightSvgIcon
                      className={classNames(projectcss.all, sty.svg__fhc3P)}
                      role={"img"}
                    />
                  }
                  type={["secondary"]}
                >
                  {"Review all changes ->"}
                </Button>
              ) : null}
            </Stack__>
          </Stack__>
          <Stack__
            as={"div"}
            hasGap={true}
            className={classNames(projectcss.all, sty.freeBox__usSde)}
          >
            <Stack__
              as={"div"}
              hasGap={true}
              className={classNames(projectcss.all, sty.freeBox___9NuEy, {
                [sty.freeBoxview_status___9NuEy2JIHl]: hasVariant(
                  $state,
                  "view",
                  "status"
                ),
              })}
            >
              <Stack__
                as={"div"}
                hasGap={true}
                className={classNames(projectcss.all, sty.freeBox__whjUu, {
                  [sty.freeBoxview_status__whjUu2JIHl]: hasVariant(
                    $state,
                    "view",
                    "status"
                  ),
                })}
              >
                <GlobeSvgIcon
                  className={classNames(projectcss.all, sty.svg__gwk1, {
                    [sty.svgview_setup__gwk1JswHi]: hasVariant(
                      $state,
                      "view",
                      "setup"
                    ),
                  })}
                  role={"img"}
                />

                <PlasmicLink__
                  data-plasmic-name={"domain"}
                  data-plasmic-override={overrides.domain}
                  className={classNames(
                    projectcss.all,
                    projectcss.a,
                    projectcss.__wab_text,
                    sty.domain,
                    {
                      [sty.domaincollapse]: hasVariant(
                        $state,
                        "collapse",
                        "collapse"
                      ),
                    }
                  )}
                  platform={"react"}
                >
                  {"DOMAIN"}
                </PlasmicLink__>
              </Stack__>
              {false ? (
                <Button
                  data-plasmic-name={"retryButton"}
                  data-plasmic-override={overrides.retryButton}
                  className={classNames("__wab_instance", sty.retryButton)}
                  endIcon={
                    <ChevronDownSvgIcon
                      className={classNames(projectcss.all, sty.svg__neiw)}
                      role={"img"}
                    />
                  }
                  size={"wide"}
                  startIcon={
                    <ArrowRightSvgIcon
                      className={classNames(projectcss.all, sty.svg__x8NOs)}
                      role={"img"}
                    />
                  }
                  type={["secondary"]}
                  withIcons={["startIcon"]}
                >
                  {"Button"}
                </Button>
              ) : null}
              {false ? (
                <Button
                  data-plasmic-name={"removeGithubButton"}
                  data-plasmic-override={overrides.removeGithubButton}
                  className={classNames(
                    "__wab_instance",
                    sty.removeGithubButton
                  )}
                  endIcon={
                    <ChevronDownSvgIcon
                      className={classNames(projectcss.all, sty.svg__qpbxf)}
                      role={"img"}
                    />
                  }
                  startIcon={
                    <CloseIcon
                      className={classNames(projectcss.all, sty.svg__fdyfh)}
                      role={"img"}
                    />
                  }
                  type={["seamless"]}
                  withIcons={["startIcon"]}
                >
                  {"Remove"}
                </Button>
              ) : null}
              <Button
                data-plasmic-name={"setupButton"}
                data-plasmic-override={overrides.setupButton}
                className={classNames("__wab_instance", sty.setupButton, {
                  [sty.setupButtonview_setup]: hasVariant(
                    $state,
                    "view",
                    "setup"
                  ),
                })}
                endIcon={
                  <ChevronDownSvgIcon
                    className={classNames(projectcss.all, sty.svg__dB19)}
                    role={"img"}
                  />
                }
                size={"wide"}
                startIcon={
                  <OpenIcon
                    className={classNames(projectcss.all, sty.svg__w5QLi)}
                    role={"img"}
                  />
                }
                type={["primary"]}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__wx7K8
                  )}
                >
                  {"Configure"}
                </div>
              </Button>
            </Stack__>
            {false ? (
              <div className={classNames(projectcss.all, sty.freeBox__av8T2)}>
                <div className={classNames(projectcss.all, sty.freeBox__ePpd)}>
                  <div
                    className={classNames(
                      projectcss.all,
                      projectcss.__wab_text,
                      sty.text__q3S4W
                    )}
                  >
                    {"Push options"}
                  </div>
                  <div
                    className={classNames(projectcss.all, sty.freeBox__sTqXg)}
                  >
                    <Button
                      data-plasmic-name={"showOptionsButton"}
                      data-plasmic-override={overrides.showOptionsButton}
                      endIcon={
                        <ChevronDownSvgIcon
                          className={classNames(projectcss.all, sty.svg__oGuul)}
                          role={"img"}
                        />
                      }
                      startIcon={
                        <ArrowRightSvgIcon
                          className={classNames(projectcss.all, sty.svg__sgEkn)}
                          role={"img"}
                        />
                      }
                      type={["clear"]}
                    >
                      <div
                        className={classNames(
                          projectcss.all,
                          projectcss.__wab_text,
                          sty.text__flkoU
                        )}
                      >
                        {"Fewer options"}
                      </div>
                    </Button>
                    <ExpandButton
                      data-plasmic-name={"showOptionsIconButton"}
                      data-plasmic-override={overrides.showOptionsIconButton}
                      className={classNames(
                        "__wab_instance",
                        sty.showOptionsIconButton
                      )}
                      isExpanded={true}
                    />
                  </div>
                </div>
                <Stack__
                  as={"div"}
                  hasGap={true}
                  className={classNames(projectcss.all, sty.freeBox__p2Og6)}
                >
                  <Stack__
                    as={"div"}
                    hasGap={true}
                    className={classNames(projectcss.all, sty.freeBox__eKj17)}
                  >
                    <div
                      className={classNames(
                        projectcss.all,
                        projectcss.__wab_text,
                        sty.text__kz772
                      )}
                    >
                      {"Push as"}
                    </div>
                    <Select
                      data-plasmic-name={"pushAs"}
                      data-plasmic-override={overrides.pushAs}
                      className={classNames("__wab_instance", sty.pushAs)}
                      icon={
                        <PlusSvgIcon
                          className={classNames(projectcss.all, sty.svg__qtaBp)}
                          role={"img"}
                        />
                      }
                      onChange={async (...eventArgs: any) => {
                        ((...eventArgs) => {
                          generateStateOnChangeProp($state, [
                            "pushAs",
                            "value",
                          ])(eventArgs[0]);
                        }).apply(null, eventArgs);

                        if (
                          eventArgs.length > 1 &&
                          eventArgs[1] &&
                          eventArgs[1]._plasmic_state_init_
                        ) {
                          return;
                        }
                      }}
                      type={"bordered"}
                      value={generateStateValueProp($state, [
                        "pushAs",
                        "value",
                      ])}
                    />
                  </Stack__>
                  <Stack__
                    as={"div"}
                    hasGap={true}
                    className={classNames(projectcss.all, sty.freeBox__fzd2)}
                  >
                    <Stack__
                      as={"div"}
                      hasGap={true}
                      className={classNames(projectcss.all, sty.freeBox__iPgR7)}
                    >
                      <div
                        className={classNames(
                          projectcss.all,
                          projectcss.__wab_text,
                          sty.text__fMrn6
                        )}
                      >
                        {"Title (optional)"}
                      </div>
                      <input
                        data-plasmic-name={"title"}
                        data-plasmic-override={overrides.title}
                        className={classNames(
                          projectcss.all,
                          projectcss.input,
                          sty.title
                        )}
                        placeholder={"Title (optional)"}
                        ref={(ref) => {
                          $refs["title"] = ref;
                        }}
                        size={1}
                        type={"text"}
                        value={""}
                      />
                    </Stack__>
                    <Stack__
                      as={"div"}
                      hasGap={true}
                      className={classNames(projectcss.all, sty.freeBox__gakKe)}
                    >
                      <div
                        className={classNames(
                          projectcss.all,
                          projectcss.__wab_text,
                          sty.text__eYbzm
                        )}
                      >
                        {"Description (optional)"}
                      </div>
                      <textarea
                        data-plasmic-name={"description"}
                        data-plasmic-override={overrides.description}
                        className={classNames(
                          projectcss.all,
                          projectcss.textarea,
                          sty.description
                        )}
                        placeholder={"Description (optional)"}
                        ref={(ref) => {
                          $refs["description"] = ref;
                        }}
                        rows={3}
                        value={""}
                      />
                    </Stack__>
                  </Stack__>
                </Stack__>
              </div>
            ) : null}
          </Stack__>
        </div>
      ) : null}
      {(hasVariant($state, "view", "status") ? true : false) ? (
        <div
          className={classNames(projectcss.all, sty.freeBox__daTyt, {
            [sty.freeBoxview_status__daTyt2JIHl]: hasVariant(
              $state,
              "view",
              "status"
            ),
          })}
        >
          <Stack__
            as={"ul"}
            data-plasmic-name={"steps"}
            data-plasmic-override={overrides.steps}
            hasGap={true}
            className={classNames(projectcss.all, projectcss.ul, sty.steps)}
          >
            <GitJobStep
              className={classNames("__wab_instance", sty.gitJobStep__u7Srv)}
              status={"finished"}
            >
              {"Check repository state"}
            </GitJobStep>
            <GitJobStep
              className={classNames("__wab_instance", sty.gitJobStep__y2Fyr)}
              status={"finished"}
            >
              {"Fetch GitHub access token"}
            </GitJobStep>
            <GitJobStep
              className={classNames("__wab_instance", sty.gitJobStep__kX6Oh)}
              status={"finished"}
            >
              {"Fetch Plasmic access token"}
            </GitJobStep>
            <GitJobStep
              className={classNames("__wab_instance", sty.gitJobStep__mbLw0)}
              status={"started"}
            >
              {"Clone repository"}
            </GitJobStep>
            <GitJobStep
              className={classNames("__wab_instance", sty.gitJobStep__gOzj4)}
            >
              {"Sync project"}
            </GitJobStep>
            <GitJobStep
              className={classNames("__wab_instance", sty.gitJobStep__nxWc0)}
            >
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__kGJg
                )}
              >
                {"Commit changes"}
              </div>
            </GitJobStep>
            <GitJobStep
              className={classNames("__wab_instance", sty.gitJobStep___3Ntiv)}
            >
              {"Push changes"}
            </GitJobStep>
            <GitJobStep
              className={classNames("__wab_instance", sty.gitJobStep___4D1Y)}
            >
              {"Make pull request"}
            </GitJobStep>
          </Stack__>
        </div>
      ) : null}
      {false ? (
        <div
          data-plasmic-name={"githubPagesDelayNotice"}
          data-plasmic-override={overrides.githubPagesDelayNotice}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.githubPagesDelayNotice
          )}
        >
          {"Enter some text"}
        </div>
      ) : null}
    </Stack__>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "checkbox",
    "img",
    "removeButton",
    "viewGithubButton",
    "learnMoreLink",
    "warning",
    "reviewChangesButton",
    "domain",
    "retryButton",
    "removeGithubButton",
    "setupButton",
    "showOptionsButton",
    "showOptionsIconButton",
    "pushAs",
    "title",
    "description",
    "steps",
    "githubPagesDelayNotice",
  ],
  checkbox: ["checkbox"],
  img: ["img"],
  removeButton: ["removeButton"],
  viewGithubButton: ["viewGithubButton"],
  learnMoreLink: ["learnMoreLink"],
  warning: ["warning", "reviewChangesButton"],
  reviewChangesButton: ["reviewChangesButton"],
  domain: ["domain"],
  retryButton: ["retryButton"],
  removeGithubButton: ["removeGithubButton"],
  setupButton: ["setupButton"],
  showOptionsButton: ["showOptionsButton"],
  showOptionsIconButton: ["showOptionsIconButton"],
  pushAs: ["pushAs"],
  title: ["title"],
  description: ["description"],
  steps: ["steps"],
  githubPagesDelayNotice: ["githubPagesDelayNotice"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  checkbox: typeof Checkbox;
  img: typeof PlasmicImg__;
  removeButton: typeof Button;
  viewGithubButton: typeof Button;
  learnMoreLink: "a";
  warning: "div";
  reviewChangesButton: typeof Button;
  domain: "a";
  retryButton: typeof Button;
  removeGithubButton: typeof Button;
  setupButton: typeof Button;
  showOptionsButton: typeof Button;
  showOptionsIconButton: typeof ExpandButton;
  pushAs: typeof Select;
  title: "input";
  description: "textarea";
  steps: "ul";
  githubPagesDelayNotice: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSubsectionPlasmicHosting__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSubsectionPlasmicHosting__VariantsArgs;
    args?: PlasmicSubsectionPlasmicHosting__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSubsectionPlasmicHosting__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicSubsectionPlasmicHosting__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSubsectionPlasmicHosting__ArgProps,
          internalVariantPropNames:
            PlasmicSubsectionPlasmicHosting__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicSubsectionPlasmicHosting__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicSubsectionPlasmicHosting";
  } else {
    func.displayName = `PlasmicSubsectionPlasmicHosting.${nodeName}`;
  }
  return func;
}

export const PlasmicSubsectionPlasmicHosting = Object.assign(
  // Top-level PlasmicSubsectionPlasmicHosting renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    checkbox: makeNodeComponent("checkbox"),
    img: makeNodeComponent("img"),
    removeButton: makeNodeComponent("removeButton"),
    viewGithubButton: makeNodeComponent("viewGithubButton"),
    learnMoreLink: makeNodeComponent("learnMoreLink"),
    warning: makeNodeComponent("warning"),
    reviewChangesButton: makeNodeComponent("reviewChangesButton"),
    domain: makeNodeComponent("domain"),
    retryButton: makeNodeComponent("retryButton"),
    removeGithubButton: makeNodeComponent("removeGithubButton"),
    setupButton: makeNodeComponent("setupButton"),
    showOptionsButton: makeNodeComponent("showOptionsButton"),
    showOptionsIconButton: makeNodeComponent("showOptionsIconButton"),
    pushAs: makeNodeComponent("pushAs"),
    title: makeNodeComponent("title"),
    description: makeNodeComponent("description"),
    steps: makeNodeComponent("steps"),
    githubPagesDelayNotice: makeNodeComponent("githubPagesDelayNotice"),

    // Metadata about props expected for PlasmicSubsectionPlasmicHosting
    internalVariantProps: PlasmicSubsectionPlasmicHosting__VariantProps,
    internalArgProps: PlasmicSubsectionPlasmicHosting__ArgProps,
  }
);

export default PlasmicSubsectionPlasmicHosting;
/* prettier-ignore-end */
