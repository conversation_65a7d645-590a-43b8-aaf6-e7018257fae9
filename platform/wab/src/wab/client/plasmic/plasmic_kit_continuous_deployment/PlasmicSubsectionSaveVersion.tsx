/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: fpbcKyXdMTvY59T4C5fjcC
// Component: 74wUdEnJhwr

import * as React from "react";

import {
  Flex as Flex__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  generateStateOnChangeProp,
  generateStateValueProp,
  hasVariant,
  renderPlasmicSlot,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import Button from "../../components/widgets/Button"; // plasmic-import: SEF-sRmSoqV5c/component
import Checkbox from "../../components/widgets/Checkbox"; // plasmic-import: W-rO7NZqPjZ/component
import Select from "../../components/widgets/Select"; // plasmic-import: j_4IQyOWK2b/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "../../components/modals/plasmic/plasmic_kit_project_settings/plasmic_plasmic_kit_project_settings.module.css"; // plasmic-import: fpbcKyXdMTvY59T4C5fjcC/projectcss
import plasmic_plasmic_kit_design_system_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import sty from "./PlasmicSubsectionSaveVersion.module.css"; // plasmic-import: 74wUdEnJhwr/css

import ArrowRightSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ArrowRightSvg"; // plasmic-import: 9Jv8jb253/icon
import ChevronDownSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg"; // plasmic-import: xZrB9_0ir/icon
import PlusSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__PlusSvg"; // plasmic-import: sQKgd2GNr/icon
import imageXpXXn3D3XS from "./images/image.svg"; // plasmic-import: XpXXn3d3xS/picture

createPlasmicElementProxy;

export type PlasmicSubsectionSaveVersion__VariantMembers = {
  collapse: "collapse";
  changesState: "loading" | "none" | "patch" | "minor" | "major" | "first";
  view: "setup" | "status";
  failed: "failed";
};
export type PlasmicSubsectionSaveVersion__VariantsArgs = {
  collapse?: SingleBooleanChoiceArg<"collapse">;
  changesState?: SingleChoiceArg<
    "loading" | "none" | "patch" | "minor" | "major" | "first"
  >;
  view?: SingleChoiceArg<"setup" | "status">;
  failed?: SingleBooleanChoiceArg<"failed">;
};
type VariantPropType = keyof PlasmicSubsectionSaveVersion__VariantsArgs;
export const PlasmicSubsectionSaveVersion__VariantProps =
  new Array<VariantPropType>("collapse", "changesState", "view", "failed");

export type PlasmicSubsectionSaveVersion__ArgsType = {
  nextVersion?: React.ReactNode;
  changesSummary?: React.ReactNode;
  feedback?: React.ReactNode;
};
type ArgPropType = keyof PlasmicSubsectionSaveVersion__ArgsType;
export const PlasmicSubsectionSaveVersion__ArgProps = new Array<ArgPropType>(
  "nextVersion",
  "changesSummary",
  "feedback"
);

export type PlasmicSubsectionSaveVersion__OverridesType = {
  root?: Flex__<"div">;
  checkbox?: Flex__<typeof Checkbox>;
  img?: Flex__<typeof PlasmicImg__>;
  learnMoreLink?: Flex__<"a">;
  reviewChangesButton?: Flex__<typeof Button>;
  viewHistoryButton?: Flex__<typeof Button>;
  description?: Flex__<"textarea">;
  tagsStack?: Flex__<"div">;
  tagsSelector?: Flex__<typeof Select>;
};

export interface DefaultSubsectionSaveVersionProps {
  nextVersion?: React.ReactNode;
  changesSummary?: React.ReactNode;
  feedback?: React.ReactNode;
  collapse?: SingleBooleanChoiceArg<"collapse">;
  changesState?: SingleChoiceArg<
    "loading" | "none" | "patch" | "minor" | "major" | "first"
  >;
  view?: SingleChoiceArg<"setup" | "status">;
  failed?: SingleBooleanChoiceArg<"failed">;
  className?: string;
}

const $$ = {};

function PlasmicSubsectionSaveVersion__RenderFunc(props: {
  variants: PlasmicSubsectionSaveVersion__VariantsArgs;
  args: PlasmicSubsectionSaveVersion__ArgsType;
  overrides: PlasmicSubsectionSaveVersion__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "collapse",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.collapse,
      },
      {
        path: "changesState",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.changesState,
      },
      {
        path: "view",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.view,
      },
      {
        path: "failed",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.failed,
      },
      {
        path: "checkbox.isChecked",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
      {
        path: "tagsSelector.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <Stack__
      as={"div"}
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      hasGap={true}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        {
          [sty.rootcollapse]: hasVariant($state, "collapse", "collapse"),
          [sty.rootfailed]: hasVariant($state, "failed", "failed"),
          [sty.rootview_status]: hasVariant($state, "view", "status"),
        }
      )}
    >
      <div className={classNames(projectcss.all, sty.freeBox__hv1F)} />

      <div
        className={classNames(projectcss.all, sty.freeBox__tZuGq, {
          [sty.freeBoxchangesState_none__tZuGqerDWt]: hasVariant(
            $state,
            "changesState",
            "none"
          ),
          [sty.freeBoxview_status__tZuGqjhl1P]: hasVariant(
            $state,
            "view",
            "status"
          ),
        })}
      >
        <Stack__
          as={"div"}
          hasGap={true}
          className={classNames(projectcss.all, sty.freeBox__aHwL)}
        >
          <div
            className={classNames(projectcss.all, sty.freeBox__oAx16, {
              [sty.freeBoxfailed__oAx165ST]: hasVariant(
                $state,
                "failed",
                "failed"
              ),
            })}
          >
            {(hasVariant($state, "view", "status") ? false : true) ? (
              <Checkbox
                data-plasmic-name={"checkbox"}
                data-plasmic-override={overrides.checkbox}
                children={null}
                className={classNames("__wab_instance", sty.checkbox, {
                  [sty.checkboxview_status]: hasVariant(
                    $state,
                    "view",
                    "status"
                  ),
                })}
                isChecked={
                  generateStateValueProp($state, ["checkbox", "isChecked"]) ??
                  false
                }
                onChange={async (...eventArgs: any) => {
                  ((...eventArgs) => {
                    generateStateOnChangeProp($state, [
                      "checkbox",
                      "isChecked",
                    ])(eventArgs[0]);
                  }).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
              />
            ) : null}
            {(hasVariant($state, "view", "status") ? true : false) ? (
              <PlasmicImg__
                data-plasmic-name={"img"}
                data-plasmic-override={overrides.img}
                alt={""}
                className={classNames(sty.img, {
                  [sty.imgview_status]: hasVariant($state, "view", "status"),
                })}
                displayHeight={"20px"}
                displayMaxHeight={"none"}
                displayMaxWidth={"none"}
                displayMinHeight={"0"}
                displayMinWidth={"0"}
                displayWidth={"20px"}
                src={{
                  src: imageXpXXn3D3XS,
                  fullWidth: 150,
                  fullHeight: 150,
                  aspectRatio: 1,
                }}
              />
            ) : null}
          </div>
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__rAFb
            )}
          >
            {"Save a new version"}
          </div>
        </Stack__>
      </div>
      {(
        hasVariant($state, "failed", "failed")
          ? false
          : hasVariant($state, "view", "status")
          ? false
          : hasVariant($state, "collapse", "collapse")
          ? false
          : true
      ) ? (
        <div
          className={classNames(projectcss.all, sty.freeBox___556M, {
            [sty.freeBoxcollapse___556MJ0D6K]: hasVariant(
              $state,
              "collapse",
              "collapse"
            ),
            [sty.freeBoxfailed___556M5ST]: hasVariant(
              $state,
              "failed",
              "failed"
            ),
            [sty.freeBoxview_status___556Mjhl1P]: hasVariant(
              $state,
              "view",
              "status"
            ),
          })}
        >
          <Stack__
            as={"div"}
            hasGap={true}
            className={classNames(projectcss.all, sty.freeBox__oMcN, {
              [sty.freeBoxfailed__oMcN5ST]: hasVariant(
                $state,
                "failed",
                "failed"
              ),
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__dnUmC
              )}
            >
              {
                "Publishing a version lets you restore it later, and also lets other projects import this project to use its components/assets."
              }
            </div>
            <PlasmicLink__
              data-plasmic-name={"learnMoreLink"}
              data-plasmic-override={overrides.learnMoreLink}
              className={classNames(
                projectcss.all,
                projectcss.a,
                projectcss.__wab_text,
                sty.learnMoreLink
              )}
              href={"https://www.plasmic.app/learn/publishing-importing/"}
              platform={"react"}
            >
              {"Learn more."}
            </PlasmicLink__>
          </Stack__>
          <Stack__
            as={"div"}
            hasGap={true}
            className={classNames(projectcss.all, sty.freeBox__ks1K7, {
              [sty.freeBoxchangesState_major__ks1K73In4S]: hasVariant(
                $state,
                "changesState",
                "major"
              ),
              [sty.freeBoxchangesState_patch__ks1K7Oslbo]: hasVariant(
                $state,
                "changesState",
                "patch"
              ),
              [sty.freeBoxfailed__ks1K75ST]: hasVariant(
                $state,
                "failed",
                "failed"
              ),
            })}
          >
            <Stack__
              as={"div"}
              hasGap={true}
              className={classNames(projectcss.all, sty.freeBox__h5G6F, {
                [sty.freeBoxchangesState_minor__h5G6FRvh4M]: hasVariant(
                  $state,
                  "changesState",
                  "minor"
                ),
                [sty.freeBoxfailed__h5G6F5ST]: hasVariant(
                  $state,
                  "failed",
                  "failed"
                ),
              })}
            >
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__kJhMc
                )}
              >
                {"Changes"}
              </div>
              <Stack__
                as={"div"}
                hasGap={true}
                className={classNames(projectcss.all, sty.freeBox___0H0Cx, {
                  [sty.freeBoxchangesState_loading___0H0Cxckc3O]: hasVariant(
                    $state,
                    "changesState",
                    "loading"
                  ),
                  [sty.freeBoxchangesState_major___0H0Cx3In4S]: hasVariant(
                    $state,
                    "changesState",
                    "major"
                  ),
                  [sty.freeBoxchangesState_minor___0H0CxRvh4M]: hasVariant(
                    $state,
                    "changesState",
                    "minor"
                  ),
                  [sty.freeBoxchangesState_none___0H0CxerDWt]: hasVariant(
                    $state,
                    "changesState",
                    "none"
                  ),
                  [sty.freeBoxchangesState_patch___0H0CxOslbo]: hasVariant(
                    $state,
                    "changesState",
                    "patch"
                  ),
                })}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__dhnm,
                    {
                      [sty.textchangesState_first__dhnmvyy0C]: hasVariant(
                        $state,
                        "changesState",
                        "first"
                      ),
                      [sty.textchangesState_loading__dhnmckc3O]: hasVariant(
                        $state,
                        "changesState",
                        "loading"
                      ),
                      [sty.textchangesState_major__dhnm3In4S]: hasVariant(
                        $state,
                        "changesState",
                        "major"
                      ),
                      [sty.textchangesState_minor__dhnmRvh4M]: hasVariant(
                        $state,
                        "changesState",
                        "minor"
                      ),
                      [sty.textchangesState_none__dhnmerDWt]: hasVariant(
                        $state,
                        "changesState",
                        "none"
                      ),
                      [sty.textchangesState_patch__dhnmOslbo]: hasVariant(
                        $state,
                        "changesState",
                        "patch"
                      ),
                    }
                  )}
                >
                  {hasVariant($state, "changesState", "first")
                    ? "You're publishing for the first time!"
                    : hasVariant($state, "changesState", "major")
                    ? "The new version you're saving contains potentially breaking changes, as some things have been removed or renamed. Please review changes before saving."
                    : hasVariant($state, "changesState", "minor")
                    ? "You have new changes to publish."
                    : hasVariant($state, "changesState", "patch")
                    ? "You have new changes to publish."
                    : hasVariant($state, "changesState", "none")
                    ? "You have no new changes to publish."
                    : "Computing changes since last publish..."}
                </div>
                {(
                  hasVariant($state, "changesState", "major")
                    ? true
                    : hasVariant($state, "changesState", "minor")
                    ? true
                    : hasVariant($state, "changesState", "patch")
                    ? true
                    : false
                ) ? (
                  <div
                    className={classNames(projectcss.all, sty.freeBox__hTHvd, {
                      [sty.freeBoxchangesState_major__hTHvd3In4S]: hasVariant(
                        $state,
                        "changesState",
                        "major"
                      ),
                      [sty.freeBoxchangesState_minor__hTHvdRvh4M]: hasVariant(
                        $state,
                        "changesState",
                        "minor"
                      ),
                      [sty.freeBoxchangesState_patch__hTHvdOslbo]: hasVariant(
                        $state,
                        "changesState",
                        "patch"
                      ),
                    })}
                  >
                    {renderPlasmicSlot({
                      defaultContents: "",
                      value: args.changesSummary,
                      className: classNames(sty.slotTargetChangesSummary, {
                        [sty.slotTargetChangesSummarychangesState_loading]:
                          hasVariant($state, "changesState", "loading"),
                        [sty.slotTargetChangesSummarychangesState_major]:
                          hasVariant($state, "changesState", "major"),
                        [sty.slotTargetChangesSummarychangesState_minor]:
                          hasVariant($state, "changesState", "minor"),
                        [sty.slotTargetChangesSummarychangesState_none]:
                          hasVariant($state, "changesState", "none"),
                        [sty.slotTargetChangesSummarychangesState_patch]:
                          hasVariant($state, "changesState", "patch"),
                      }),
                    })}
                  </div>
                ) : null}
                {(
                  hasVariant($state, "changesState", "major")
                    ? true
                    : hasVariant($state, "changesState", "minor")
                    ? true
                    : hasVariant($state, "changesState", "patch")
                    ? true
                    : hasVariant($state, "changesState", "none")
                    ? false
                    : false
                ) ? (
                  <Button
                    data-plasmic-name={"reviewChangesButton"}
                    data-plasmic-override={overrides.reviewChangesButton}
                    className={classNames(
                      "__wab_instance",
                      sty.reviewChangesButton,
                      {
                        [sty.reviewChangesButtonchangesState_loading]:
                          hasVariant($state, "changesState", "loading"),
                        [sty.reviewChangesButtonchangesState_major]: hasVariant(
                          $state,
                          "changesState",
                          "major"
                        ),
                        [sty.reviewChangesButtonchangesState_minor]: hasVariant(
                          $state,
                          "changesState",
                          "minor"
                        ),
                        [sty.reviewChangesButtonchangesState_none]: hasVariant(
                          $state,
                          "changesState",
                          "none"
                        ),
                        [sty.reviewChangesButtonchangesState_patch]: hasVariant(
                          $state,
                          "changesState",
                          "patch"
                        ),
                      }
                    )}
                    endIcon={
                      <ChevronDownSvgIcon
                        className={classNames(projectcss.all, sty.svg__cgWjM)}
                        role={"img"}
                      />
                    }
                    size={"small"}
                    startIcon={
                      <ArrowRightSvgIcon
                        className={classNames(projectcss.all, sty.svg__td7KB)}
                        role={"img"}
                      />
                    }
                    type={["secondary"]}
                  >
                    {"Review all changes ->"}
                  </Button>
                ) : null}
              </Stack__>
            </Stack__>
            {(
              hasVariant($state, "changesState", "major")
                ? true
                : hasVariant($state, "changesState", "minor")
                ? true
                : hasVariant($state, "changesState", "patch")
                ? true
                : false
            ) ? (
              <Stack__
                as={"div"}
                hasGap={true}
                className={classNames(projectcss.all, sty.freeBox__zomOs, {
                  [sty.freeBoxchangesState_major__zomOs3In4S]: hasVariant(
                    $state,
                    "changesState",
                    "major"
                  ),
                  [sty.freeBoxchangesState_minor__zomOsRvh4M]: hasVariant(
                    $state,
                    "changesState",
                    "minor"
                  ),
                  [sty.freeBoxchangesState_patch__zomOsOslbo]: hasVariant(
                    $state,
                    "changesState",
                    "patch"
                  ),
                })}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__ppYw,
                    {
                      [sty.textchangesState_patch__ppYwoslbo]: hasVariant(
                        $state,
                        "changesState",
                        "patch"
                      ),
                      [sty.textview_setup__ppYw7XwDp]: hasVariant(
                        $state,
                        "view",
                        "setup"
                      ),
                    }
                  )}
                >
                  {"Version"}
                </div>
                <div
                  className={classNames(projectcss.all, sty.freeBox__kospk, {
                    [sty.freeBoxchangesState_patch__kospkoslbo]: hasVariant(
                      $state,
                      "changesState",
                      "patch"
                    ),
                  })}
                >
                  <div
                    className={classNames(projectcss.all, sty.freeBox___0E7Ii)}
                  >
                    {renderPlasmicSlot({
                      defaultContents: "v0.0.1",
                      value: args.nextVersion,
                      className: classNames(sty.slotTargetNextVersion, {
                        [sty.slotTargetNextVersionchangesState_patch]:
                          hasVariant($state, "changesState", "patch"),
                      }),
                    })}
                  </div>
                  <Button
                    data-plasmic-name={"viewHistoryButton"}
                    data-plasmic-override={overrides.viewHistoryButton}
                    className={classNames(
                      "__wab_instance",
                      sty.viewHistoryButton
                    )}
                    endIcon={
                      <ChevronDownSvgIcon
                        className={classNames(projectcss.all, sty.svg__dZ2D)}
                        role={"img"}
                      />
                    }
                    size={"wide"}
                    startIcon={
                      <ArrowRightSvgIcon
                        className={classNames(projectcss.all, sty.svg__w1Hb)}
                        role={"img"}
                      />
                    }
                    type={["clear"]}
                  >
                    <div
                      className={classNames(
                        projectcss.all,
                        projectcss.__wab_text,
                        sty.text___6S6Cr
                      )}
                    >
                      {"View history"}
                    </div>
                  </Button>
                </div>
              </Stack__>
            ) : null}
            {(
              hasVariant($state, "changesState", "major")
                ? true
                : hasVariant($state, "changesState", "minor")
                ? true
                : hasVariant($state, "changesState", "patch")
                ? true
                : false
            ) ? (
              <Stack__
                as={"div"}
                hasGap={true}
                className={classNames(projectcss.all, sty.freeBox__pX75D, {
                  [sty.freeBoxchangesState_major__pX75D3In4S]: hasVariant(
                    $state,
                    "changesState",
                    "major"
                  ),
                  [sty.freeBoxchangesState_minor__pX75DRvh4M]: hasVariant(
                    $state,
                    "changesState",
                    "minor"
                  ),
                  [sty.freeBoxchangesState_patch__pX75DOslbo]: hasVariant(
                    $state,
                    "changesState",
                    "patch"
                  ),
                })}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__g1WL,
                    {
                      [sty.textchangesState_patch__g1WLoslbo]: hasVariant(
                        $state,
                        "changesState",
                        "patch"
                      ),
                    }
                  )}
                >
                  {"Description"}
                </div>
                <textarea
                  data-plasmic-name={"description"}
                  data-plasmic-override={overrides.description}
                  className={classNames(
                    projectcss.all,
                    projectcss.textarea,
                    sty.description,
                    {
                      [sty.descriptionchangesState_major]: hasVariant(
                        $state,
                        "changesState",
                        "major"
                      ),
                      [sty.descriptionchangesState_patch]: hasVariant(
                        $state,
                        "changesState",
                        "patch"
                      ),
                    }
                  )}
                  placeholder={"Enter description here (optional)…"}
                  ref={(ref) => {
                    $refs["description"] = ref;
                  }}
                  rows={2}
                  value={""}
                />
              </Stack__>
            ) : null}
            {(
              hasVariant($state, "changesState", "major")
                ? true
                : hasVariant($state, "changesState", "minor")
                ? true
                : hasVariant($state, "changesState", "patch")
                ? true
                : false
            ) ? (
              <Stack__
                as={"div"}
                data-plasmic-name={"tagsStack"}
                data-plasmic-override={overrides.tagsStack}
                hasGap={true}
                className={classNames(projectcss.all, sty.tagsStack, {
                  [sty.tagsStackchangesState_major]: hasVariant(
                    $state,
                    "changesState",
                    "major"
                  ),
                  [sty.tagsStackchangesState_minor]: hasVariant(
                    $state,
                    "changesState",
                    "minor"
                  ),
                  [sty.tagsStackchangesState_patch]: hasVariant(
                    $state,
                    "changesState",
                    "patch"
                  ),
                })}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__l4FT0,
                    {
                      [sty.textchangesState_patch__l4FT0Oslbo]: hasVariant(
                        $state,
                        "changesState",
                        "patch"
                      ),
                    }
                  )}
                >
                  {"Tags"}
                </div>
                <Select
                  data-plasmic-name={"tagsSelector"}
                  data-plasmic-override={overrides.tagsSelector}
                  className={classNames("__wab_instance", sty.tagsSelector, {
                    [sty.tagsSelectorchangesState_major]: hasVariant(
                      $state,
                      "changesState",
                      "major"
                    ),
                    [sty.tagsSelectorchangesState_patch]: hasVariant(
                      $state,
                      "changesState",
                      "patch"
                    ),
                  })}
                  icon={
                    <PlusSvgIcon
                      className={classNames(projectcss.all, sty.svg__db8GA)}
                      role={"img"}
                    />
                  }
                  onChange={async (...eventArgs: any) => {
                    ((...eventArgs) => {
                      generateStateOnChangeProp($state, [
                        "tagsSelector",
                        "value",
                      ])(eventArgs[0]);
                    }).apply(null, eventArgs);

                    if (
                      eventArgs.length > 1 &&
                      eventArgs[1] &&
                      eventArgs[1]._plasmic_state_init_
                    ) {
                      return;
                    }
                  }}
                  type={"bordered"}
                  value={generateStateValueProp($state, [
                    "tagsSelector",
                    "value",
                  ])}
                />
              </Stack__>
            ) : null}
          </Stack__>
        </div>
      ) : null}
      {(
        hasVariant($state, "failed", "failed")
          ? true
          : hasVariant($state, "view", "status")
          ? true
          : false
      ) ? (
        <Stack__
          as={"div"}
          hasGap={true}
          className={classNames(projectcss.all, sty.freeBox___0V80Z, {
            [sty.freeBoxchangesState_minor___0V80ZRvh4M]: hasVariant(
              $state,
              "changesState",
              "minor"
            ),
            [sty.freeBoxfailed___0V80Z5ST]: hasVariant(
              $state,
              "failed",
              "failed"
            ),
            [sty.freeBoxview_status___0V80Zjhl1P]: hasVariant(
              $state,
              "view",
              "status"
            ),
          })}
        >
          {renderPlasmicSlot({
            defaultContents: (
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__dkcxx
                )}
              >
                <React.Fragment>
                  <React.Fragment>{"Successfully published "}</React.Fragment>
                  <span
                    className={"plasmic_default__all plasmic_default__span"}
                    style={{ fontWeight: 700 }}
                  >
                    {"Project Name v1.0.1"}
                  </span>
                  <React.Fragment>{"!"}</React.Fragment>
                </React.Fragment>
              </div>
            ),
            value: args.feedback,
            className: classNames(sty.slotTargetFeedback, {
              [sty.slotTargetFeedbackfailed]: hasVariant(
                $state,
                "failed",
                "failed"
              ),
              [sty.slotTargetFeedbackview_status]: hasVariant(
                $state,
                "view",
                "status"
              ),
            }),
          })}
        </Stack__>
      ) : null}
    </Stack__>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "checkbox",
    "img",
    "learnMoreLink",
    "reviewChangesButton",
    "viewHistoryButton",
    "description",
    "tagsStack",
    "tagsSelector",
  ],
  checkbox: ["checkbox"],
  img: ["img"],
  learnMoreLink: ["learnMoreLink"],
  reviewChangesButton: ["reviewChangesButton"],
  viewHistoryButton: ["viewHistoryButton"],
  description: ["description"],
  tagsStack: ["tagsStack", "tagsSelector"],
  tagsSelector: ["tagsSelector"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  checkbox: typeof Checkbox;
  img: typeof PlasmicImg__;
  learnMoreLink: "a";
  reviewChangesButton: typeof Button;
  viewHistoryButton: typeof Button;
  description: "textarea";
  tagsStack: "div";
  tagsSelector: typeof Select;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSubsectionSaveVersion__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSubsectionSaveVersion__VariantsArgs;
    args?: PlasmicSubsectionSaveVersion__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSubsectionSaveVersion__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicSubsectionSaveVersion__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSubsectionSaveVersion__ArgProps,
          internalVariantPropNames: PlasmicSubsectionSaveVersion__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicSubsectionSaveVersion__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicSubsectionSaveVersion";
  } else {
    func.displayName = `PlasmicSubsectionSaveVersion.${nodeName}`;
  }
  return func;
}

export const PlasmicSubsectionSaveVersion = Object.assign(
  // Top-level PlasmicSubsectionSaveVersion renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    checkbox: makeNodeComponent("checkbox"),
    img: makeNodeComponent("img"),
    learnMoreLink: makeNodeComponent("learnMoreLink"),
    reviewChangesButton: makeNodeComponent("reviewChangesButton"),
    viewHistoryButton: makeNodeComponent("viewHistoryButton"),
    description: makeNodeComponent("description"),
    tagsStack: makeNodeComponent("tagsStack"),
    tagsSelector: makeNodeComponent("tagsSelector"),

    // Metadata about props expected for PlasmicSubsectionSaveVersion
    internalVariantProps: PlasmicSubsectionSaveVersion__VariantProps,
    internalArgProps: PlasmicSubsectionSaveVersion__ArgProps,
  }
);

export default PlasmicSubsectionSaveVersion;
/* prettier-ignore-end */
