.root {
  display: flex;
  position: relative;
  flex-direction: column;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.freeBox__oBcR5 {
  display: flex;
  position: relative;
  flex-direction: row;
  padding: 32px;
}
.freeBox__oBcR5 > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  margin-left: calc(0px - 32px);
  width: calc(100% + 32px);
}
.freeBox__oBcR5 > :global(.__wab_flex-container) > *,
.freeBox__oBcR5 > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox__oBcR5 > :global(.__wab_flex-container) > picture > img,
.freeBox__oBcR5
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: 32px;
}
.text__fcY<PERSON>o {
  font-size: 14px;
  line-height: 20px;
  height: auto;
}
.text__dQpEf {
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
  height: auto;
}
.freeBox__p2G5H {
  display: flex;
  position: relative;
  flex-direction: row;
  padding: 32px;
}
.freeBox__p2G5H > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  margin-left: calc(0px - 32px);
  width: calc(100% + 32px);
}
.freeBox__p2G5H > :global(.__wab_flex-container) > *,
.freeBox__p2G5H > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox__p2G5H > :global(.__wab_flex-container) > picture > img,
.freeBox__p2G5H
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: 32px;
}
.text___4PNiK {
  font-size: 11px;
  line-height: 16px;
  height: auto;
}
.text__isDyD {
  font-size: 11px;
  line-height: 16px;
  font-weight: 600;
  height: auto;
}
