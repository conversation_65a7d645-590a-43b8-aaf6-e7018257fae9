{"name": "wab", "version": "0.0.1", "description": "Plasmic main codebase", "private": true, "engines": {"node": ">=18.0.0"}, "homepage": "https://studio.plasmic.app", "scripts": {"postinstall": "patch-package", "dev": "bash tools/dev.bash", "dev:screens": "bash tools/start.bash", "dev:frontend": "wait-on http://localhost:3004 && PORT=3003 yarn start", "dev:frontend:prodbuild": "wait-on http://localhost:3004 && PUBLIC_URL=http://localhost:3003 bash tools/dev-server.bash build && yarn dev:frontend:proxy", "dev:frontend:proxy": "cd build && npx -p local-web-server ws --spa index.html --port 3003 --cors.origin '*' --rewrite '/api/(.*) -> http://localhost:3004/api/$1'", "dev:backend": "yarn backend", "start": "bash tools/dev-server.bash dev", "ts-watch": "NODE_OPTIONS='--max-old-space-size=16384' tsc --noEmit --watch", "typecheck": "NODE_OPTIONS='--max-old-space-size=16384' tsc --noEmit", "host-server": "bash tools/host-server.bash", "socket-server": "npm run run-ts -- src/wab/server/app-socket-backend-real.ts", "build": "rsbuild build", "cypress:open": "cypress open", "migrate-dev-bundles": "npm run run-ts -- src/wab/server/scripts/dev-bundle-migrator.ts", "db:eval-corpus": "npm run run-ts -- src/wab/server/copilot/internal/sandbox/corpus-eval.ts", "db:reset": "bash tools/docker-dev/db-reset.bash", "db:setup": "bash tools/docker-dev/db-setup.bash", "db:prune": "NODE_OPTIONS='--unhandled-rejections=strict' npm run run-ts -- src/wab/server/db/DbPruner.ts", "db:invariants": "npm run run-ts -- src/wab/server/db/AssertSiteInvariants.ts", "db:revert-migration": "npm run run-ts -- src/wab/server/db/RevertMigration.ts", "db:projects": "npm run run-ts -- src/wab/server/db/DbProjectUtils.ts", "db:one-time-job": "npm run run-ts -- src/wab/server/db/DbCustomScripts.ts", "db:publish-hostless": "npm run run-ts -- src/wab/server/db/PublishHostless.ts", "db:permanently-delete": "npm run run-ts -- src/wab/server/db/permanently-delete.ts", "db:upgrade-stale-bundle": "npm run run-ts -- src/wab/server/scripts/upgrade-stale-bundle.ts", "email:host": "cd src/wab/server/emails/host && vite --port 54789", "email:sync": "cd src/wab/server/emails/templates && plasmic sync -p taNK5uwsoPrzfpYmBVwUwX --skip-upgrade-check", "email:generate": "cd src/wab/server/emails/host && vite-node ../test-email.mts", "prepare": "playwright install chromium", "eslint-all": "yarn --cwd ../.. eslint platform/wab", "test": "jest --runInBand --forceExit", "test:coverage": "jest --coverage", "test:inspect": "node --inspect-brk node_modules/.bin/jest --runInBand", "backend": "bash tools/backend-server.bash", "backend:debug": "debug=1 bash tools/backend-server.bash", "gen:models": "npm run run-ts -- tools/gen-models.ts", "gen:component-metas": "npm run run-ts -- tools/gen-react-meta.ts", "gen:plasmic-tokens-sass": "npm run run-ts -- tools/gen-theo.ts src/wab/styles/plasmic-tokens.theo.json src/wab/styles/_tokens.sass", "gen:plasmic-tokens-ts": "npm run run-ts -- tools/gen-theo.ts src/wab/styles/plasmic-tokens.theo.json src/wab/styles/_tokens.ts", "start-backend": "pm2 start ts-node -i 1 --wait-ready --listen-timeout 19999 --kill-timeout 7999 -- -T -P tsconfig.tools.json src/wab/server/main.ts", "perftool": "npm run run-ts -- tools/perf.ts", "build-css": "sass src/wab/styles/canvas/:dev-build/static/styles/canvas/", "watch-css": "npm run build-css && sass src/wab/styles/canvas/:dev-build/static/styles/canvas/ -w", "seed": "npm run run-ts -- src/wab/server/db/DbInit.ts", "clean-svg": "npm run run-ts -- tools/clean-svg.ts", "plume:dev": "npm run run-ts -- src/wab/server/pkg-mgr/plume-pkg-mgr.ts", "typeorm": "npm run run-ts -- ./node_modules/.bin/typeorm", "tutorialdb": "npm run run-ts -- src/wab/server/tutorialdb/tutorialdb-cli.ts", "run-ts": "bash tools/run.bash", "dedupe-aria": "npx yarn-deduplicate@latest --scopes @react-aria @react-stately @react-types @internationalized && npx yarn-deduplicate@latest --packages react-aria react-stately @swc/helpers", "upgrade-aria": "yarn upgrade --latest react-aria react-stately && yarn dedupe-aria", "plasmic:sync": "npx plasmic sync", "plasmic:watch": "plasmic watch", "test:update-snapshots": "jest --updateSnapshot --env=jsdom", "storybook": "NO_ESLINT=1 NO_TYPECHECK=1 NODE_OPTIONS='--max-old-space-size=12288 --max_old_space_size=12288' storybook dev --port 6006", "storybook:build": "NO_ESLINT=1 NO_TYPECHECK=1 NODE_OPTIONS='--max-old-space-size=12288 --max_old_space_size=12288' storybook build", "storybook:start": "http-server storybook-static --port 6006", "storybook:test": "test-storybook --maxWorkers=2", "storybook:test:ci": "concurrently -k -s first -n \"SB,TEST\" -c \"magenta,blue\" \"yarn storybook:build && yarn storybook:start\" \"playwright install && wait-on tcp:127.0.0.1:6006 && yarn storybook:test\""}, "author": "Plasmic Team", "gitHead": "14cfee473c6a27212c54d536a8bd531c84e9fb68", "devDependencies": {"@cypress/webpack-preprocessor": "^5.4.1", "@plasmicapp/cli": "^0.1.338", "@rsbuild/core": "0.3.7", "@rsbuild/plugin-react": "0.3.7", "@rspack/core": "0.5.3", "@storybook/addon-essentials": "^7.5.0", "@storybook/addon-interactions": "^7.5.0", "@storybook/addon-links": "^7.5.0", "@storybook/addon-onboarding": "^1.0.8", "@storybook/jest": "^0.2.3", "@storybook/preset-create-react-app": "^7.5.0", "@storybook/react": "^7.5.0", "@storybook/react-webpack5": "^7.5.0", "@storybook/test-runner": "^0.13.0", "@storybook/testing-library": "^0.2.2", "@storybook/types": "^7.5.2", "@sucrase/jest-plugin": "^3.0.0", "@sucrase/webpack-loader": "^2.0.0", "@swc/helpers": "^0.4.14", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/async": "^3.2.3", "@types/bcrypt": "^3.0.0", "@types/body-parser": "^1.19.0", "@types/chroma-js": "^2.0.0", "@types/chrome": "^0.0.123", "@types/cryptr": "^4.0.1", "@types/css-tree": "^2.3.10", "@types/errorhandler": "^1.5.0", "@types/escodegen": "^0.0.7", "@types/estree": "^1.0.4", "@types/express": "^4.17.7", "@types/express-fileupload": "^1.2.2", "@types/express-session": "^1.17.0", "@types/glob": "^7.1.3", "@types/globalize": "^1.5.0", "@types/history": "^4.7.6", "@types/inquirer": "^9.0.3", "@types/jest": "^26.0.4", "@types/jquery": "^3.5.0", "@types/json-logic-js": "^2.0.1", "@types/lodash": "^4.14.157", "@types/lusca": "^1.6.2", "@types/mime-types": "^2.1.1", "@types/morgan": "^1.9.1", "@types/mousetrap": "^1.6.3", "@types/node": "^18.19.75", "@types/node-fetch": "^2.5.7", "@types/nodemailer": "^6.4.0", "@types/passport": "^1.0.16", "@types/passport-google-oauth20": "^2.0.11", "@types/passport-local": "^1.0.38", "@types/passport-oauth2": "^1.4.17", "@types/passport-strategy": "^0.2.38", "@types/pg": "^7.14.4", "@types/platform": "^1.3.2", "@types/pluralize": "^0.0.29", "@types/react-beautiful-dnd": "^13.0.0", "@types/react-csv": "^1.1.3", "@types/react-dom": "^18.3.5", "@types/react-helmet": "^6.0.0", "@types/react-inspector": "^4.0.1", "@types/react-router-dom": "^5.2.0", "@types/react-virtualized-auto-sizer": "^1.0.0", "@types/react-window": "^1.8.2", "@types/resize-observer-browser": "^0.1.5", "@types/sharp": "^0.31.1", "@types/signals": "^1.0.1", "@types/socket.io-client": "^1.4.34", "@types/tmp": "^0.2.0", "@types/underscore": "^1.10.9", "@types/underscore.string": "^0.0.38", "@types/url-join": "^4.0.0", "@types/uuid": "^8.0.0", "@types/validator": "^13.1.0", "@types/workerpool": "^6.0.0", "@vitejs/plugin-react": "^4.4.1", "babel-jest": "^29.7.0", "concurrently": "^5.3.0", "cypress": "^13.4.0", "cypress-fail-fast": "^7.1.1", "cypress-log-to-output": "^1.1.2", "cypress-mouse-position": "^1.0.0", "cypress-on-fix": "^1.0.3", "cypress-real-events": "^1.13.0", "cypress-split": "^1.24.7", "enzyme": "^3.11.0", "enzyme-to-json": "^3.6.2", "graphql-ws": "5.16.0", "http-server": "^0.12.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-extended": "^0.11.5", "jest-mock-extended": "^3.0.5", "jest-watch-typeahead": "^2.2.2", "monaco-editor-webpack-plugin": "7.1.0", "pegjs": "~0.10.0", "pegjs-coffee-plugin": "~0.3.0", "prando": "^6.0.1", "storybook": "^7.5.0", "sucrase": "^3.35.0", "ts-node": "^8.10.2", "type-fest": "^4.15.0", "vite": "^6.3.5", "vite-node": "^3.1.3", "wait-on": "^5.2.0", "whatwg-fetch": "^3.6.20"}, "dependenciesComments": {"@ant-design/pro-components": "Must be 2.6.4. Earlier doesn't support newer antd, later incurs a disallowed dynamic require of antd/es/layout/Sider. https://app.shortcut.com/plasmic/story/37043/richlayout-always-initially-loads-with-dark-background"}, "dependencies": {"@adobe/css-tools": "^4.4.2", "@amplitude/analytics-browser": "2.11.1", "@amplitude/analytics-node": "1.3.6", "@apidevtools/swagger-parser": "^10.0.2", "@aws-sdk/client-dynamodb": "^3.319.0", "@babel/core": "^7.23.2", "@babel/generator": "^7.23.0", "@babel/parser": "^7.23.0", "@babel/plugin-proposal-decorators": "^7.23.6", "@babel/preset-env": "^7.23.6", "@babel/preset-typescript": "^7.23.3", "@babel/types": "^7.23.0", "@clickhouse/client": "^0.2.1", "@figma-plugin/helpers": "^0.15.1", "@fortawesome/fontawesome": "^1.1.8", "@fortawesome/fontawesome-free": "^5.14.0", "@fortawesome/fontawesome-svg-core": "^1.2.30", "@fortawesome/free-regular-svg-icons": "^5.14.0", "@fortawesome/free-solid-svg-icons": "^5.14.0", "@fortawesome/react-fontawesome": "^0.1.11", "@godaddy/terminus": "^4.12.1", "@graphiql/plugin-explorer": "3.2.3", "@graphiql/react": "0.26.2", "@graphiql/toolkit": "0.11.1", "@js-temporal/polyfill": "^0.3.0", "@octokit/app": "^13.0.11", "@octokit/core": "^4.1.0", "@octokit/plugin-paginate-rest": "^5.0.1", "@okta/jwt-verifier": "^2.6.0", "@pankod/refine-core": "^3.58.1", "@pankod/refine-supabase": "^4.3.0", "@plasmicapp/data-sources": "^0.1.182", "@plasmicapp/data-sources-context": "0.1.21", "@plasmicapp/host": "^1.0.218", "@plasmicapp/loader-react": "^1.0.387", "@plasmicapp/react-web": "^0.2.388", "@plasmicpkgs/antd5": "^0.0.289", "@plasmicpkgs/commerce-local": "^0.0.216", "@plasmicpkgs/plasmic-basic-components": "^0.0.245", "@plasmicpkgs/plasmic-embed-css": "^0.1.203", "@plasmicpkgs/react-aria": "^0.0.137", "@popperjs/core": "2.11.6", "@qualifyze/airtable-formulator": "^1.3.1", "@react-awesome-query-builder/antd": "^6.1.2", "@react-email/components": "^0.0.38", "@sentry/browser": "^6.6.0", "@sentry/integrations": "^6.6.0", "@sentry/node": "^6.6.0", "@sentry/tracing": "^6.6.0", "@simonwep/pickr": "^1.8.0", "@stripe/react-stripe-js": "^1.4.1", "@stripe/stripe-js": "^1.16.0", "@supabase/supabase-js": "^2.38.4", "@tinymce/tinymce-react": "^4.3.2", "@types/react": "^18.3.18", "@xmldom/xmldom": "^0.8.10", "@xyflow/react": "^12.0.3", "@zxcvbn-ts/core": "^3.0.4", "@zxcvbn-ts/language-common": "^3.0.4", "@zxcvbn-ts/language-en": "^3.0.2", "@zxcvbn-ts/matcher-pwned": "^3.0.4", "JSONStream": "^1.3.5", "acorn": "^8.10.0", "acorn-walk": "^8.2.0", "airtable": "0.11.5", "antd": "^4.24.14", "async": "^3.2.0", "async-mutex": "^0.4.0", "aws-sdk": "^2.1666.0", "axios": "^1.7.2", "bcrypt": "^5.1.0", "body-parser": "^1.19.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "chroma-js": "^2.1.0", "class-validator": "^0.14.0", "classnames": "^2.3.2", "cmd-ts": "^0.12.1", "coffeescript": "^2.5.1", "comlink": "^4.3.1", "commander": "^11.0.0", "connect-typeorm": "^1.1.4", "constate": "^3.3.2", "cookie-parser": "^1.4.6", "copy-to-clipboard": "^3.3.1", "core-js": "^3.32.0", "cors": "^2.8.5", "cryptr": "^6.0.2", "css-initials": "^0.3.1", "css-tree": "^3.1.0", "css.escape": "^1.5.1", "csv": "^6.3.1", "csv-parse": "^5.5.2", "dayjs": "^1.11.9", "debug": "2.6.9", "dom-align": "^1.12.0", "dotenv": "^8.2.0", "downscale": "^1.0.6", "downshift": "^6.1.9", "emoji-picker-react": "^4.9.3", "errorhandler": "^1.5.1", "esbuild": "^0.18.0", "esbuild-register": "^3.6.0", "escodegen": "^2.1.0", "execa": "^5.1.1", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-fileupload": "^1.2.1", "express-prom-bundle": "^6.4.1", "express-rate-limit": "^7.1.5", "express-session": "^1.17.1", "fast-stringify": "2.0.0", "file-type": "^16.3.0", "file-type-browser": "^1.0.0", "font-awesome": "^4.7.0", "framer-motion": "^7.6.7", "get-port": "^7.0.0", "glob": "^7.1.6", "globalize": "^1.5.0", "gpt3-tokenizer": "^1.1.5", "graphiql": "3.7.0", "graphql": "^16.7.1", "hibp": "^11.1.0", "history": "^4.9.0", "html2canvas": "^1.0.0-rc.7", "http-proxy": "^1.18.1", "https-browserify": "^1.0.0", "immer": "^10.0.2", "immutable": "4.3.0", "inquirer": "^9.2.9", "is-hotkey": "^0.1.6", "isomorphic-unfetch": "^3", "jquery": "~3.5.1", "jquery-serializejson": "^2.9.0", "js-cookie": "^3.0.1", "js-string-escape": "^1.0.1", "jsdom": "^22.1.0", "jsonrepair": "^2.2.1", "jsonwebtoken": "^9.0.1", "keyboardjs": "^2.6.3", "lmdb": "^2.8.4", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lusca": "^1.6.1", "memoize-one": "^6.0.0", "mime": "^2.4.6", "mime-types": "^2.1.35", "mobx": "6.13.6", "mobx-react": "7.6.0", "mobx-utils": "6.1.0", "mocha-reporter-gha": "^1.1.1", "moize": "^6.1.5", "moment": "^2.29.4", "monaco-editor": "0.50.0", "morgan": "^1.10.0", "mousetrap": "^1.6.5", "najax": "^1.0.4", "nanoid": "^3.3.6", "nanoid-dictionary": "^4.3.0", "node-cron": "^3.0.3", "node-fetch": "^2.6.1", "node-html-parser": "^3.3.3", "node-sql-parser": "^4.4.0", "nodemailer": "^6.4.10", "openai": "^4.98.0", "openapi-types": "^8.0.0", "os-browserify": "^0.3.0", "packrattle": "4.1.0", "parse-data-url": "^2.0.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "passport-oauth2": "^1.8.0", "passport-oauth2-refresh": "^2.2.0", "passport-okta-oauth20": "^1.1.0", "passport-strategy": "^1.0.0", "patch-package": "^8.0.0", "path-browserify": "^1.0.1", "path-to-regexp": "^1.7.0", "perfect-cursors": "^1.0.5", "pg": "^8.3.0", "pg-connection-string": "^2.6.2", "pgsql-ast-parser": "^11.0.1", "platform": "^1.3.6", "playwright": "1.38.1", "pluralize": "^8.0.0", "posthog-js": "1.234.9", "posthog-node": "4.11.1", "prettier": "2.8.8", "prism-react-renderer": "^1.1.1", "prismjs": "^1.29.0", "private-ip": "^3.0.1", "prom-client": "^14.0.1", "random": "^4.1.0", "react": "^18.3.1", "react-aria": "3.40.0", "react-beautiful-dnd": "^13.0.0", "react-confetti": "^6.1.0", "react-csv": "^2.2.2", "react-dom": "^18.3.1", "react-draggable": "^4.4.3", "react-error-boundary": "^3.1.3", "react-helmet": "^6.1.0", "react-hook-form": "^8.0.0-alpha.4", "react-icons": "^3.10.0", "react-inspector": "^5.1.1", "react-joyride": "^2.5.3", "react-keyed-flatten-children": "^1.3.0", "react-markdown": "^8.0.1", "react-monaco-editor": "0.55.0", "react-router": "5.2.0", "react-router-dom": "^5.2.0", "react-spring": "^9.5.5", "react-stately": "3.38.0", "react-use": "^15.3.2", "react-use-hoverintent": "^1.2.3", "react-virtualized-auto-sizer": "^1.0.2", "react-window": "^1.8.6", "rebound": "^0.1.0", "recharts": "^2.1.10", "rectangle-overlap": "^2.0.0", "regex": "^4.1.3", "regexp.execall": "^1.0.2", "remark-gfm": "^3.0.1", "resize-observer-polyfill": "^1.5.1", "safe-stable-stringify": "^2.4.3", "sass": "^1.63.4", "semver": "^6.3.0", "sharp": "^0.31.3", "shellsync": "^0.2.2", "short-uuid": "^3.1.1", "signals": "^1.0.0", "slate": "^0.81.1", "slate-history": "^0.66.0", "slate-react": "^0.81.0", "socket.io": "^4.6.2", "socket.io-client": "^4.6.1", "specificity": "^1.0.0", "sql-highlight": "^4.3.2", "sqlstring": "^2.3.3", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "strip-css-comments": "^4.1.0", "stripe": "^8.167.0", "svgo": "^3.3.2", "swr": "^2.2.0", "tinymce": "^6.8.0", "tldts": "^6.0.12", "tmp": "^0.2.1", "tqdm": "^2.0.3", "transformation-matrix": "^2.4.0", "ts-adt": "^2.1.2", "ts-failable": "^0.6.1", "tunnel-rat": "^0.1.2", "typeorm": "0.2.45", "typeorm-naming-strategies": "^4.1.0", "typescript": "5.7.3", "typia": "^3.8.1", "underscore": "^1.10.2", "underscore.string": "~3.3.5", "url": "0.11.3", "url-join": "^4.0.1", "util": "^0.12.5", "uuid": "^8.2.0", "validator": "^13.1.1", "workerpool": "^6.1.4", "yargs": "^16.2.0", "zod": "^3.8.1"}, "resolutionsComments": {"ini": "CVE-2020-7788 for 1.3.5"}, "resolutions": {"ini": "1.3.6", "react-aria-components": "1.2.0"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "browser": {"crypto": false}, "nohoist": ["**/babel-preset-react-app/@babel/runtime"]}