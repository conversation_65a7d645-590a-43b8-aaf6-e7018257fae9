[["hfGs6yvc1pDQRRQfv3DRQn", {"root": "52785001", "map": {"1511001": {"rows": [{"__ref": "1511002"}], "__type": "ArenaFrameGrid"}, "1511002": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "1511003": {"rows": [{"__ref": "1511004"}], "__type": "ArenaFrameGrid"}, "1511004": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "1511005": {"rows": [{"__ref": "1511006"}], "__type": "ArenaFrameGrid"}, "1511006": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "46694001": {"uuid": "foANVfsbRH", "name": "Custom Page", "params": [], "states": [], "tplTree": {"__ref": "46694003"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "46694004"}], "variantGroups": [], "pageMeta": {"__ref": "46694005"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "46694002": {"component": {"__ref": "46694001"}, "matrix": {"__ref": "46694006"}, "customMatrix": {"__ref": "1511003"}, "__type": "PageArena"}, "46694003": {"tag": "div", "name": null, "children": [{"__ref": "48582011"}, {"__ref": "48582038"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "DhYJ_sW7n", "parent": null, "locked": null, "vsettings": [{"__ref": "46694007"}, {"__ref": "48582024"}], "__type": "TplTag"}, "46694004": {"uuid": "gZkEH4jNFp", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "46694005": {"path": "/custom", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "46694006": {"rows": [{"__ref": "46694009"}, {"__ref": "48582023"}], "__type": "ArenaFrameGrid"}, "46694007": {"variants": [{"__ref": "46694004"}], "args": [], "attrs": {}, "rs": {"__ref": "46694010"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "46694009": {"cols": [{"__ref": "46694011"}, {"__ref": "46694012"}], "rowKey": {"__ref": "46694004"}, "__type": "ArenaFrameRow"}, "46694010": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "46694011": {"frame": {"__ref": "46694020"}, "cellKey": null, "__type": "ArenaFrameCell"}, "46694012": {"frame": {"__ref": "46694021"}, "cellKey": null, "__type": "ArenaFrameCell"}, "46694020": {"uuid": "JT3ZTby1_q", "width": 1366, "height": 768, "container": {"__ref": "46694022"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "46694004"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "46694021": {"uuid": "nO9Gmu-gN-", "width": 414, "height": 736, "container": {"__ref": "46694023"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "46694004"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "46694022": {"name": null, "component": {"__ref": "46694001"}, "uuid": "jEY73UPlyT", "parent": null, "locked": null, "vsettings": [{"__ref": "46694024"}], "__type": "TplComponent"}, "46694023": {"name": null, "component": {"__ref": "46694001"}, "uuid": "XsRTz4lAWc", "parent": null, "locked": null, "vsettings": [{"__ref": "46694025"}], "__type": "TplComponent"}, "46694024": {"variants": [{"__ref": "52785007"}], "args": [], "attrs": {}, "rs": {"__ref": "46694026"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "46694025": {"variants": [{"__ref": "52785007"}], "args": [], "attrs": {}, "rs": {"__ref": "46694027"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "46694026": {"values": {}, "mixins": [], "__type": "RuleSet"}, "46694027": {"values": {}, "mixins": [], "__type": "RuleSet"}, "48582001": {"type": "global-user-defined", "param": {"__ref": "48582003"}, "uuid": "Pe6m_TdsJY", "variants": [{"__ref": "48582004"}], "multi": false, "__type": "GlobalVariantGroup"}, "48582002": {"uuid": "zYsIRlfDl8", "name": "UTM Campaign", "splitType": "segment", "slices": [{"__ref": "48582005"}, {"__ref": "48582006"}], "status": "running", "targetEvents": ["track-conversion"], "description": null, "externalId": "ext-utm-campaign", "__type": "Split"}, "48582003": {"type": {"__ref": "48582008"}, "variable": {"__ref": "48582007"}, "uuid": "3-uwnWC-uz", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "48582004": {"uuid": "zmu1XaNIKX", "name": "With 22 campaign", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "48582001"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "48582005": {"cond": "{}", "uuid": "ISIbGXh3BF", "name": "A", "externalId": "ext-utm-campaign-a", "contents": [], "__type": "SegmentSplitSlice"}, "48582006": {"cond": "{\"__logic\":{\"and\":[{\"==\":[{\"var\":\"utm_campaign\"},\"myfirstcampaign\"]}]}}", "uuid": "pNc0c1kmpo", "name": "B", "externalId": "ext-utm-campaign-b", "contents": [{"__ref": "48582010"}], "__type": "SegmentSplitSlice"}, "48582007": {"name": "UTM Campaign", "uuid": "H02qfPGEU", "__type": "Var"}, "48582008": {"name": "text", "__type": "Text"}, "48582010": {"group": {"__ref": "48582001"}, "variant": {"__ref": "48582004"}, "__type": "GlobalVariantSplitContent"}, "48582011": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "tFuyLbC36z", "parent": {"__ref": "46694003"}, "locked": null, "vsettings": [{"__ref": "48582012"}, {"__ref": "48582036"}], "__type": "TplTag"}, "48582012": {"variants": [{"__ref": "46694004"}], "args": [], "attrs": {}, "rs": {"__ref": "48582013"}, "dataCond": null, "dataRep": null, "text": {"__ref": "48582022"}, "columnsConfig": null, "__type": "VariantSetting"}, "48582013": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px", "font-size": "72px", "text-align": "center"}, "mixins": [], "__type": "RuleSet"}, "48582022": {"markers": [], "text": "This is my campaign page", "__type": "RawText"}, "48582023": {"cols": [{"__ref": "48582025"}, {"__ref": "48582026"}], "rowKey": {"__ref": "48582004"}, "__type": "ArenaFrameRow"}, "48582024": {"variants": [{"__ref": "48582004"}], "args": [], "attrs": {}, "rs": {"__ref": "48582027"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "48582025": {"frame": {"__ref": "48582028"}, "cellKey": null, "__type": "ArenaFrameCell"}, "48582026": {"frame": {"__ref": "48582029"}, "cellKey": null, "__type": "ArenaFrameCell"}, "48582027": {"values": {}, "mixins": [], "__type": "RuleSet"}, "48582028": {"uuid": "8CtG0FAuk", "width": 1366, "height": 768, "container": {"__ref": "48582030"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [], "pinnedGlobalVariants": {"zmu1XaNIKX": true}, "targetGlobalVariants": [{"__ref": "48582004"}], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "48582029": {"uuid": "kr6S-MGqQM", "width": 414, "height": 736, "container": {"__ref": "48582031"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "46694004"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [{"__ref": "48582004"}], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "48582030": {"name": null, "component": {"__ref": "46694001"}, "uuid": "Ofi_EgY7ne", "parent": null, "locked": null, "vsettings": [{"__ref": "48582032"}], "__type": "TplComponent"}, "48582031": {"name": null, "component": {"__ref": "46694001"}, "uuid": "eLWIxJVPqL", "parent": null, "locked": null, "vsettings": [{"__ref": "48582033"}], "__type": "TplComponent"}, "48582032": {"variants": [{"__ref": "52785007"}], "args": [], "attrs": {}, "rs": {"__ref": "48582034"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "48582033": {"variants": [{"__ref": "52785007"}], "args": [], "attrs": {}, "rs": {"__ref": "48582035"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "48582034": {"values": {}, "mixins": [], "__type": "RuleSet"}, "48582035": {"values": {}, "mixins": [], "__type": "RuleSet"}, "48582036": {"variants": [{"__ref": "48582004"}], "args": [], "attrs": {}, "rs": {"__ref": "48582037"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "48582037": {"values": {}, "mixins": [], "__type": "RuleSet"}, "48582038": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "J-D3bqjYN", "parent": {"__ref": "46694003"}, "locked": null, "vsettings": [{"__ref": "48582039"}, {"__ref": "48582040"}], "__type": "TplTag"}, "48582039": {"variants": [{"__ref": "46694004"}], "args": [], "attrs": {}, "rs": {"__ref": "48582041"}, "dataCond": {"__ref": "48582062"}, "dataRep": null, "text": {"__ref": "48582067"}, "columnsConfig": null, "__type": "VariantSetting"}, "48582040": {"variants": [{"__ref": "48582004"}], "args": [], "attrs": {}, "rs": {"__ref": "48582044"}, "dataCond": {"__ref": "48582045"}, "dataRep": null, "text": {"__ref": "48582066"}, "columnsConfig": null, "__type": "VariantSetting"}, "48582041": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px", "font-size": "48px", "plasmic-display-none": "false", "text-align": "center"}, "mixins": [], "__type": "RuleSet"}, "48582044": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "48582045": {"code": "true", "fallback": null, "__type": "CustomCode"}, "48582062": {"code": "true", "fallback": null, "__type": "CustomCode"}, "48582066": {"markers": [], "text": "You are seeing a campaign segment", "__type": "RawText"}, "48582067": {"markers": [], "text": "NO CAMPAIGN HERE", "__type": "RawText"}, "52785001": {"components": [{"__ref": "60943001"}, {"__ref": "46694001"}, {"__ref": "61747001"}], "arenas": [], "pageArenas": [{"__ref": "60943002"}, {"__ref": "46694002"}, {"__ref": "61747002"}], "componentArenas": [], "globalVariantGroups": [{"__ref": "52785003"}, {"__ref": "60943045"}, {"__ref": "60943055"}, {"__ref": "60943133"}, {"__ref": "48582001"}], "userManagedFonts": [], "globalVariant": {"__ref": "52785007"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "52785009"}], "activeTheme": {"__ref": "52785009"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "52785003"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [{"__ref": "60943046"}, {"__ref": "60943056"}, {"__ref": "60943134"}, {"__ref": "48582002"}], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "52785003": {"type": "global-screen", "param": {"__ref": "52785004"}, "uuid": "fX01k1ByS1w", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "52785004": {"type": {"__ref": "52785006"}, "variable": {"__ref": "52785005"}, "uuid": "CVsBgyZuGOg", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "52785005": {"name": "Screen", "uuid": "sB24w6Klz6", "__type": "Var"}, "52785006": {"name": "text", "__type": "Text"}, "52785007": {"uuid": "llt5CEK-9dL", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "52785009": {"defaultStyle": {"__ref": "52785010"}, "styles": [{"__ref": "52785025"}, {"__ref": "52785034"}, {"__ref": "52785043"}, {"__ref": "52785052"}, {"__ref": "52785061"}, {"__ref": "52785070"}, {"__ref": "52785078"}, {"__ref": "52785082"}, {"__ref": "52785086"}, {"__ref": "52785094"}, {"__ref": "52785119"}, {"__ref": "52785144"}, {"__ref": "52785155"}], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "52785010": {"name": "Default Typography", "rs": {"__ref": "52785011"}, "preview": null, "uuid": "NQCUTd4zNb", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "52785011": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "52785025": {"selector": "h1", "style": {"__ref": "52785026"}, "__type": "ThemeStyle"}, "52785026": {"name": "Default \"h1\"", "rs": {"__ref": "52785027"}, "preview": null, "uuid": "N6HxN5QIto", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "52785027": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "52785034": {"selector": "h2", "style": {"__ref": "52785035"}, "__type": "ThemeStyle"}, "52785035": {"name": "Default \"h2\"", "rs": {"__ref": "52785036"}, "preview": null, "uuid": "uuVtjf5pn_", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "52785036": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "52785043": {"selector": "h3", "style": {"__ref": "52785044"}, "__type": "ThemeStyle"}, "52785044": {"name": "Default \"h3\"", "rs": {"__ref": "52785045"}, "preview": null, "uuid": "El4iT0uUf9", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "52785045": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "52785052": {"selector": "h4", "style": {"__ref": "52785053"}, "__type": "ThemeStyle"}, "52785053": {"name": "Default \"h4\"", "rs": {"__ref": "52785054"}, "preview": null, "uuid": "8Ph6Iu0hd8", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "52785054": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "52785061": {"selector": "h5", "style": {"__ref": "52785062"}, "__type": "ThemeStyle"}, "52785062": {"name": "Default \"h5\"", "rs": {"__ref": "52785063"}, "preview": null, "uuid": "sl8YOZuVKE", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "52785063": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "52785070": {"selector": "h6", "style": {"__ref": "52785071"}, "__type": "ThemeStyle"}, "52785071": {"name": "Default \"h6\"", "rs": {"__ref": "52785072"}, "preview": null, "uuid": "LP_PE3JLoi", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "52785072": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "52785078": {"selector": "a", "style": {"__ref": "52785079"}, "__type": "ThemeStyle"}, "52785079": {"name": "Default \"a\"", "rs": {"__ref": "52785080"}, "preview": null, "uuid": "tnboqtHNxa", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "52785080": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "52785082": {"selector": "a:hover", "style": {"__ref": "52785083"}, "__type": "ThemeStyle"}, "52785083": {"name": "Default \"a:hover\"", "rs": {"__ref": "52785084"}, "preview": null, "uuid": "wCiGiPGuGS", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "52785084": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "52785086": {"selector": "blockquote", "style": {"__ref": "52785087"}, "__type": "ThemeStyle"}, "52785087": {"name": "Default \"blockquote\"", "rs": {"__ref": "52785088"}, "preview": null, "uuid": "dvbYHwaTnR", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "52785088": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "52785094": {"selector": "code", "style": {"__ref": "52785095"}, "__type": "ThemeStyle"}, "52785095": {"name": "Default \"code\"", "rs": {"__ref": "52785096"}, "preview": null, "uuid": "WHEvNTR0dy", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "52785096": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "52785119": {"selector": "pre", "style": {"__ref": "52785120"}, "__type": "ThemeStyle"}, "52785120": {"name": "Default \"pre\"", "rs": {"__ref": "52785121"}, "preview": null, "uuid": "cefTSzE2hn", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "52785121": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "52785144": {"selector": "ol", "style": {"__ref": "52785145"}, "__type": "ThemeStyle"}, "52785145": {"name": "Default \"ol\"", "rs": {"__ref": "52785146"}, "preview": null, "uuid": "79hq_BpbTM", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "52785146": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "52785155": {"selector": "ul", "style": {"__ref": "52785156"}, "__type": "ThemeStyle"}, "52785156": {"name": "Default \"ul\"", "rs": {"__ref": "52785157"}, "preview": null, "uuid": "oL90p1bQp6", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "52785157": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "60943001": {"uuid": "fyA8endFB4", "name": "HomePage", "params": [], "states": [], "tplTree": {"__ref": "60943003"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "60943004"}], "variantGroups": [], "pageMeta": {"__ref": "60943005"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "60943002": {"component": {"__ref": "60943001"}, "matrix": {"__ref": "60943006"}, "customMatrix": {"__ref": "1511001"}, "__type": "PageArena"}, "60943003": {"tag": "div", "name": null, "children": [{"__ref": "60943028"}, {"__ref": "60943078"}, {"__ref": "60943086"}, {"__ref": "60943087"}, {"__ref": "60943102"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "P8h1dIXqC", "parent": null, "locked": null, "vsettings": [{"__ref": "60943007"}, {"__ref": "60943076"}, {"__ref": "60943118"}, {"__ref": "60943144"}], "__type": "TplTag"}, "60943004": {"uuid": "Z9i4N7eLz8", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "60943005": {"path": "/", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "60943006": {"rows": [{"__ref": "60943009"}, {"__ref": "60943117"}, {"__ref": "60943065"}, {"__ref": "60943143"}], "__type": "ArenaFrameGrid"}, "60943007": {"variants": [{"__ref": "60943004"}], "args": [], "attrs": {}, "rs": {"__ref": "60943010"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "60943009": {"cols": [{"__ref": "60943011"}, {"__ref": "60943012"}], "rowKey": {"__ref": "60943004"}, "__type": "ArenaFrameRow"}, "60943010": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "60943011": {"frame": {"__ref": "60943020"}, "cellKey": null, "__type": "ArenaFrameCell"}, "60943012": {"frame": {"__ref": "60943021"}, "cellKey": null, "__type": "ArenaFrameCell"}, "60943020": {"uuid": "iGl03Fkfr2", "width": 1366, "height": 768, "container": {"__ref": "60943022"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "60943004"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "60943021": {"uuid": "6Rk9_2t-8O", "width": 414, "height": 736, "container": {"__ref": "60943023"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "60943004"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "60943022": {"name": null, "component": {"__ref": "60943001"}, "uuid": "qhj4dhezxK", "parent": null, "locked": null, "vsettings": [{"__ref": "60943024"}], "__type": "TplComponent"}, "60943023": {"name": null, "component": {"__ref": "60943001"}, "uuid": "KxpqgEnwLT", "parent": null, "locked": null, "vsettings": [{"__ref": "60943025"}], "__type": "TplComponent"}, "60943024": {"variants": [{"__ref": "52785007"}], "args": [], "attrs": {}, "rs": {"__ref": "60943026"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "60943025": {"variants": [{"__ref": "52785007"}], "args": [], "attrs": {}, "rs": {"__ref": "60943027"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "60943026": {"values": {}, "mixins": [], "__type": "RuleSet"}, "60943027": {"values": {}, "mixins": [], "__type": "RuleSet"}, "60943028": {"tag": "div", "name": null, "children": [{"__ref": "60943037"}], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "PSM_VupQFT", "parent": {"__ref": "60943003"}, "locked": null, "vsettings": [{"__ref": "60943029"}], "__type": "TplTag"}, "60943029": {"variants": [{"__ref": "60943004"}], "args": [], "attrs": {}, "rs": {"__ref": "60943030"}, "dataCond": null, "dataRep": null, "text": {"__ref": "60943043"}, "columnsConfig": null, "__type": "VariantSetting"}, "60943030": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px"}, "mixins": [], "__type": "RuleSet"}, "60943037": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "Ak2eZZrrH9", "parent": {"__ref": "60943028"}, "locked": null, "vsettings": [{"__ref": "60943039"}, {"__ref": "60943163"}], "__type": "TplTag"}, "60943039": {"variants": [{"__ref": "60943004"}], "args": [], "attrs": {}, "rs": {"__ref": "60943040"}, "dataCond": null, "dataRep": null, "text": {"__ref": "60943042"}, "columnsConfig": null, "__type": "VariantSetting"}, "60943040": {"values": {}, "mixins": [], "__type": "RuleSet"}, "60943042": {"markers": [], "text": "Split testing page", "__type": "RawText"}, "60943043": {"markers": [{"__ref": "60943044"}], "text": "[child]", "__type": "RawText"}, "60943044": {"tpl": {"__ref": "60943037"}, "position": 0, "length": 7, "__type": "NodeMarker"}, "60943045": {"type": "global-user-defined", "param": {"__ref": "60943047"}, "uuid": "DvqO1ccX7w", "variants": [{"__ref": "60943048"}], "multi": false, "__type": "GlobalVariantGroup"}, "60943046": {"uuid": "Tjml6TQBIr", "name": "Segment", "splitType": "segment", "slices": [{"__ref": "60943049"}, {"__ref": "60943050"}], "status": "running", "targetEvents": ["track-conversion"], "description": null, "externalId": "", "__type": "Split"}, "60943047": {"type": {"__ref": "60943052"}, "variable": {"__ref": "60943051"}, "uuid": "mlpHnPsNo6", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "60943048": {"uuid": "7sAJY_aAWw", "name": "variation", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "60943045"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "60943049": {"cond": "{}", "uuid": "zhCpR2oaJ3", "name": "A", "externalId": "", "contents": [], "__type": "SegmentSplitSlice"}, "60943050": {"cond": "{\"__logic\":{\"and\":[{\"==\":[{\"var\":\"str\"},\"magic\"]}, {\"<=\": [{\"var\": \"num\"}, 30]}, {\"==\": [{\"var\": \"active\"}, true]} ]}}", "uuid": "2imyA2A88G", "name": "B", "externalId": "", "contents": [{"__ref": "60943054"}], "__type": "SegmentSplitSlice"}, "60943051": {"name": "Segment", "uuid": "489153iT9", "__type": "Var"}, "60943052": {"name": "text", "__type": "Text"}, "60943054": {"group": {"__ref": "60943045"}, "variant": {"__ref": "60943048"}, "__type": "GlobalVariantSplitContent"}, "60943055": {"type": "global-user-defined", "param": {"__ref": "60943057"}, "uuid": "Bm5rQWCfj8", "variants": [{"__ref": "60943058"}], "multi": false, "__type": "GlobalVariantGroup"}, "60943056": {"uuid": "j7cCxfS-Vu", "name": "Experiment", "splitType": "experiment", "slices": [{"__ref": "60943059"}, {"__ref": "60943060"}], "status": "running", "targetEvents": ["track-conversion"], "description": null, "externalId": "ext-experiment", "__type": "Split"}, "60943057": {"type": {"__ref": "60943062"}, "variable": {"__ref": "60943061"}, "uuid": "ak88QpezFC", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "60943058": {"uuid": "qhJ31GHsxN", "name": "variation", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "60943055"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "60943059": {"prob": 50, "uuid": "I4hoVVeME_", "name": "A", "externalId": "ext-experiment-a", "contents": [], "__type": "RandomSplitSlice"}, "60943060": {"prob": 50, "uuid": "6z5_V8jUij", "name": "B", "externalId": "ext-experiment-b", "contents": [{"__ref": "60943064"}], "__type": "RandomSplitSlice"}, "60943061": {"name": "Experiment", "uuid": "VYf7rJ73z", "__type": "Var"}, "60943062": {"name": "text", "__type": "Text"}, "60943064": {"group": {"__ref": "60943055"}, "variant": {"__ref": "60943058"}, "__type": "GlobalVariantSplitContent"}, "60943065": {"cols": [{"__ref": "60943066"}, {"__ref": "60943067"}], "rowKey": {"__ref": "60943058"}, "__type": "ArenaFrameRow"}, "60943066": {"frame": {"__ref": "60943068"}, "cellKey": null, "__type": "ArenaFrameCell"}, "60943067": {"frame": {"__ref": "60943069"}, "cellKey": null, "__type": "ArenaFrameCell"}, "60943068": {"uuid": "jd1WjqKPv", "width": 1366, "height": 768, "container": {"__ref": "60943070"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "60943004"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [{"__ref": "60943058"}], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "60943069": {"uuid": "AtUXxl3zmv", "width": 414, "height": 736, "container": {"__ref": "60943071"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "60943004"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [{"__ref": "60943058"}], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "60943070": {"name": null, "component": {"__ref": "60943001"}, "uuid": "CEMs7hh7WS", "parent": null, "locked": null, "vsettings": [{"__ref": "60943072"}], "__type": "TplComponent"}, "60943071": {"name": null, "component": {"__ref": "60943001"}, "uuid": "xp3HgAh8Vr", "parent": null, "locked": null, "vsettings": [{"__ref": "60943073"}], "__type": "TplComponent"}, "60943072": {"variants": [{"__ref": "52785007"}], "args": [], "attrs": {}, "rs": {"__ref": "60943074"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "60943073": {"variants": [{"__ref": "52785007"}], "args": [], "attrs": {}, "rs": {"__ref": "60943075"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "60943074": {"values": {}, "mixins": [], "__type": "RuleSet"}, "60943075": {"values": {}, "mixins": [], "__type": "RuleSet"}, "60943076": {"variants": [{"__ref": "60943058"}], "args": [], "attrs": {}, "rs": {"__ref": "60943077"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "60943077": {"values": {}, "mixins": [], "__type": "RuleSet"}, "60943078": {"tag": "h2", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "S157cdSPX", "parent": {"__ref": "60943003"}, "locked": null, "vsettings": [{"__ref": "60943079"}, {"__ref": "60943111"}, {"__ref": "60943160"}], "__type": "TplTag"}, "60943079": {"variants": [{"__ref": "60943004"}], "args": [], "attrs": {}, "rs": {"__ref": "60943080"}, "dataCond": null, "dataRep": null, "text": {"__ref": "60943114"}, "columnsConfig": null, "__type": "VariantSetting"}, "60943080": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px"}, "mixins": [], "__type": "RuleSet"}, "60943086": {"tag": "h2", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "7ooKOiOkJ-", "parent": {"__ref": "60943003"}, "locked": null, "vsettings": [{"__ref": "60943088"}, {"__ref": "60943130"}], "__type": "TplTag"}, "60943087": {"tag": "h2", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "TBQD-74lJ", "parent": {"__ref": "60943003"}, "locked": null, "vsettings": [{"__ref": "60943089"}, {"__ref": "60943145"}, {"__ref": "60943166"}], "__type": "TplTag"}, "60943088": {"variants": [{"__ref": "60943004"}], "args": [], "attrs": {}, "rs": {"__ref": "60943090"}, "dataCond": null, "dataRep": null, "text": {"__ref": "60943115"}, "columnsConfig": null, "__type": "VariantSetting"}, "60943089": {"variants": [{"__ref": "60943004"}], "args": [], "attrs": {}, "rs": {"__ref": "60943092"}, "dataCond": null, "dataRep": null, "text": {"__ref": "60943116"}, "columnsConfig": null, "__type": "VariantSetting"}, "60943090": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px"}, "mixins": [], "__type": "RuleSet"}, "60943092": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px"}, "mixins": [], "__type": "RuleSet"}, "60943102": {"tag": "h2", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "1B3W346tI", "parent": {"__ref": "60943003"}, "locked": null, "vsettings": [{"__ref": "60943103"}], "__type": "TplTag"}, "60943103": {"variants": [{"__ref": "60943004"}], "args": [], "attrs": {}, "rs": {"__ref": "60943104"}, "dataCond": null, "dataRep": null, "text": {"__ref": "60943159"}, "columnsConfig": null, "__type": "VariantSetting"}, "60943104": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px"}, "mixins": [], "__type": "RuleSet"}, "60943111": {"variants": [{"__ref": "60943058"}], "args": [], "attrs": {}, "rs": {"__ref": "60943112"}, "dataCond": null, "dataRep": null, "text": {"__ref": "60943113"}, "columnsConfig": null, "__type": "VariantSetting"}, "60943112": {"values": {"color": "#00FF65"}, "mixins": [], "__type": "RuleSet"}, "60943113": {"markers": [], "text": "active experiment", "__type": "RawText"}, "60943114": {"markers": [], "text": "inactive experiment", "__type": "RawText"}, "60943115": {"markers": [], "text": "inactive segment", "__type": "RawText"}, "60943116": {"markers": [], "text": "inactive schedule", "__type": "RawText"}, "60943117": {"cols": [{"__ref": "60943119"}, {"__ref": "60943120"}], "rowKey": {"__ref": "60943048"}, "__type": "ArenaFrameRow"}, "60943118": {"variants": [{"__ref": "60943048"}], "args": [], "attrs": {}, "rs": {"__ref": "60943121"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "60943119": {"frame": {"__ref": "60943122"}, "cellKey": null, "__type": "ArenaFrameCell"}, "60943120": {"frame": {"__ref": "60943123"}, "cellKey": null, "__type": "ArenaFrameCell"}, "60943121": {"values": {}, "mixins": [], "__type": "RuleSet"}, "60943122": {"uuid": "ULtgtfhp4", "width": 1366, "height": 768, "container": {"__ref": "60943124"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "60943004"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [{"__ref": "60943048"}], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "60943123": {"uuid": "bvC_ofcHlb", "width": 414, "height": 736, "container": {"__ref": "60943125"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "60943004"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [{"__ref": "60943048"}], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "60943124": {"name": null, "component": {"__ref": "60943001"}, "uuid": "rOe26llLZU", "parent": null, "locked": null, "vsettings": [{"__ref": "60943126"}], "__type": "TplComponent"}, "60943125": {"name": null, "component": {"__ref": "60943001"}, "uuid": "K0W36sOKBs", "parent": null, "locked": null, "vsettings": [{"__ref": "60943127"}], "__type": "TplComponent"}, "60943126": {"variants": [{"__ref": "52785007"}], "args": [], "attrs": {}, "rs": {"__ref": "60943128"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "60943127": {"variants": [{"__ref": "52785007"}], "args": [], "attrs": {}, "rs": {"__ref": "60943129"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "60943128": {"values": {}, "mixins": [], "__type": "RuleSet"}, "60943129": {"values": {}, "mixins": [], "__type": "RuleSet"}, "60943130": {"variants": [{"__ref": "60943048"}], "args": [], "attrs": {}, "rs": {"__ref": "60943131"}, "dataCond": null, "dataRep": null, "text": {"__ref": "60943132"}, "columnsConfig": null, "__type": "VariantSetting"}, "60943131": {"values": {"color": "#FF0000"}, "mixins": [], "__type": "RuleSet"}, "60943132": {"markers": [], "text": "active segment", "__type": "RawText"}, "60943133": {"type": "global-user-defined", "param": {"__ref": "60943135"}, "uuid": "JQozHPAOj1", "variants": [{"__ref": "60943136"}], "multi": false, "__type": "GlobalVariantGroup"}, "60943134": {"uuid": "R4VDXPpPif", "name": "Schedule", "splitType": "schedule", "slices": [{"__ref": "60943137"}, {"__ref": "60943138"}], "status": "running", "targetEvents": ["track-conversion"], "description": null, "externalId": "", "__type": "Split"}, "60943135": {"type": {"__ref": "60943140"}, "variable": {"__ref": "60943139"}, "uuid": "cwvj6DeRsy", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "60943136": {"uuid": "J7KIiue2f8", "name": "variation", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "60943133"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "60943137": {"cond": "{}", "uuid": "XnC0rbVFqo", "name": "A", "externalId": null, "contents": [], "__type": "SegmentSplitSlice"}, "60943138": {"cond": "{\"<=\":[\"2000-01-01T02:00:27.046Z\",{\"var\":\"time\"},\"2100-01-01T02:59:40.115Z\"]}", "uuid": "62q5SoYGmQ", "name": "B", "externalId": null, "contents": [{"__ref": "60943142"}], "__type": "SegmentSplitSlice"}, "60943139": {"name": "Schedule", "uuid": "95GKW0QV3", "__type": "Var"}, "60943140": {"name": "text", "__type": "Text"}, "60943142": {"group": {"__ref": "60943133"}, "variant": {"__ref": "60943136"}, "__type": "GlobalVariantSplitContent"}, "60943143": {"cols": [{"__ref": "60943146"}, {"__ref": "60943147"}], "rowKey": {"__ref": "60943136"}, "__type": "ArenaFrameRow"}, "60943144": {"variants": [{"__ref": "60943136"}], "args": [], "attrs": {}, "rs": {"__ref": "60943148"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "60943145": {"variants": [{"__ref": "60943136"}], "args": [], "attrs": {}, "rs": {"__ref": "60943149"}, "dataCond": null, "dataRep": null, "text": {"__ref": "60943158"}, "columnsConfig": null, "__type": "VariantSetting"}, "60943146": {"frame": {"__ref": "60943150"}, "cellKey": null, "__type": "ArenaFrameCell"}, "60943147": {"frame": {"__ref": "60943151"}, "cellKey": null, "__type": "ArenaFrameCell"}, "60943148": {"values": {}, "mixins": [], "__type": "RuleSet"}, "60943149": {"values": {"color": "#3900FF"}, "mixins": [], "__type": "RuleSet"}, "60943150": {"uuid": "VABWFDa_k", "width": 1366, "height": 768, "container": {"__ref": "60943152"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "60943004"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [{"__ref": "60943136"}], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "60943151": {"uuid": "advZjlyvuN", "width": 414, "height": 736, "container": {"__ref": "60943153"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "60943004"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [{"__ref": "60943136"}], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "60943152": {"name": null, "component": {"__ref": "60943001"}, "uuid": "DFNXNO19yc", "parent": null, "locked": null, "vsettings": [{"__ref": "60943154"}], "__type": "TplComponent"}, "60943153": {"name": null, "component": {"__ref": "60943001"}, "uuid": "-NT3oVv6L_", "parent": null, "locked": null, "vsettings": [{"__ref": "60943155"}], "__type": "TplComponent"}, "60943154": {"variants": [{"__ref": "52785007"}], "args": [], "attrs": {}, "rs": {"__ref": "60943156"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "60943155": {"variants": [{"__ref": "52785007"}], "args": [], "attrs": {}, "rs": {"__ref": "60943157"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "60943156": {"values": {}, "mixins": [], "__type": "RuleSet"}, "60943157": {"values": {}, "mixins": [], "__type": "RuleSet"}, "60943158": {"markers": [], "text": "active schedule", "__type": "RawText"}, "60943159": {"markers": [], "text": "control", "__type": "RawText"}, "60943160": {"variants": [{"__ref": "60943048"}], "args": [], "attrs": {}, "rs": {"__ref": "60943161"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "60943161": {"values": {"color": "#000000"}, "mixins": [], "__type": "RuleSet"}, "60943163": {"variants": [{"__ref": "60943048"}], "args": [], "attrs": {}, "rs": {"__ref": "60943165"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "60943165": {"values": {}, "mixins": [], "__type": "RuleSet"}, "60943166": {"variants": [{"__ref": "60943058"}], "args": [], "attrs": {}, "rs": {"__ref": "60943167"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "60943167": {"values": {}, "mixins": [], "__type": "RuleSet"}, "61747001": {"uuid": "4ox5upY30R", "name": "ExternalIds", "params": [{"__ref": "61747050"}], "states": [], "tplTree": {"__ref": "61747003"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "61747004"}], "variantGroups": [], "pageMeta": {"__ref": "61747005"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "61747002": {"component": {"__ref": "61747001"}, "matrix": {"__ref": "61747006"}, "customMatrix": {"__ref": "1511005"}, "__type": "PageArena"}, "61747003": {"tag": "div", "name": null, "children": [{"__ref": "61747028"}, {"__ref": "61747039"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "8ympJ1DkV", "parent": null, "locked": null, "vsettings": [{"__ref": "61747007"}], "__type": "TplTag"}, "61747004": {"uuid": "O5kbFHiGlA", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "61747005": {"path": "/external", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "61747006": {"rows": [{"__ref": "61747009"}], "__type": "ArenaFrameGrid"}, "61747007": {"variants": [{"__ref": "61747004"}], "args": [], "attrs": {}, "rs": {"__ref": "61747010"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "61747009": {"cols": [{"__ref": "61747011"}, {"__ref": "61747012"}], "rowKey": {"__ref": "61747004"}, "__type": "ArenaFrameRow"}, "61747010": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "61747011": {"frame": {"__ref": "61747020"}, "cellKey": null, "__type": "ArenaFrameCell"}, "61747012": {"frame": {"__ref": "61747021"}, "cellKey": null, "__type": "ArenaFrameCell"}, "61747020": {"uuid": "cmr_jV35S5", "width": 1366, "height": 768, "container": {"__ref": "61747022"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "61747004"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "61747021": {"uuid": "6yHYs24ARu", "width": 414, "height": 736, "container": {"__ref": "61747023"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "61747004"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "61747022": {"name": null, "component": {"__ref": "61747001"}, "uuid": "lDxvYfDShE", "parent": null, "locked": null, "vsettings": [{"__ref": "61747024"}], "__type": "TplComponent"}, "61747023": {"name": null, "component": {"__ref": "61747001"}, "uuid": "-SuGifY2Tc", "parent": null, "locked": null, "vsettings": [{"__ref": "61747025"}], "__type": "TplComponent"}, "61747024": {"variants": [{"__ref": "52785007"}], "args": [], "attrs": {}, "rs": {"__ref": "61747026"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "61747025": {"variants": [{"__ref": "52785007"}], "args": [], "attrs": {}, "rs": {"__ref": "61747027"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "61747026": {"values": {}, "mixins": [], "__type": "RuleSet"}, "61747027": {"values": {}, "mixins": [], "__type": "RuleSet"}, "61747028": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "in6yGeAra", "parent": {"__ref": "61747003"}, "locked": null, "vsettings": [{"__ref": "61747029"}], "__type": "TplTag"}, "61747029": {"variants": [{"__ref": "61747004"}], "args": [], "attrs": {}, "rs": {"__ref": "61747030"}, "dataCond": null, "dataRep": null, "text": {"__ref": "61747037"}, "columnsConfig": null, "__type": "VariantSetting"}, "61747030": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px", "font-size": "72px", "text-align": "center"}, "mixins": [], "__type": "RuleSet"}, "61747037": {"markers": [], "text": "External ids", "__type": "RawText"}, "61747039": {"tag": "div", "name": null, "children": [{"__ref": "61747047"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Cf2FbRrqQ8", "parent": {"__ref": "61747003"}, "locked": null, "vsettings": [{"__ref": "61747040"}], "__type": "TplTag"}, "61747040": {"variants": [{"__ref": "61747004"}], "args": [], "attrs": {}, "rs": {"__ref": "61747041"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "61747041": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px", "display": "flex", "flex-direction": "row", "align-items": "center", "justify-content": "center"}, "mixins": [], "__type": "RuleSet"}, "61747047": {"param": {"__ref": "61747050"}, "defaultContents": [{"__ref": "61747051"}], "uuid": "ot5_aef7Vq", "parent": {"__ref": "61747039"}, "locked": null, "vsettings": [{"__ref": "61747052"}], "__type": "TplSlot"}, "61747050": {"type": {"__ref": "61747054"}, "tplSlot": {"__ref": "61747047"}, "variable": {"__ref": "61747053"}, "uuid": "TIgrzIcH0f", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "61747051": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "cS7Uqhd6sC", "parent": {"__ref": "61747047"}, "locked": null, "vsettings": [{"__ref": "61747055"}], "__type": "TplTag"}, "61747052": {"variants": [{"__ref": "61747004"}], "args": [], "attrs": {}, "rs": {"__ref": "61747056"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "61747053": {"name": "children", "uuid": "ONbV1w7Gem", "__type": "Var"}, "61747054": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "61747055": {"variants": [{"__ref": "61747004"}], "args": [], "attrs": {}, "rs": {"__ref": "61747057"}, "dataCond": null, "dataRep": null, "text": {"__ref": "61747058"}, "columnsConfig": null, "__type": "VariantSetting"}, "61747056": {"values": {}, "mixins": [], "__type": "RuleSet"}, "61747057": {"values": {}, "mixins": [], "__type": "RuleSet"}, "61747058": {"markers": [], "text": "Enter some text", "__type": "RawText"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}]]