[["220522b3-6ffd-4033-bed2-11941b5fdc8d", {"root": "iCXtEyddYb_n", "map": {"dodwlij9Qrdo": {"values": {}, "mixins": [], "__type": "RuleSet"}, "9DQpdp26V81I": {"name": "Default Typography", "rs": {"__ref": "dodwlij9Qrdo"}, "preview": null, "uuid": "XLY27_Tw-d5I", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "CzxEp4yOdIdX": {"defaultStyle": {"__ref": "9DQpdp26V81I"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "OmtBUmj0nGd7": {"uuid": "QIcNIXhlzrpm", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "QRhR_OiULoYg": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "OmtBUmj0nGd7"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "CzxEp4yOdIdX"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "AhbgQB4Lrv5s"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "uJ_Vga7FECxT"}], "__type": "Site"}, "uJ_Vga7FECxT": {"name": "hostless-lodash", "importPath": "lodash", "jsIdentifier": "lodash", "importType": "namespace", "namedImport": null, "isSyntheticDefaultImport": false, "__type": "CodeLibrary"}, "AhbgQB4Lrv5s": {"name": "lodash", "npmPkg": ["lodash"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "iCXtEyddYb_n": {"uuid": "acmpzAtyyWsZ", "pkgId": "db596f74-547c-420e-b963-e252b0a51d2c", "projectId": "hsyJcmBceSfPzvvXzVTuk7", "version": "0.0.1", "name": "lodash", "site": {"__ref": "QRhR_OiULoYg"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["c54f9d3e-56d6-4751-baa0-8b2a93ae8e0c", {"root": "JZxr36S-RvHI", "map": {"F3mJSLACsaLi": {"values": {}, "mixins": [], "__type": "RuleSet"}, "OZ_btUzqjPF5": {"name": "Default Typography", "rs": {"__ref": "F3mJSLACsaLi"}, "preview": null, "uuid": "q8ZejepvUaqm", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "4V0rLch7gIF4": {"defaultStyle": {"__ref": "OZ_btUzqjPF5"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "X_yDTZJQWR96": {"uuid": "P6JzkGokSLSp", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "nEZoJlYTYfD9": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "X_yDTZJQWR96"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "4V0rLch7gIF4"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "DSxCZUveQ4zQ"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "ddRVopkZ3EW9"}], "__type": "Site"}, "ddRVopkZ3EW9": {"name": "hostless-uuid", "importPath": "uuid", "jsIdentifier": "uuid", "importType": "namespace", "namedImport": null, "isSyntheticDefaultImport": false, "__type": "CodeLibrary"}, "DSxCZUveQ4zQ": {"name": "uuid", "npmPkg": ["uuid"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "JZxr36S-RvHI": {"uuid": "DZKA742bBk9G", "pkgId": "8394457f-3768-45ba-a937-35482e4d1501", "projectId": "a3pmZtcpA5asHkNtN3SGoF", "version": "0.2.0", "name": "uuid", "site": {"__ref": "nEZoJlYTYfD9"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["6a1dbdef-31d7-48d6-af32-426d27ae140d", {"root": "l9IiOSK8vl_A", "map": {"TdF37o_gMjux": {"values": {}, "mixins": [], "__type": "RuleSet"}, "-2dJzCBUSv8z": {"name": "Default Typography", "rs": {"__ref": "TdF37o_gMjux"}, "preview": null, "uuid": "9dJZg5Ke_nRy", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ffHhsar4Y7Vb": {"defaultStyle": {"__ref": "-2dJzCBUSv8z"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "iio2m_jx__ic": {"uuid": "BLbShoUWiWWh", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "oOYTH6jndtAf": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "iio2m_jx__ic"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "ffHhsar4Y7Vb"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "056VLtcIV5eW"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "vfmL7Wd8ZFzp"}], "__type": "Site"}, "vfmL7Wd8ZFzp": {"name": "hostless-isomorphic-fetch", "importPath": "isomorphic-fetch", "jsIdentifier": "fetch", "importType": "default", "namedImport": null, "isSyntheticDefaultImport": true, "__type": "CodeLibrary"}, "056VLtcIV5eW": {"name": "isomorphic-fetch", "npmPkg": ["isomorphic-fetch"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "l9IiOSK8vl_A": {"uuid": "CI52raOnd04Z", "pkgId": "b4ab506a-6185-4bf1-9716-a44bb5a63c4e", "projectId": "nRtUo7paSYc6d9hkKhzfrA", "version": "0.2.0", "name": "isomorphic-fetch", "site": {"__ref": "oOYTH6jndtAf"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["b12aeddb-2fe6-4c26-8574-36f3b8b72685", {"root": "CaK9Q6dJPob4", "map": {"BdP-LrSAvi37": {"values": {}, "mixins": [], "__type": "RuleSet"}, "eTjlvi_jYdeh": {"name": "Default Typography", "rs": {"__ref": "BdP-LrSAvi37"}, "preview": null, "uuid": "CzM1RIUd6U7-", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "YL9psJ3rR6hn": {"defaultStyle": {"__ref": "eTjlvi_jYdeh"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "qTaF_bFqvCXU": {"uuid": "-x4WqJTOfRUn", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "cmedJOyvWYjJ": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "qTaF_bFqvCXU"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "YL9psJ3rR6hn"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "-1Z2PC7vESQs"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "vDWFjVM3-Zvt"}], "__type": "Site"}, "vDWFjVM3-Zvt": {"name": "hostless-fast-stringify", "importPath": "fast-stringify", "jsIdentifier": "stringify", "importType": "default", "namedImport": null, "isSyntheticDefaultImport": false, "__type": "CodeLibrary"}, "-1Z2PC7vESQs": {"name": "fast-stringify", "npmPkg": ["fast-stringify"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "CaK9Q6dJPob4": {"uuid": "3mdWey72-8c8", "pkgId": "ca19a222-4a39-466d-8059-15ecd2bb124c", "projectId": "95cydKu4zdmeK3V4rXXSKZ", "version": "0.2.0", "name": "fast-stringify", "site": {"__ref": "cmedJOyvWYjJ"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["2e3e634b-0431-4dc5-842b-a652da3a62ca", {"root": "pX-idRyfhQc4", "map": {"cUIpCnu__--M": {"values": {}, "mixins": [], "__type": "RuleSet"}, "obRkPpBAKpaq": {"name": "Default Typography", "rs": {"__ref": "cUIpCnu__--M"}, "preview": null, "uuid": "wT_Wt70mUShW", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "06pSXjsfhG4m": {"defaultStyle": {"__ref": "obRkPpBAKpaq"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "l3wyAVaUohe2": {"uuid": "lJpS406iw3U_", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "sEhBXCkFLTTl": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "l3wyAVaUohe2"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "06pSXjsfhG4m"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "7d7P0tCDWwA3"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "POp7pjHRf4yq"}], "__type": "Site"}, "POp7pjHRf4yq": {"name": "hostless-marked", "importPath": "marked", "jsIdentifier": "marked", "importType": "named", "namedImport": "marked", "isSyntheticDefaultImport": false, "__type": "CodeLibrary"}, "7d7P0tCDWwA3": {"name": "marked", "npmPkg": ["marked"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "pX-idRyfhQc4": {"uuid": "JgwvGhtHoNbV", "pkgId": "816794d7-ce55-4144-bcd6-641fb2ce0064", "projectId": "miaW1xp1scEQBREQYHRbaL", "version": "0.2.0", "name": "marked", "site": {"__ref": "sEhBXCkFLTTl"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["822c0aa6-7b6c-4ddf-b468-e1583a1ee933", {"root": "05cLlBhqs78l", "map": {"UAaOQkhco1aP": {"values": {}, "mixins": [], "__type": "RuleSet"}, "DXXMxQyQ67GG": {"name": "Default Typography", "rs": {"__ref": "UAaOQkhco1aP"}, "preview": null, "uuid": "y3tfUOnCc_9P", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "rvmf1iHqsrNM": {"defaultStyle": {"__ref": "DXXMxQyQ67GG"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "ga6vkkjoy1Hz": {"uuid": "Dp_Vmt_bCXNr", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "_edJ5c3mvuBF": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "ga6vkkjoy1Hz"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "rvmf1iHqsrNM"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "D8Yhn2-iSU8C"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "i7M0jYQEPS55"}], "__type": "Site"}, "i7M0jYQEPS55": {"name": "hostless-pluralize", "importPath": "pluralize", "jsIdentifier": "pluralize", "importType": "default", "namedImport": null, "isSyntheticDefaultImport": true, "__type": "CodeLibrary"}, "D8Yhn2-iSU8C": {"name": "pluralize", "npmPkg": ["pluralize"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "05cLlBhqs78l": {"uuid": "mqu7UyGY7-Bp", "pkgId": "67df7914-ab16-4d45-9376-c28758393ca7", "projectId": "7J5DBmLL9S8cZoRJpQkUda", "version": "0.2.0", "name": "pluralize", "site": {"__ref": "_edJ5c3mvuBF"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["5399a8a4-8a49-4637-a6bb-b1e3e071c73f", {"root": "YMHRoh-DyGbA", "map": {"e4V9bxtiIf9k": {"values": {}, "mixins": [], "__type": "RuleSet"}, "FzNl1v8A3WSe": {"name": "Default Typography", "rs": {"__ref": "e4V9bxtiIf9k"}, "preview": null, "uuid": "iwcZnJ-COYP3", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "DKnETAcsLnPT": {"defaultStyle": {"__ref": "FzNl1v8A3WSe"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "52j-rHexq18o": {"uuid": "amZ-zax6wCza", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "s00dtdZIEOOn": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "52j-rHexq18o"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "DKnETAcsLnPT"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "oPE7lqoQcqZb"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "FQ7Sa5yAbUCA"}], "__type": "Site"}, "FQ7Sa5yAbUCA": {"name": "hostless-copy-to-clipboard", "importPath": "copy-to-clipboard", "jsIdentifier": "copyToClipboard", "importType": "default", "namedImport": null, "isSyntheticDefaultImport": true, "__type": "CodeLibrary"}, "oPE7lqoQcqZb": {"name": "copy-to-clipboard", "npmPkg": ["copy-to-clipboard"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "YMHRoh-DyGbA": {"uuid": "mqiqMw_wIN1Q", "pkgId": "de30a5b9-cc63-4054-b3b6-a749bda27f0b", "projectId": "aEHVN5v6mfM4eovDsL8Z7d", "version": "0.2.0", "name": "copy-to-clipboard", "site": {"__ref": "s00dtdZIEOOn"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["65946b0b-b4c4-492a-80ee-a7736ae3a735", {"root": "MMDp_wX48vMH", "map": {"_UlNykC1frvC": {"values": {}, "mixins": [], "__type": "RuleSet"}, "_yifD1KTrRru": {"name": "Default Typography", "rs": {"__ref": "_UlNykC1frvC"}, "preview": null, "uuid": "3TqibmfGbu19", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "nh618SKlXPTJ": {"defaultStyle": {"__ref": "_yifD1KTrRru"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "uWFEcIn6Gqbp": {"uuid": "ow7DaPyzbQkm", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "ZdDhYhesBG4v": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "uWFEcIn6Gqbp"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "nh618SKlXPTJ"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "ocMJxUx6Oo6P"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "gHb0r4Q7X2C4"}, {"__ref": "QUo_LR2rwY79"}], "__type": "Site"}, "gHb0r4Q7X2C4": {"name": "hostless-random", "importPath": "random", "jsIdentifier": "random", "importType": "default", "namedImport": null, "isSyntheticDefaultImport": false, "__type": "CodeLibrary"}, "QUo_LR2rwY79": {"name": "hostless-random-extras", "importPath": "random", "jsIdentifier": "randomExtras", "importType": "namespace", "namedImport": null, "isSyntheticDefaultImport": false, "__type": "CodeLibrary"}, "ocMJxUx6Oo6P": {"name": "random", "npmPkg": ["random"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "MMDp_wX48vMH": {"uuid": "M3kJd9hZcujW", "pkgId": "3e92be9b-a6ab-4da1-a9e5-ec2c21350ee1", "projectId": "kb3Kya2pMxe2srmyNSvXg", "version": "0.2.0", "name": "random", "site": {"__ref": "ZdDhYhesBG4v"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["388ed04e-61fd-4211-8b2d-352ee2cfd5b8", {"root": "Hs98jzrpZ22M", "map": {"6F0d3jSLDxub": {"values": {}, "mixins": [], "__type": "RuleSet"}, "mTtFUZhil0Is": {"name": "Default Typography", "rs": {"__ref": "6F0d3jSLDxub"}, "preview": null, "uuid": "X5eGjG_VqqVk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Hjui2DZFy-Nf": {"defaultStyle": {"__ref": "mTtFUZhil0Is"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "gPuotvzgf9XA": {"uuid": "-ohsa6ehLi69", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "O4RuFZAbeeyq": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "gPuotvzgf9XA"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "Hjui2DZFy-Nf"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "4dK5EAGcTa7D"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "g8K35iyjQ4US"}, {"__ref": "SBxPlqCpuc_L"}], "__type": "Site"}, "g8K35iyjQ4US": {"name": "hostless-faker-js", "importPath": "@faker-js/faker", "jsIdentifier": "fakerJs", "importType": "named", "namedImport": "faker", "isSyntheticDefaultImport": false, "__type": "CodeLibrary"}, "SBxPlqCpuc_L": {"name": "hostless-faker-js-extras", "importPath": "@faker-js/faker", "jsIdentifier": "fakerJsExtras", "importType": "namespace", "namedImport": null, "isSyntheticDefaultImport": false, "__type": "CodeLibrary"}, "4dK5EAGcTa7D": {"name": "faker", "npmPkg": ["@faker-js/faker"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "Hs98jzrpZ22M": {"uuid": "dBjvrND8hlYR", "pkgId": "abcb36f6-4181-4e9c-9277-e0fdf460c3c0", "projectId": "cq9NPWVMXvHe5bhLAYLfT1", "version": "0.2.0", "name": "faker", "site": {"__ref": "O4RuFZAbeeyq"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["c128d63f-01ec-4d8b-a3d9-f1991d044290", {"root": "5AxyDDc4rLZo", "map": {"7kzokWcQ5kx-": {"values": {}, "mixins": [], "__type": "RuleSet"}, "8toNjfF3wlW6": {"name": "Default Typography", "rs": {"__ref": "7kzokWcQ5kx-"}, "preview": null, "uuid": "OGuzI5w56JcO", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "efU9ur_DA2Z3": {"defaultStyle": {"__ref": "8toNjfF3wlW6"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "BvFU6ZnbB6fE": {"uuid": "RvgvnhvUUdmj", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "8xqfHnaGgyT0": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "BvFU6ZnbB6fE"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "efU9ur_DA2Z3"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "2Z4vagpz7rRj"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "GWC7cUpHhJAh"}], "__type": "Site"}, "GWC7cUpHhJAh": {"name": "hostless-nanoid", "importPath": "nanoid", "jsIdentifier": "nanoid", "importType": "namespace", "namedImport": null, "isSyntheticDefaultImport": false, "__type": "CodeLibrary"}, "2Z4vagpz7rRj": {"name": "nanoid", "npmPkg": ["nanoid"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "5AxyDDc4rLZo": {"uuid": "o1tyXfkx6--O", "pkgId": "d784dad6-3942-452a-b232-e452cafa1fa3", "projectId": "9UibdRruyX43DxnB8Q1QXF", "version": "0.2.0", "name": "nanoid", "site": {"__ref": "8xqfHnaGgyT0"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["6cd11ba1-885b-4a6c-82fa-fbf5829e5ce5", {"root": "Q9vmaQgE1Ax_", "map": {"vzPPSc02usCD": {"values": {}, "mixins": [], "__type": "RuleSet"}, "CcuVOzVSOyoW": {"name": "Default Typography", "rs": {"__ref": "vzPPSc02usCD"}, "preview": null, "uuid": "AITV308cwqGZ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "vyIbth-yBSns": {"defaultStyle": {"__ref": "CcuVOzVSOyoW"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "RXcSoE_YEa0f": {"uuid": "q5rUDCm8a-dm", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "GtJA5KfvwcQR": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "RXcSoE_YEa0f"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "vyIbth-yBSns"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "w8N5BJ2o1iw0"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "vWw1MCTgNkAe"}], "__type": "Site"}, "vWw1MCTgNkAe": {"name": "hostless-tinycolor2", "importPath": "tinycolor2", "jsIdentifier": "tinycolor2", "importType": "default", "namedImport": null, "isSyntheticDefaultImport": true, "__type": "CodeLibrary"}, "w8N5BJ2o1iw0": {"name": "tinycolor2", "npmPkg": ["tinycolor2"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "Q9vmaQgE1Ax_": {"uuid": "piobokQndzU6", "pkgId": "6ef967d6-a6b1-45d5-919d-1c1938124c18", "projectId": "gvZmCBEn1URTZWQ5E6hHJp", "version": "0.2.0", "name": "tinycolor2", "site": {"__ref": "GtJA5KfvwcQR"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["13bb2354-599c-43e0-a9b8-c4f3733e78f5", {"root": "nsa_lh-2NFJ-", "map": {"2WQWluXskWor": {"values": {}, "mixins": [], "__type": "RuleSet"}, "khZ_kGVNJ88I": {"name": "Default Typography", "rs": {"__ref": "2WQWluXskWor"}, "preview": null, "uuid": "F_hErvdQxd-x", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "bU4-PtFckzmT": {"defaultStyle": {"__ref": "khZ_kGVNJ88I"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "vE7sNZ6erZjh": {"uuid": "OjsIUK9fBaqw", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "1noWIZa_fkoz": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "vE7sNZ6erZjh"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "bU4-PtFckzmT"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "aehljfeUOeD0"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "tTLUiQn0xQz_"}], "__type": "Site"}, "tTLUiQn0xQz_": {"name": "hostless-zod", "importPath": "zod", "jsIdentifier": "zod", "importType": "named", "namedImport": "z", "isSyntheticDefaultImport": false, "__type": "CodeLibrary"}, "aehljfeUOeD0": {"name": "zod", "npmPkg": ["zod"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "nsa_lh-2NFJ-": {"uuid": "psV_tjO99Vb3", "pkgId": "aba8334e-5b03-4d5c-b448-31268ea0c9ac", "projectId": "vWtWgPPF1YuRD8hUh8BgQm", "version": "0.2.0", "name": "zod", "site": {"__ref": "1noWIZa_fkoz"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["ea7d9df6-a1da-4893-8bcc-a04096a23010", {"root": "M9P8qKfL7iiI", "map": {"dTOpXnyISt7M": {"values": {}, "mixins": [], "__type": "RuleSet"}, "rbfIn12dtTfE": {"name": "Default Typography", "rs": {"__ref": "dTOpXnyISt7M"}, "preview": null, "uuid": "gv0RHyizgL-Y", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "koapsheR8cRc": {"defaultStyle": {"__ref": "rbfIn12dtTfE"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "Q0rNbUWGhaAi": {"uuid": "jf8Tm1dWSxmw", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "FY0IKt8CUSJO": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "Q0rNbUWGhaAi"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "koapsheR8cRc"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "ZgktpiI7zXj_"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "i6Hw6ZC-EeA4"}], "__type": "Site"}, "i6Hw6ZC-EeA4": {"name": "hostless-immer", "importPath": "immer", "jsIdentifier": "immer", "importType": "namespace", "namedImport": null, "isSyntheticDefaultImport": false, "__type": "CodeLibrary"}, "ZgktpiI7zXj_": {"name": "immer", "npmPkg": ["immer"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "M9P8qKfL7iiI": {"uuid": "nEKosMrN5j0t", "pkgId": "82df8453-136a-4f65-b739-144b1928e286", "projectId": "t8YkhWMcnmjNxpEvnBYxFF", "version": "0.2.0", "name": "immer", "site": {"__ref": "FY0IKt8CUSJO"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["de7a14b8-3322-4ce1-9b9a-cc95f35bfd4b", {"root": "wfUnX1A70rKM", "map": {"YvACsKUE04ni": {"values": {}, "mixins": [], "__type": "RuleSet"}, "NyaqjH-_cx0R": {"name": "Default Typography", "rs": {"__ref": "YvACsKUE04ni"}, "preview": null, "uuid": "aJ3OodaQ0-10", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "pYo-guKJ9Jt6": {"defaultStyle": {"__ref": "NyaqjH-_cx0R"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "wiDAcIpyrpIt": {"uuid": "jMge8mCZKoCH", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "sdJo5AFpWxKa": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "wiDAcIpyrpIt"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "pYo-guKJ9Jt6"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "4FyiTzuxAcyc"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "r8S-uNO2ykON"}], "__type": "Site"}, "r8S-uNO2ykON": {"name": "hostless-md5", "importPath": "md5", "jsIdentifier": "md5", "importType": "default", "namedImport": null, "isSyntheticDefaultImport": true, "__type": "CodeLibrary"}, "4FyiTzuxAcyc": {"name": "md5", "npmPkg": ["md5"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "wfUnX1A70rKM": {"uuid": "cLoMc-2e5LJB", "pkgId": "4b897ce0-003e-4447-808c-874c6d1aaf85", "projectId": "cAAqA2dcbTMWwuLSmjs7ss", "version": "0.2.0", "name": "md5", "site": {"__ref": "sdJo5AFpWxKa"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["e19497fe-1015-4c06-bb56-693e52674193", {"root": "mO0BAa8o2TJO", "map": {"bimZxF6HOVb4": {"values": {}, "mixins": [], "__type": "RuleSet"}, "RJZ4Te7P1E8g": {"name": "Default Typography", "rs": {"__ref": "bimZxF6HOVb4"}, "preview": null, "uuid": "KMbLvIttT42C", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "cdSzBQQvJAE-": {"defaultStyle": {"__ref": "RJZ4Te7P1E8g"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "vkHCZzI44LVK": {"uuid": "PxCR-JNW3OLm", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "dEWvCCFxFmml": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "vkHCZzI44LVK"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "cdSzBQQvJAE-"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "Ikp-5pMUfLXS"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "5AQ9L0Y1uTjP"}, {"__ref": "XUnmrjZLo8hS"}], "__type": "Site"}, "5AQ9L0Y1uTjP": {"name": "hostless-axios", "importPath": "axios", "jsIdentifier": "axios", "importType": "default", "namedImport": null, "isSyntheticDefaultImport": false, "__type": "CodeLibrary"}, "XUnmrjZLo8hS": {"name": "hostless-axios-extras", "importPath": "axios", "jsIdentifier": "axiosExtras", "importType": "namespace", "namedImport": null, "isSyntheticDefaultImport": false, "__type": "CodeLibrary"}, "Ikp-5pMUfLXS": {"name": "axios", "npmPkg": ["axios"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "mO0BAa8o2TJO": {"uuid": "P1fBnRGDrBjA", "pkgId": "0484967d-5d2c-4f63-ac29-2ed420313fa9", "projectId": "k6BnBuAUepzRX8Qs2WvkKs", "version": "0.2.0", "name": "axios", "site": {"__ref": "dEWvCCFxFmml"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["6718f4f5-7b8d-4fd0-a544-9ba8a7269ac4", {"root": "vKhXkwMYKb8w", "map": {"Qvl5hoOigKxU": {"values": {}, "mixins": [], "__type": "RuleSet"}, "LwzQIuq6jJFs": {"name": "Default Typography", "rs": {"__ref": "Qvl5hoOigKxU"}, "preview": null, "uuid": "0w2P8QA6QXoG", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "KDI1vTDyl4aA": {"defaultStyle": {"__ref": "LwzQIuq6jJFs"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "ttQHQwUs8gb2": {"uuid": "c73zsXYBl1zD", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "91NrLQMVIMZ6": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "ttQHQwUs8gb2"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "KDI1vTDyl4aA"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "DLtF5MRuyfaa"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "xSZSeLzEoPRL"}], "__type": "Site"}, "xSZSeLzEoPRL": {"name": "hostless-semver", "importPath": "semver", "jsIdentifier": "semver", "importType": "namespace", "namedImport": null, "isSyntheticDefaultImport": false, "__type": "CodeLibrary"}, "DLtF5MRuyfaa": {"name": "semver", "npmPkg": ["semver"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "vKhXkwMYKb8w": {"uuid": "iSgGzgaXqVg_", "pkgId": "7887ad35-0808-48c3-8900-1c26412bdf83", "projectId": "m9f2sdCsBvUkgw2ruM3pjM", "version": "0.2.0", "name": "semver", "site": {"__ref": "91NrLQMVIMZ6"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["f28b72d1-77e8-4010-9170-a783c5560c08", {"root": "mEMAYXhmv4Hd", "map": {"UMGwrZj8PFVL": {"values": {}, "mixins": [], "__type": "RuleSet"}, "dXB1W5qaYprf": {"name": "Default Typography", "rs": {"__ref": "UMGwrZj8PFVL"}, "preview": null, "uuid": "zIyLjy_x3KQT", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ltSSxnVTmcKV": {"defaultStyle": {"__ref": "dXB1W5qaYprf"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "2huIDl_a0_ow": {"uuid": "3lcXmQV6MUm7", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "iNy4_aX3SP1s": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "2huIDl_a0_ow"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "ltSSxnVTmcKV"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "U1b_g0kclxEH"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "IuVMmuhiZp5O"}], "__type": "Site"}, "IuVMmuhiZp5O": {"name": "hostless-date-fns", "importPath": "date-fns", "jsIdentifier": "dateFns", "importType": "namespace", "namedImport": null, "isSyntheticDefaultImport": false, "__type": "CodeLibrary"}, "U1b_g0kclxEH": {"name": "date-fns", "npmPkg": ["date-fns"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "mEMAYXhmv4Hd": {"uuid": "IpunTxEgsi5e", "pkgId": "9bde7cf2-4629-47dd-a873-4c8226af9ed4", "projectId": "77tPPMY1MAED1Fqwv1T1ig", "version": "0.2.0", "name": "date-fns", "site": {"__ref": "iNy4_aX3SP1s"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["d52194cc-ad47-46c8-8d5d-955ad010b9e7", {"root": "OXKwEI208S_B", "map": {"nlXGH7MxoVct": {"values": {}, "mixins": [], "__type": "RuleSet"}, "eDX2rKZPdjqE": {"name": "Default Typography", "rs": {"__ref": "nlXGH7MxoVct"}, "preview": null, "uuid": "23ibG0G5FCbt", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "MLrtajGGW1ZJ": {"defaultStyle": {"__ref": "eDX2rKZPdjqE"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "gIwSjShPjAY5": {"uuid": "qLRv6Rlf_Y_L", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "kdcrYK_FC7Dk": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "gIwSjShPjAY5"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "MLrtajGGW1ZJ"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "KMXclZLTg6oc"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "DzKntokJRYZw"}], "__type": "Site"}, "DzKntokJRYZw": {"name": "hostless-papaparse", "importPath": "<PERSON><PERSON><PERSON><PERSON>", "jsIdentifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "importType": "namespace", "namedImport": null, "isSyntheticDefaultImport": false, "__type": "CodeLibrary"}, "KMXclZLTg6oc": {"name": "<PERSON><PERSON><PERSON><PERSON>", "npmPkg": ["<PERSON><PERSON><PERSON><PERSON>"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "OXKwEI208S_B": {"uuid": "GQv-AKApgYah", "pkgId": "4469f32e-3122-45aa-8dd7-b2c2acc86953", "projectId": "rNRUHCSwyFxFnddSQ3cpnL", "version": "0.2.0", "name": "<PERSON><PERSON><PERSON><PERSON>", "site": {"__ref": "kdcrYK_FC7Dk"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["17eae61b-545f-4d2a-a33e-7637d79212ba", {"root": "rIChVUdMxdV6", "map": {"HWPWn6xoNKKv": {"values": {}, "mixins": [], "__type": "RuleSet"}, "XK9lfwRkqVJm": {"name": "Default Typography", "rs": {"__ref": "HWPWn6xoNKKv"}, "preview": null, "uuid": "rvaXxEakXDSE", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "59oCPI37arru": {"defaultStyle": {"__ref": "XK9lfwRkqVJm"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "mj6LfX2--GWQ": {"uuid": "aYp3DIbQaRKh", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "KmS5UR4I5dD6": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "mj6LfX2--GWQ"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "59oCPI37arru"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "T-TXd4h_5VQF"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "bLhRm7u6_6OO"}], "__type": "Site"}, "bLhRm7u6_6OO": {"name": "hostless-j<PERSON>y", "importPath": "j<PERSON>y", "jsIdentifier": "j<PERSON>y", "importType": "default", "namedImport": null, "isSyntheticDefaultImport": true, "__type": "CodeLibrary"}, "T-TXd4h_5VQF": {"name": "j<PERSON>y", "npmPkg": ["j<PERSON>y"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "rIChVUdMxdV6": {"uuid": "7X6iZajfq2Tw", "pkgId": "9bea388a-03df-4764-b290-d601255bd202", "projectId": "mqxdhm5p6WHAt7hW1mdcCX", "version": "0.2.0", "name": "j<PERSON>y", "site": {"__ref": "KmS5UR4I5dD6"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["ea54091c-dcfb-47e2-86b4-fe360c62f6ec", {"root": "AdZ9R7hfnWc_", "map": {"_xBLSxLVcWz_": {"values": {}, "mixins": [], "__type": "RuleSet"}, "N68wRUqg0jEF": {"name": "Default Typography", "rs": {"__ref": "_xBLSxLVcWz_"}, "preview": null, "uuid": "5171zt3DnuWS", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "CM11jwGdkap_": {"defaultStyle": {"__ref": "N68wRUqg0jEF"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "-CVGhMBkm39j": {"uuid": "Yh91xc6bzKH6", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "tHubwV10QTm8": {"components": [], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "-CVGhMBkm39j"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "CM11jwGdkap_"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "LoYwiunWTK8C"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [{"__ref": "xdaSdIP1LmBV"}], "__type": "Site"}, "xdaSdIP1LmBV": {"name": "hostless-dayjs", "importPath": "dayjs", "jsIdentifier": "dayjs", "importType": "default", "namedImport": null, "isSyntheticDefaultImport": true, "__type": "CodeLibrary"}, "LoYwiunWTK8C": {"name": "dayjs", "npmPkg": ["dayjs"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "AdZ9R7hfnWc_": {"uuid": "NnjQWkUU7LTf", "pkgId": "23d726e1-f56d-4a84-a75e-dca03157338d", "projectId": "kpio2pzukKRJkKg6SSrKTe", "version": "0.0.1", "name": "dayjs", "site": {"__ref": "tHubwV10QTm8"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["7bfdcdde-66e0-469e-9022-02955a3f8f15", {"root": "6q1HDtNt4qJj", "map": {"388001": {"name": "bool", "__type": "BoolType"}, "388002": {"code": "true", "fallback": null, "__type": "CustomCode"}, "1364501": {"name": "func", "params": [], "__type": "FunctionType"}, "3091301": {"uuid": "zlBHsvkFlN", "name": "hostless-condition-guard", "params": [{"__ref": "3091302"}, {"__ref": "3091305"}, {"__ref": "3091309"}, {"__ref": "3091312"}], "states": [], "tplTree": {"__ref": "3091315"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "3091318"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "3091324"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "3091302": {"type": {"__ref": "3091304"}, "tplSlot": {"__ref": "3091316"}, "variable": {"__ref": "3091303"}, "uuid": "RDjE7dpa54", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "3091303": {"name": "children", "uuid": "OKKadQInTs", "__type": "Var"}, "3091304": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "3091305": {"type": {"__ref": "3091307"}, "variable": {"__ref": "3091306"}, "uuid": "qs39Osr8HG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "3091308"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Condition", "about": "The condition to guard against", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "3091306": {"name": "condition", "uuid": "AdRRIryUxl", "__type": "Var"}, "3091307": {"name": "bool", "__type": "BoolType"}, "3091308": {"code": "true", "fallback": null, "__type": "CustomCode"}, "3091309": {"type": {"__ref": "1364501"}, "variable": {"__ref": "3091310"}, "uuid": "xVC6ymMn1M", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "On condition false", "about": "The action to run when the condition is not satisfied", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "3091310": {"name": "onNotSatisfied", "uuid": "cIsVtxbOms", "__type": "Var"}, "3091312": {"type": {"__ref": "3091314"}, "variable": {"__ref": "3091313"}, "uuid": "KI8jKkLjQCx", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Skip Paths", "about": "Paths that the action should not run", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "3091313": {"name": "skipPaths", "uuid": "ddQLCJFRpw", "__type": "Var"}, "3091314": {"name": "any", "__type": "AnyType"}, "3091315": {"tag": "div", "name": null, "children": [{"__ref": "3091316"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "x_t6SyUUDt", "parent": null, "locked": null, "vsettings": [{"__ref": "3091320"}], "__type": "TplTag"}, "3091316": {"param": {"__ref": "3091302"}, "defaultContents": [], "uuid": "stmE5YAlgKe", "parent": {"__ref": "3091315"}, "locked": null, "vsettings": [{"__ref": "3091317"}], "__type": "TplSlot"}, "3091317": {"variants": [{"__ref": "3091318"}], "args": [], "attrs": {}, "rs": {"__ref": "3091319"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3091318": {"uuid": "DGb520OHL", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3091319": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3091320": {"variants": [{"__ref": "3091318"}], "args": [], "attrs": {}, "rs": {"__ref": "3091321"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3091321": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "3091324": {"importPath": "@plasmicpkgs/plasmic-basic-components", "defaultExport": false, "displayName": "Condition Guard", "importName": "Condition<PERSON><PERSON>", "description": "Ensure some condition, or else run an interaction. Examples: ensure all users have a database row, or require new users to setup a profile.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "3935001": {"components": [{"__ref": "34442008"}, {"__ref": "34442010"}, {"__ref": "7682001"}, {"__ref": "11490001"}, {"__ref": "3091301"}, {"__ref": "IO7KN06x7tsN"}, {"__ref": "wpKxI3HMxpwz"}], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "3935053"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "3935061"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "0aNeVPi5XpSa"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "3935053": {"uuid": "iumhDju3mca", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3935061": {"defaultStyle": {"__ref": "3935062"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "3935062": {"name": "Default Typography", "rs": {"__ref": "3935063"}, "preview": null, "uuid": "KIzxF7SZdO", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "3935063": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7682001": {"uuid": "PKldDYkH42", "name": "hostless-embed", "params": [{"__ref": "7682003"}, {"__ref": "7682004"}], "states": [], "tplTree": {"__ref": "7682005"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "7682006"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "7682007"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "7682003": {"type": {"__ref": "39050001"}, "variable": {"__ref": "7682008"}, "uuid": "9D1tAarKCe", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": "The HTML code to be embedded", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "7682004": {"type": {"__ref": "7682012"}, "variable": {"__ref": "7682011"}, "uuid": "gtKUvh0VsH", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "<PERSON>de in editor", "about": "Disable running the code while editing in Plasmic Studio (may require reload)", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "7682005": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "uLdBgr-g0D", "parent": null, "locked": null, "vsettings": [{"__ref": "7682013"}], "__type": "TplTag"}, "7682006": {"uuid": "dSzJ_RIkU", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "7682007": {"importPath": "@plasmicpkgs/plasmic-basic-components", "defaultExport": false, "displayName": "Embed HTML", "importName": "Embed", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "7682015"}, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "7682008": {"name": "code", "uuid": "ARXptWIBzB", "__type": "Var"}, "7682011": {"name": "hideInEditor", "uuid": "XQuF0qGHn6", "__type": "Var"}, "7682012": {"name": "bool", "__type": "BoolType"}, "7682013": {"variants": [{"__ref": "7682006"}], "args": [], "attrs": {}, "rs": {"__ref": "7682016"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7682015": {"values": {"max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "7682016": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "11490001": {"uuid": "D4RbnlpRXg3", "name": "hostless-data-provider", "params": [{"__ref": "11490003"}, {"__ref": "11490004"}, {"__ref": "11490005"}], "states": [], "tplTree": {"__ref": "11490006"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "11490007"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "11490008"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "11490003": {"type": {"__ref": "11490010"}, "variable": {"__ref": "11490009"}, "uuid": "Zb2z0FYQYHx", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "11490011"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": "The name of the variable to store the data in", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "11490004": {"type": {"__ref": "11490013"}, "variable": {"__ref": "11490012"}, "uuid": "L6BE72bobUA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "11490014"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "11490005": {"type": {"__ref": "11490016"}, "tplSlot": {"__ref": "11490017"}, "variable": {"__ref": "11490015"}, "uuid": "NOqUk0yNY_W", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "11490006": {"tag": "div", "name": null, "children": [{"__ref": "11490017"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Dihoed4pPMC", "parent": null, "locked": null, "vsettings": [{"__ref": "11490018"}], "__type": "TplTag"}, "11490007": {"uuid": "R3Rc6GfRB7D", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "11490008": {"importPath": "@plasmicpkgs/plasmic-basic-components", "defaultExport": false, "displayName": "Data Provider", "importName": "DataProvider", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": true, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "11490009": {"name": "name", "uuid": "GXFVFWfyxZl", "__type": "Var"}, "11490010": {"name": "text", "__type": "Text"}, "11490011": {"code": "\"celebrities\"", "fallback": null, "__type": "CustomCode"}, "11490012": {"name": "data", "uuid": "rSNlDi5lxtV", "__type": "Var"}, "11490013": {"name": "any", "__type": "AnyType"}, "11490014": {"code": "[{\"name\":\"<PERSON><PERSON> <PERSON>\",\"birthYear\":1950,\"profilePicture\":[\"https://www.fillmurray.com/200/300\"]},{\"name\":\"<PERSON> Cage\",\"birthYear\":1950,\"profilePicture\":[\"https://www.placecage.com/200/300\"]}]", "fallback": null, "__type": "CustomCode"}, "11490015": {"name": "children", "uuid": "q3jo4T09vhj", "__type": "Var"}, "11490016": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "11490017": {"param": {"__ref": "11490005"}, "defaultContents": [], "uuid": "zEYcFmn2rx6", "parent": {"__ref": "11490006"}, "locked": null, "vsettings": [{"__ref": "11490020"}], "__type": "TplSlot"}, "11490018": {"variants": [{"__ref": "11490007"}], "args": [], "attrs": {}, "rs": {"__ref": "11490021"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "11490020": {"variants": [{"__ref": "11490007"}], "args": [], "attrs": {}, "rs": {"__ref": "11490022"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "11490021": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "11490022": {"values": {}, "mixins": [], "__type": "RuleSet"}, "34442008": {"uuid": "CMDBvOhaI4s", "name": "hostless-iframe", "params": [{"__ref": "34442059"}, {"__ref": "34442060"}, {"__ref": "MTSIbR4xXJ7P"}], "states": [], "tplTree": {"__ref": "34442061"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "34442062"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "34442063"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "34442010": {"uuid": "RhitNJW5Zu-", "name": "hostless-html-video", "params": [{"__ref": "34442070"}, {"__ref": "34442071"}, {"__ref": "34442072"}, {"__ref": "34442073"}, {"__ref": "34442074"}, {"__ref": "34442075"}, {"__ref": "34442076"}, {"__ref": "34442077"}], "states": [], "tplTree": {"__ref": "34442078"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "34442079"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "34442080"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "34442059": {"type": {"__ref": "34442161"}, "variable": {"__ref": "34442160"}, "uuid": "pc7QF_n-AN4", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "34442162"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "34442060": {"type": {"__ref": "34442164"}, "variable": {"__ref": "34442163"}, "uuid": "6tZa1ABhlzx", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": "Load the iframe while editing in Plasmic Studio", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "34442061": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "BLbXDUzy0PM", "parent": null, "locked": null, "vsettings": [{"__ref": "34442165"}], "__type": "TplTag"}, "34442062": {"uuid": "YzDxmORjAk9", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "34442063": {"importPath": "@plasmicpkgs/plasmic-basic-components", "defaultExport": false, "displayName": "<PERSON><PERSON><PERSON>", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "34442167"}, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "34442070": {"type": {"__ref": "34442179"}, "variable": {"__ref": "34442178"}, "uuid": "JhE1V01vFwC", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "34442180"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Source URL", "about": "URL to a video file.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "34442071": {"type": {"__ref": "44225001"}, "variable": {"__ref": "34442181"}, "uuid": "BeaU6bd6KF2", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Auto Play", "about": "Whether the video show automatically start playing when the player loads. Chrome and other browsers require 'muted' to also be set for 'autoplay' to work.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "34442072": {"type": {"__ref": "388001"}, "variable": {"__ref": "34442183"}, "uuid": "lp_OObyuUhX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "388002"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Show Controls", "about": "Whether the video player controls should be displayed", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "34442073": {"type": {"__ref": "34442186"}, "variable": {"__ref": "34442185"}, "uuid": "9KDPt60pddh", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Plays inline", "about": "Usually on mobile, when tilted landscape, videos can play fullscreen. Turn this on to prevent that.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "34442074": {"type": {"__ref": "34442188"}, "variable": {"__ref": "34442187"}, "uuid": "Byvf_4lQcUU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Loop", "about": "Whether the video should be played again after it finishes", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "34442075": {"type": {"__ref": "34442190"}, "variable": {"__ref": "34442189"}, "uuid": "ddKH_rNl-gh", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Muted", "about": "Whether audio should be muted", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "34442076": {"type": {"__ref": "34442192"}, "variable": {"__ref": "34442191"}, "uuid": "XchXjJ5tq1k", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Poster (placeholder) image", "about": "Image to show while video is downloading", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "34442077": {"type": {"__ref": "34442194"}, "variable": {"__ref": "34442193"}, "uuid": "zoTn2flQDzH", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Preload", "about": "Whether to preload nothing, metadata only, or the full video", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "34442078": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "qGG8LRdzXoM", "parent": null, "locked": null, "vsettings": [{"__ref": "34442195"}], "__type": "TplTag"}, "34442079": {"uuid": "rlwUqrQ8bjh", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "34442080": {"importPath": "@plasmicpkgs/plasmic-basic-components", "defaultExport": false, "displayName": "HTML Video", "importName": "Video", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "39447001"}, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "34442160": {"name": "src", "uuid": "6-acOUSDbVf", "__type": "Var"}, "34442161": {"name": "text", "__type": "Text"}, "34442162": {"code": "\"https://www.example.com\"", "fallback": null, "__type": "CustomCode"}, "34442163": {"name": "preview", "uuid": "5YX8HLrhyzZ", "__type": "Var"}, "34442164": {"name": "bool", "__type": "BoolType"}, "34442165": {"variants": [{"__ref": "34442062"}], "args": [], "attrs": {}, "rs": {"__ref": "34442212"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "34442167": {"values": {"width": "300px", "height": "150px", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "34442178": {"name": "src", "uuid": "S2TL94qEy69", "__type": "Var"}, "34442179": {"name": "text", "__type": "Text"}, "34442180": {"code": "\"https://interactive-examples.mdn.mozilla.net/media/cc0-videos/flower.webm\"", "fallback": null, "__type": "CustomCode"}, "34442181": {"name": "autoPlay", "uuid": "AZvDKjX6YdB", "__type": "Var"}, "34442183": {"name": "controls", "uuid": "G_yDBPdbHse", "__type": "Var"}, "34442185": {"name": "playsInline", "uuid": "WQUR20b68-h", "__type": "Var"}, "34442186": {"name": "bool", "__type": "BoolType"}, "34442187": {"name": "loop", "uuid": "Y3qgEELp9_t", "__type": "Var"}, "34442188": {"name": "bool", "__type": "BoolType"}, "34442189": {"name": "muted", "uuid": "gAW_Xausxzh", "__type": "Var"}, "34442190": {"name": "bool", "__type": "BoolType"}, "34442191": {"name": "poster", "uuid": "TRpBgB_xjp4", "__type": "Var"}, "34442192": {"name": "img", "__type": "Img"}, "34442193": {"name": "preload", "uuid": "xeZFQ2XjZgb", "__type": "Var"}, "34442194": {"name": "choice", "options": ["none", "metadata", "auto"], "__type": "Choice"}, "34442195": {"variants": [{"__ref": "34442079"}], "args": [], "attrs": {}, "rs": {"__ref": "34442220"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "34442212": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "34442220": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "39050001": {"name": "text", "__type": "Text"}, "39447001": {"values": {"width": "640px", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "44225001": {"name": "bool", "__type": "BoolType"}, "fQ4AjmKcRIT4": {"importPath": "@plasmicpkgs/plasmic-basic-components", "defaultExport": false, "displayName": "Side Effect", "importName": "SideEffect", "description": "Run actions on load, unload, and when data changes.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "169KSwIlWIPK": {"uuid": "5HCry2LpS1FS", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "QqdN0XR0MgfX": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "SF76h0MdCbHi", "parent": null, "locked": null, "vsettings": [{"__ref": "UuU2en3rxOtX"}], "__type": "TplTag"}, "IO7KN06x7tsN": {"uuid": "K-mWGqrHefEp", "name": "hostless-side-effect", "params": [{"__ref": "JvX8g98s8CCN"}, {"__ref": "feVpEq6aDAnh"}, {"__ref": "QAF1HdU64q_T"}], "states": [], "tplTree": {"__ref": "QqdN0XR0MgfX"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "169KSwIlWIPK"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "fQ4AjmKcRIT4"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "iMZeBdvV6UTF": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "UuU2en3rxOtX": {"variants": [{"__ref": "169KSwIlWIPK"}], "args": [], "attrs": {}, "rs": {"__ref": "iMZeBdvV6UTF"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "uRPTjK_KIEM5": {"name": "onMount", "uuid": "mmP85x7Mqwqu", "__type": "Var"}, "JvX8g98s8CCN": {"type": {"__ref": "ZgAq0jIJVBd_"}, "variable": {"__ref": "uRPTjK_KIEM5"}, "uuid": "VJkMeUb1U8XZ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "On load", "about": "Actions to run when this Side Effect component is mounted.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "Gnk3v5GRxKi0": {"name": "onUnmount", "uuid": "fZLH4Vamwuf6", "__type": "Var"}, "feVpEq6aDAnh": {"type": {"__ref": "--BjLkhZKn7p"}, "variable": {"__ref": "Gnk3v5GRxKi0"}, "uuid": "lpl14zu6ca8B", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "On unload", "about": "Actions to run when this Side Effect component is unmounted.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "_-c_hLNpYKJn": {"name": "deps", "uuid": "LWTLJW_Yl_GG", "__type": "Var"}, "QAF1HdU64q_T": {"type": {"__ref": "lNg747luop_P"}, "variable": {"__ref": "_-c_hLNpYKJn"}, "uuid": "wj0IPp4isarG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "When data changes", "about": "List of values which should trigger a re-run of the actions if changed.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "0aNeVPi5XpSa": {"name": "plasmic-basic-components", "npmPkg": ["@plasmicpkgs/plasmic-basic-components"], "cssImport": [], "deps": [], "registerCalls": ["registerIframe", "registerVideo", "registerEmbed", "registerDataProvider", "registerConditionGuard", "registerSideEffect"], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "ZgAq0jIJVBd_": {"name": "func", "params": [], "__type": "FunctionType"}, "--BjLkhZKn7p": {"name": "func", "params": [], "__type": "FunctionType"}, "lNg747luop_P": {"name": "any", "__type": "AnyType"}, "xeO--FAL1Khb": {"importPath": "@plasmicpkgs/plasmic-basic-components", "defaultExport": false, "displayName": "Timer", "importName": "Timer", "description": "Run something periodically", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "tY8MGiZAhddF": {"uuid": "R6jVl64am4sX", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "-JYPoSajQpio": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "hIUws3RKkAWW", "parent": null, "locked": null, "vsettings": [{"__ref": "q0Lugb1QVHe2"}], "__type": "TplTag"}, "wpKxI3HMxpwz": {"uuid": "SpkzmXJqg27p", "name": "hostless-timer", "params": [{"__ref": "4JHusNCcEisj"}, {"__ref": "R85JX9qEqiE9"}, {"__ref": "6FLWRPmF-_qD"}, {"__ref": "H-UWoTzVPzBL"}], "states": [], "tplTree": {"__ref": "-JYPoSajQpio"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "tY8MGiZAhddF"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "xeO--FAL1Khb"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "ZlDzXHTOVPzq": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "q0Lugb1QVHe2": {"variants": [{"__ref": "tY8MGiZAhddF"}], "args": [], "attrs": {}, "rs": {"__ref": "ZlDzXHTOVPzq"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "pB2pthxe7dqo": {"name": "onTick", "uuid": "WfX4q4UcfCtJ", "__type": "Var"}, "4JHusNCcEisj": {"type": {"__ref": "TFdBPmDpMdPR"}, "variable": {"__ref": "pB2pthxe7dqo"}, "uuid": "00VqhRkWRnWA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Run this every interval", "about": "Actions to run periodically", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "KO1MKkLlYwed": {"name": "isRunning", "uuid": "ZfmWdxvkf0Vr", "__type": "Var"}, "R85JX9qEqiE9": {"type": {"__ref": "nyrH9-l3_zii"}, "variable": {"__ref": "KO1MKkLlYwed"}, "uuid": "IsqBCHi9DORo", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "XVX-9dNqgWpl"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Is Running?", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "FkDWtrX-YTO_": {"name": "intervalSeconds", "uuid": "2Y9sJMbv3_Jq", "__type": "Var"}, "6FLWRPmF-_qD": {"type": {"__ref": "UqMMa2DK23wK"}, "variable": {"__ref": "FkDWtrX-YTO_"}, "uuid": "eAfIM-Gspk3g", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Interval (seconds)", "about": "Interval in seconds", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "VBG_TSUruaw0": {"name": "any", "__type": "AnyType"}, "SMa5K9HkrqgT": {"name": "arg", "argName": "event", "displayName": null, "type": {"__ref": "VBG_TSUruaw0"}, "__type": "ArgType"}, "YUObO3keY3SM": {"name": "func", "params": [{"__ref": "SMa5K9HkrqgT"}], "__type": "FunctionType"}, "chVH0Eld_5VH": {"name": "onLoad", "uuid": "JGY-84IybTdk", "__type": "Var"}, "MTSIbR4xXJ7P": {"type": {"__ref": "YUObO3keY3SM"}, "variable": {"__ref": "chVH0Eld_5VH"}, "uuid": "JSiRCnV_cKab", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "TFdBPmDpMdPR": {"name": "func", "params": [], "__type": "FunctionType"}, "nyrH9-l3_zii": {"name": "bool", "__type": "BoolType"}, "XVX-9dNqgWpl": {"code": "true", "fallback": null, "__type": "CustomCode"}, "UqMMa2DK23wK": {"name": "num", "__type": "<PERSON><PERSON>"}, "vVDpQmp00nk_": {"name": "bool", "__type": "BoolType"}, "fGS566H6Nw4C": {"code": "false", "fallback": null, "__type": "CustomCode"}, "jGqDkqFcYNTF": {"name": "runWhileEditing", "uuid": "TEGc1mVU9j9X", "__type": "Var"}, "H-UWoTzVPzBL": {"type": {"__ref": "vVDpQmp00nk_"}, "variable": {"__ref": "jGqDkqFcYNTF"}, "uuid": "GrtrTMYojmcy", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "fGS566H6Nw4C"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Run while editing", "about": "Normally this only runs in the live site or in preview mode, but you can force it to run even while you are editing in the canvas (Please enable interactive mode to observe state changes)", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "6q1HDtNt4qJj": {"uuid": "55OsCXXBhlA5", "pkgId": "5f6e2797-**************-fad89c4fa1c3", "projectId": "caTPwKxj5ZrD9LQ7DMdK4Z", "version": "3.32.0", "name": "plasmic-basic-components", "site": {"__ref": "3935001"}, "__type": "ProjectDependency"}}, "deps": [], "version": "245-code-component-meta-add-refActions"}], ["nDFnhe42wpQjup7umWdQEn", {"root": "RSCPpAZCId0k", "map": {"ibw1dRzB6jrs": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Lil7EmBu70nR": {"name": "Default Typography", "rs": {"__ref": "ibw1dRzB6jrs"}, "preview": null, "uuid": "zdOnkJrna4W-", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "eS6svP11ctUd": {"defaultStyle": {"__ref": "Lil7EmBu70nR"}, "styles": [], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "YaH5yOkOuXCR": {"uuid": "nnfejcEYApPs", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "RSCPpAZCId0k": {"components": [{"__ref": "Jg9JKYVLgJIR"}, {"__ref": "lGTG5uDbuc4V"}, {"__ref": "HjKZJjh4N7Nf"}, {"__ref": "b8iFGp3XGZsu"}], "arenas": [], "pageArenas": [{"__ref": "BN5Qc6nQkuuf"}], "componentArenas": [{"__ref": "id7B2ES67Pre"}], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "YaH5yOkOuXCR"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "eS6svP11ctUd"}], "activeTheme": null, "imageAssets": [{"__ref": "rxUOs-eIvLZ9"}, {"__ref": "K2AgylbEF-8g"}], "projectDependencies": [{"__xref": {"uuid": "220522b3-6ffd-4033-bed2-11941b5fdc8d", "iid": "iCXtEyddYb_n"}}, {"__xref": {"uuid": "c54f9d3e-56d6-4751-baa0-8b2a93ae8e0c", "iid": "JZxr36S-RvHI"}}, {"__xref": {"uuid": "6a1dbdef-31d7-48d6-af32-426d27ae140d", "iid": "l9IiOSK8vl_A"}}, {"__xref": {"uuid": "b12aeddb-2fe6-4c26-8574-36f3b8b72685", "iid": "CaK9Q6dJPob4"}}, {"__xref": {"uuid": "2e3e634b-0431-4dc5-842b-a652da3a62ca", "iid": "pX-idRyfhQc4"}}, {"__xref": {"uuid": "822c0aa6-7b6c-4ddf-b468-e1583a1ee933", "iid": "05cLlBhqs78l"}}, {"__xref": {"uuid": "5399a8a4-8a49-4637-a6bb-b1e3e071c73f", "iid": "YMHRoh-DyGbA"}}, {"__xref": {"uuid": "65946b0b-b4c4-492a-80ee-a7736ae3a735", "iid": "MMDp_wX48vMH"}}, {"__xref": {"uuid": "388ed04e-61fd-4211-8b2d-352ee2cfd5b8", "iid": "Hs98jzrpZ22M"}}, {"__xref": {"uuid": "c128d63f-01ec-4d8b-a3d9-f1991d044290", "iid": "5AxyDDc4rLZo"}}, {"__xref": {"uuid": "6cd11ba1-885b-4a6c-82fa-fbf5829e5ce5", "iid": "Q9vmaQgE1Ax_"}}, {"__xref": {"uuid": "13bb2354-599c-43e0-a9b8-c4f3733e78f5", "iid": "nsa_lh-2NFJ-"}}, {"__xref": {"uuid": "ea7d9df6-a1da-4893-8bcc-a04096a23010", "iid": "M9P8qKfL7iiI"}}, {"__xref": {"uuid": "de7a14b8-3322-4ce1-9b9a-cc95f35bfd4b", "iid": "wfUnX1A70rKM"}}, {"__xref": {"uuid": "e19497fe-1015-4c06-bb56-693e52674193", "iid": "mO0BAa8o2TJO"}}, {"__xref": {"uuid": "6718f4f5-7b8d-4fd0-a544-9ba8a7269ac4", "iid": "vKhXkwMYKb8w"}}, {"__xref": {"uuid": "f28b72d1-77e8-4010-9170-a783c5560c08", "iid": "mEMAYXhmv4Hd"}}, {"__xref": {"uuid": "d52194cc-ad47-46c8-8d5d-955ad010b9e7", "iid": "OXKwEI208S_B"}}, {"__xref": {"uuid": "17eae61b-545f-4d2a-a33e-7637d79212ba", "iid": "rIChVUdMxdV6"}}, {"__xref": {"uuid": "ea54091c-dcfb-47e2-86b4-fe360c62f6ec", "iid": "AdZ9R7hfnWc_"}}, {"__xref": {"uuid": "7bfdcdde-66e0-469e-9022-02955a3f8f15", "iid": "6q1HDtNt4qJj"}}], "activeScreenVariantGroup": null, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {"button": {"__ref": "b8iFGp3XGZsu"}}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "Jg9JKYVLgJIR": {"uuid": "J7m1SxWGI_9g", "name": "hostless-plasmic-head", "params": [{"__ref": "Uyf5LOwm9Qjg"}, {"__ref": "_pw1ir4GwjzL"}, {"__ref": "VQbgQQ7zaMeT"}, {"__ref": "Fv1khCl4OoCC"}], "states": [], "tplTree": {"__ref": "AveMnSD9JN8R"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "1fA_UeCuCzba"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "-FM7S_bIO2zY"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "lGTG5uDbuc4V": {"uuid": "KAmv6XxYVFlk", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "hKsdzIkEtrB3"}, {"__ref": "nG1i4n8DlJTG"}, {"__ref": "ZCQHguPXdLli"}, {"__ref": "XVyy1Hd7n0jJ"}, {"__ref": "L5x4nka0dwn5"}], "states": [], "tplTree": {"__ref": "HCxfWalSA7ZM"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "ljRgkdN-sdfg"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "K8aeJ0sM5CdP"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component"}, "Uyf5LOwm9Qjg": {"type": {"__ref": "L6NdkzTQ3wv5"}, "variable": {"__ref": "5xMdqYO-29iq"}, "uuid": "OErLIe285cuG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "_pw1ir4GwjzL": {"type": {"__ref": "aBoPqGqePek_"}, "variable": {"__ref": "w1f7i5IQ7Skk"}, "uuid": "STCaSftCfASU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "VQbgQQ7zaMeT": {"type": {"__ref": "e-xHiXZX5sZu"}, "variable": {"__ref": "0Jex4cgH0y6E"}, "uuid": "rFXxcxUZ-rTd", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "Fv1khCl4OoCC": {"type": {"__ref": "v-y1XLs7HAmj"}, "variable": {"__ref": "80X0OmmOL-lX"}, "uuid": "4n4-R14r7-0Q", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "AveMnSD9JN8R": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "BM9bjYlGrfu7", "parent": null, "locked": null, "vsettings": [{"__ref": "n5MkJOtvYOHL"}], "__type": "TplTag"}, "1fA_UeCuCzba": {"uuid": "qXCs9CyPwsFu", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "-FM7S_bIO2zY": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "hKsdzIkEtrB3": {"type": {"__ref": "iGlqXTrzjUdI"}, "variable": {"__ref": "VjMR-iJA8j7s"}, "uuid": "pEfBGp9etULv", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "nG1i4n8DlJTG": {"type": {"__ref": "-XcF5vq4dh_F"}, "variable": {"__ref": "TQfCsFicz6nW"}, "uuid": "GBwfS3MtMP5u", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "ZCQHguPXdLli": {"type": {"__ref": "0WmvidWS0kBa"}, "tplSlot": {"__ref": "-1AR_ZdjgEtF"}, "variable": {"__ref": "g2JO4s11woP8"}, "uuid": "Ru43uB9sQS_d", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "XVyy1Hd7n0jJ": {"type": {"__ref": "kUsSsJwk5_4i"}, "variable": {"__ref": "g8SQr3AAWqf_"}, "uuid": "joLDraa-bpMK", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "L5x4nka0dwn5": {"type": {"__ref": "NoH09307MXQS"}, "variable": {"__ref": "SLxX7uvJ4aIw"}, "uuid": "pqqBB5RBVZDu", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "HCxfWalSA7ZM": {"tag": "div", "name": null, "children": [{"__ref": "-1AR_ZdjgEtF"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "T14jz0R4y9bf", "parent": null, "locked": null, "vsettings": [{"__ref": "uYyk3lOHQlTA"}], "__type": "TplTag"}, "ljRgkdN-sdfg": {"uuid": "7ePnOZCS2Xdh", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "K8aeJ0sM5CdP": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "L6NdkzTQ3wv5": {"name": "text", "__type": "Text"}, "5xMdqYO-29iq": {"name": "title", "uuid": "tBQV_cHYoxN5", "__type": "Var"}, "aBoPqGqePek_": {"name": "text", "__type": "Text"}, "w1f7i5IQ7Skk": {"name": "description", "uuid": "BvX2tJgrz9ey", "__type": "Var"}, "e-xHiXZX5sZu": {"name": "img", "__type": "Img"}, "0Jex4cgH0y6E": {"name": "image", "uuid": "I3pMzmb5zQeK", "__type": "Var"}, "v-y1XLs7HAmj": {"name": "text", "__type": "Text"}, "80X0OmmOL-lX": {"name": "canonical", "uuid": "T1yrO6w_8Sdd", "__type": "Var"}, "n5MkJOtvYOHL": {"variants": [{"__ref": "1fA_UeCuCzba"}], "args": [], "attrs": {}, "rs": {"__ref": "H55Ek2EwCzS-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "iGlqXTrzjUdI": {"name": "any", "__type": "AnyType"}, "VjMR-iJA8j7s": {"name": "dataOp", "uuid": "BN2Qc8t_fEh8", "__type": "Var"}, "-XcF5vq4dh_F": {"name": "text", "__type": "Text"}, "TQfCsFicz6nW": {"name": "name", "uuid": "yZPQk8_56gwf", "__type": "Var"}, "0WmvidWS0kBa": {"name": "renderFunc", "params": [{"__ref": "fRX6i9xWeQeD"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "g2JO4s11woP8": {"name": "children", "uuid": "8m06bwQsxHjG", "__type": "Var"}, "kUsSsJwk5_4i": {"name": "num", "__type": "<PERSON><PERSON>"}, "g8SQr3AAWqf_": {"name": "pageSize", "uuid": "wE3wAXW2z5RK", "__type": "Var"}, "NoH09307MXQS": {"name": "num", "__type": "<PERSON><PERSON>"}, "SLxX7uvJ4aIw": {"name": "pageIndex", "uuid": "XX4sRWtHVzsk", "__type": "Var"}, "-1AR_ZdjgEtF": {"param": {"__ref": "ZCQHguPXdLli"}, "defaultContents": [], "uuid": "-jDFx3S8CegQ", "parent": {"__ref": "HCxfWalSA7ZM"}, "locked": null, "vsettings": [{"__ref": "KXQsXwsChmQf"}], "__type": "TplSlot"}, "uYyk3lOHQlTA": {"variants": [{"__ref": "ljRgkdN-sdfg"}], "args": [], "attrs": {}, "rs": {"__ref": "TVHw1h3VgfrA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "H55Ek2EwCzS-": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "fRX6i9xWeQeD": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "MxvZUtRGI_TL"}, "__type": "ArgType"}, "KXQsXwsChmQf": {"variants": [{"__ref": "ljRgkdN-sdfg"}], "args": [], "attrs": {}, "rs": {"__ref": "meMtrT7vvYGE"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "TVHw1h3VgfrA": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "MxvZUtRGI_TL": {"name": "any", "__type": "AnyType"}, "meMtrT7vvYGE": {"values": {}, "mixins": [], "__type": "RuleSet"}, "HjKZJjh4N7Nf": {"uuid": "Ip9trO3mxkyx", "name": "Homepage", "params": [{"__ref": "k-FfROanlGzR"}, {"__ref": "z39jU4WU0JIz"}, {"__ref": "R9GlckkK7pKv"}, {"__ref": "WEL5gPlgV--8"}], "states": [{"__ref": "AhwOKQpbUH2c"}, {"__ref": "zgBFCeattg8x"}], "tplTree": {"__ref": "HrtMv6GQLRZI"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "hVgCKqmRll81"}], "variantGroups": [], "pageMeta": {"__ref": "fQJrSq9BJdfQ"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component"}, "BN5Qc6nQkuuf": {"component": {"__ref": "HjKZJjh4N7Nf"}, "matrix": {"__ref": "eP-ZfMGVuaeh"}, "customMatrix": {"__ref": "mf5u7A3F8a75"}, "__type": "PageArena"}, "HrtMv6GQLRZI": {"tag": "div", "name": null, "children": [{"__ref": "Q8so_Ql9-9V1"}, {"__ref": "vXWl7ouW-J8q"}, {"__ref": "YUQ9_L0YDQYW"}, {"__ref": "nws3nHJS50oH"}, {"__ref": "zkFy1agBmPAy"}, {"__ref": "6EdDYdqr1VxK"}, {"__ref": "bbgy06ErXTK6"}, {"__ref": "iQDRtlSZfCF-"}, {"__ref": "GtNB6ykibwpx"}, {"__ref": "Fdm5kSmFzYOl"}, {"__ref": "qzX_KOZa8W_G"}, {"__ref": "eDH3neF0EPxT"}, {"__ref": "BylsKX3Xpmaf"}, {"__ref": "dXTbidJ3j01n"}, {"__ref": "wkuMVdYIK9KL"}, {"__ref": "VZPr57zkPFB8"}, {"__ref": "haxzZ8r7pFzz"}, {"__ref": "y_b2HY480hF4"}, {"__ref": "KmQ54ZOjb4Tb"}, {"__ref": "L5z0U_TjSR5-"}, {"__ref": "kEIT9W3d7-FQ"}, {"__ref": "td8Blm8kozNk"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Qi9jyXMNBF25", "parent": null, "locked": null, "vsettings": [{"__ref": "sBpAl8aLFduZ"}], "__type": "TplTag"}, "hVgCKqmRll81": {"uuid": "yIaDVEt8uzQy", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "fQJrSq9BJdfQ": {"path": "/", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "eP-ZfMGVuaeh": {"rows": [{"__ref": "kekvYLlBuJ2J"}], "__type": "ArenaFrameGrid"}, "mf5u7A3F8a75": {"rows": [{"__ref": "AwuolkZm1Gc0"}], "__type": "ArenaFrameGrid"}, "sBpAl8aLFduZ": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "cekNruzcgO5C"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kekvYLlBuJ2J": {"cols": [{"__ref": "P7VMMVEIBWoi"}], "rowKey": {"__ref": "hVgCKqmRll81"}, "__type": "ArenaFrameRow"}, "AwuolkZm1Gc0": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "cekNruzcgO5C": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center", "justify-content": "flex-start", "align-items": "center", "grid-row-gap": "8px"}, "mixins": [], "__type": "RuleSet"}, "P7VMMVEIBWoi": {"frame": {"__ref": "WdyNj3pIiJ3q"}, "cellKey": null, "__type": "ArenaFrameCell"}, "WdyNj3pIiJ3q": {"uuid": "UgGpORNyVTXI", "width": 1366, "height": 768, "container": {"__ref": "sg4LBAF4vCab"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "hVgCKqmRll81"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "sg4LBAF4vCab": {"name": null, "component": {"__ref": "HjKZJjh4N7Nf"}, "uuid": "-VFpfyIszD6O", "parent": null, "locked": null, "vsettings": [{"__ref": "_rhFNhOnS8gx"}], "__type": "TplComponent"}, "_rhFNhOnS8gx": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "0sGl6LYvylH8"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "0sGl6LYvylH8": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Q8so_Ql9-9V1": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "AVT-ffUaA8Z3", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "DLwZWiN1W6f_"}], "__type": "TplTag"}, "DLwZWiN1W6f_": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "PFuZZH2T0szA"}, "dataCond": null, "dataRep": null, "text": {"__ref": "kaLmR87q1RlP"}, "columnsConfig": null, "__type": "VariantSetting"}, "PFuZZH2T0szA": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "Jw2mg-GF6dhl": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "dKPF_PHxs-_Z", "parent": {"__ref": "vXWl7ouW-J8q"}, "locked": null, "vsettings": [{"__ref": "vQ_wHwXt-1IV"}], "__type": "TplTag"}, "vQ_wHwXt-1IV": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "ieE1wrAg5xFJ"}, "dataCond": null, "dataRep": null, "text": {"__ref": "GCAWPeLSoFfS"}, "columnsConfig": null, "__type": "VariantSetting"}, "ieE1wrAg5xFJ": {"values": {"width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "GCAWPeLSoFfS": {"expr": {"__ref": "esHriUfwgSiw"}, "html": false, "__type": "ExprText"}, "esHriUfwgSiw": {"code": "(`Copy to clipboard type: \"${typeof $$.copyToClipboard}\"`)", "fallback": {"__ref": "N96p4FhCLACn"}, "__type": "CustomCode"}, "N96p4FhCLACn": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "rxUOs-eIvLZ9": {"uuid": "FuRQu5x652-c", "name": "check.svg", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAyNCAyNCIgaGVpZ2h0PSIxZW0iIHdpZHRoPSIxZW0iIHN0eWxlPSJmaWxsOiBjdXJyZW50Y29sb3I7Ij4KICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTE4LjQxNiA1Ljg3NmEuNzUuNzUgMCAwMS4yMDggMS4wNEwxMS40MiAxNy43MjFhMS43NSAxLjc1IDAgMDEtMi44NzEuMDZsLTMuMTU2LTQuMzRhLjc1Ljc1IDAgMTExLjIxNC0uODgybDMuMTU1IDQuMzM5YS4yNS4yNSAwIDAwLjQxLS4wMDlsNy4yMDQtMTAuODA1YS43NS43NSAwIDAxMS4wNC0uMjA4eiIgZmlsbD0iY3VycmVudENvbG9yIi8+Cjwvc3ZnPg==", "width": 150, "height": 150, "aspectRatio": 1000000, "__type": "ImageAsset"}, "K2AgylbEF-8g": {"uuid": "Vji6PUUgxZlb", "name": "icon", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHN0cm9rZT0iY3VycmVudENvbG9yIiBmaWxsPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMCIgdmlld0JveD0iMCAwIDE2IDE2IiBoZWlnaHQ9IjFlbSIgd2lkdGg9IjFlbSI+CiAgPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBkPSJNMSA4YS41LjUgMCAwMS41LS41aDExLjc5M2wtMy4xNDctMy4xNDZhLjUuNSAwIDAxLjcwOC0uNzA4bDQgNGEuNS41IDAgMDEwIC43MDhsLTQgNGEuNS41IDAgMDEtLjcwOC0uNzA4TDEzLjI5MyA4LjVIMS41QS41LjUgMCAwMTEgOHoiIHN0cm9rZT0ibm9uZSIvPgo8L3N2Zz4=", "width": 150, "height": 150, "aspectRatio": 1000000, "__type": "ImageAsset"}, "b8iFGp3XGZsu": {"uuid": "ZBTAwmuTOLm7", "name": "<PERSON><PERSON>", "params": [{"__ref": "wLlq7c86cpEZ"}, {"__ref": "fM3htqPnoo3O"}, {"__ref": "tx2HNnXEFibk"}, {"__ref": "8imcyzt3b8AA"}, {"__ref": "PI0qZTj0EyKV"}, {"__ref": "jTLIPe-WqvTW"}, {"__ref": "wB5Of8MPAvce"}, {"__ref": "182S1uz0gVw6"}, {"__ref": "YErMFPjRvvOg"}, {"__ref": "mfUek-k0i6FJ"}, {"__ref": "uTE-pY4Joe6x"}, {"__ref": "mNGbV7bnQvke"}, {"__ref": "9_jD0sbKqevw"}, {"__ref": "iX_WxCxco_Ui"}, {"__ref": "nE9VgO4q8s9i"}, {"__ref": "tgj01So5q_Ew"}, {"__ref": "nfykOakTQySo"}, {"__ref": "o3dLQFBvU_t6"}], "states": [{"__ref": "1BgMob2-0fko"}, {"__ref": "oy9VDHLftxEG"}, {"__ref": "EJ5S1yrVYPU2"}, {"__ref": "uzIf7g-3Pa-X"}, {"__ref": "3AdW8443Pxpw"}, {"__ref": "v95Y7AuyihRO"}], "tplTree": {"__ref": "M-gX5_3ll-AQ"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "tV1959nPHhqn"}, {"__ref": "unYEfNHe7XRn"}, {"__ref": "98bCPPxdZwa4"}, {"__ref": "NlNWf7-MImkI"}, {"__ref": "uxKEiZcqedp9"}], "variantGroups": [{"__ref": "zVHQZ_BbL9Dp"}, {"__ref": "gb3mij5PVVAk"}, {"__ref": "3k2Lm6KBHQjO"}, {"__ref": "Nxmurwo5uasz"}, {"__ref": "oyZ8keBITnlE"}, {"__ref": "as0v2YszzpAL"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": {"__ref": "ljokjc_MYqeW"}, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true, "__type": "Component"}, "id7B2ES67Pre": {"component": {"__ref": "b8iFGp3XGZsu"}, "matrix": {"__ref": "_sg_FTgt82hP"}, "customMatrix": {"__ref": "3uFk3EhiZL8D"}, "__type": "ComponentArena"}, "ahJBCyYiLSyn": {"name": "copyToClipboardBtn", "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "EJaN4OoFM3Yj", "parent": {"__ref": "vXWl7ouW-J8q"}, "locked": null, "vsettings": [{"__ref": "EpquudkamF19"}], "__type": "TplComponent"}, "wLlq7c86cpEZ": {"type": {"__ref": "-vaHlI4w3CtU"}, "tplSlot": {"__ref": "UefTUd8b38Bc"}, "variable": {"__ref": "feiKaLSDUy8K"}, "uuid": "3uDaAAOt4P8n", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false, "__type": "SlotParam"}, "fM3htqPnoo3O": {"type": {"__ref": "swTsoRaStxYw"}, "state": {"__ref": "1BgMob2-0fko"}, "variable": {"__ref": "LiQXDtO0IstC"}, "uuid": "KB-jsGqfImiQ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "tx2HNnXEFibk": {"type": {"__ref": "_PcRx3L2aBek"}, "state": {"__ref": "oy9VDHLftxEG"}, "variable": {"__ref": "VwrQ2XXwrG9d"}, "uuid": "9j50kvGUY5hE", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "8imcyzt3b8AA": {"type": {"__ref": "Bv_aTzNajWUx"}, "tplSlot": {"__ref": "NVB0ONqhxBF3"}, "variable": {"__ref": "X0FIQw9WhmmB"}, "uuid": "k3BP8C3YWWnQ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "PI0qZTj0EyKV": {"type": {"__ref": "IdLEfAUeaxxO"}, "tplSlot": {"__ref": "opcPkVDFUkfp"}, "variable": {"__ref": "FRHt0pDC4wxX"}, "uuid": "jQRfENqzNlKg", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "jTLIPe-WqvTW": {"type": {"__ref": "SnB-BNWlZjHo"}, "state": {"__ref": "EJ5S1yrVYPU2"}, "variable": {"__ref": "Jf9hgcPLgk0s"}, "uuid": "1KKFwb23OtNr", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "wB5Of8MPAvce": {"type": {"__ref": "gmGu4kQZo_C8"}, "variable": {"__ref": "1K86vbrEI3Kz"}, "uuid": "Y9HPV1TLMPIQ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "182S1uz0gVw6": {"type": {"__ref": "duFWdbfVI3i3"}, "state": {"__ref": "v95Y7AuyihRO"}, "variable": {"__ref": "bJt5TeqDC6tE"}, "uuid": "d7NoI-r4jKbu", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "YErMFPjRvvOg": {"type": {"__ref": "s7tZ0U8bi8dl"}, "state": {"__ref": "3AdW8443Pxpw"}, "variable": {"__ref": "RToa0vPz7dKl"}, "uuid": "Zmlz5n4q6Usl", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "mfUek-k0i6FJ": {"type": {"__ref": "Nz87imS00bUI"}, "state": {"__ref": "uzIf7g-3Pa-X"}, "variable": {"__ref": "74ISIPN9jbZ4"}, "uuid": "ceZfTdQ48hYN", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "uTE-pY4Joe6x": {"type": {"__ref": "SnFJU93eOfAP"}, "variable": {"__ref": "d1TaWPt9g52r"}, "uuid": "MY39KcSvmWvt", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Submits form?", "about": "Whether clicking on this button submits the enclosing form or not", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "mNGbV7bnQvke": {"type": {"__ref": "nRkZSEjDtqPq"}, "variable": {"__ref": "RHUuUJ5Rv01s"}, "uuid": "m7wpEI8Ow7W-", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Open in new tab?", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "9_jD0sbKqevw": {"type": {"__ref": "NwReSVIB_p7l"}, "state": {"__ref": "1BgMob2-0fko"}, "variable": {"__ref": "VY9dqOCbVyyk"}, "uuid": "ZDXe-fq4kDNs", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "iX_WxCxco_Ui": {"type": {"__ref": "E6yoROz7EL1_"}, "state": {"__ref": "oy9VDHLftxEG"}, "variable": {"__ref": "NVLRM6xuiRc2"}, "uuid": "tb2fnP4q9yUd", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "nE9VgO4q8s9i": {"type": {"__ref": "MbwAuz9Qwk7t"}, "state": {"__ref": "EJ5S1yrVYPU2"}, "variable": {"__ref": "u5lj2Sw1gJom"}, "uuid": "VB3dnJnzF0qm", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "tgj01So5q_Ew": {"type": {"__ref": "8w14zX148LWj"}, "state": {"__ref": "uzIf7g-3Pa-X"}, "variable": {"__ref": "EKxr6FHGNQw6"}, "uuid": "l9GHvJab5H0x", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "nfykOakTQySo": {"type": {"__ref": "GoFXLxOiXQ5l"}, "state": {"__ref": "3AdW8443Pxpw"}, "variable": {"__ref": "rD3tS7nUvI2e"}, "uuid": "hXW5zDW0I4jR", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "o3dLQFBvU_t6": {"type": {"__ref": "q3jlRzko8D6m"}, "state": {"__ref": "v95Y7AuyihRO"}, "variable": {"__ref": "zYwTADb7V2VY"}, "uuid": "OmTWtSyv_85Z", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "1BgMob2-0fko": {"variantGroup": {"__ref": "zVHQZ_BbL9Dp"}, "param": {"__ref": "fM3htqPnoo3O"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "9_jD0sbKqevw"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "oy9VDHLftxEG": {"variantGroup": {"__ref": "gb3mij5PVVAk"}, "param": {"__ref": "tx2HNnXEFibk"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "iX_WxCxco_Ui"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "EJ5S1yrVYPU2": {"variantGroup": {"__ref": "3k2Lm6KBHQjO"}, "param": {"__ref": "jTLIPe-WqvTW"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "nE9VgO4q8s9i"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "uzIf7g-3Pa-X": {"variantGroup": {"__ref": "Nxmurwo5uasz"}, "param": {"__ref": "mfUek-k0i6FJ"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "tgj01So5q_Ew"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "3AdW8443Pxpw": {"variantGroup": {"__ref": "oyZ8keBITnlE"}, "param": {"__ref": "YErMFPjRvvOg"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "nfykOakTQySo"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "v95Y7AuyihRO": {"variantGroup": {"__ref": "as0v2YszzpAL"}, "param": {"__ref": "182S1uz0gVw6"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "o3dLQFBvU_t6"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "M-gX5_3ll-AQ": {"tag": "button", "name": null, "children": [{"__ref": "zjR2GOyoPiIp"}, {"__ref": "LL2FQeTNlfCw"}, {"__ref": "Oflqu7F5tu41"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "zjpwudTzhC-u", "parent": null, "locked": null, "vsettings": [{"__ref": "Ogv1DEgq72t8"}, {"__ref": "9MgY4ZQ46nqJ"}, {"__ref": "PZzHgzr2lU37"}, {"__ref": "eRYaHZT8vljf"}, {"__ref": "U4809ujTzGU-"}, {"__ref": "DPTKeuGSr5NU"}, {"__ref": "M0pp9XTpRDEf"}, {"__ref": "fqjfb8vTx5jr"}, {"__ref": "hVeEh09vUcvb"}, {"__ref": "r3xrvtIEZvEQ"}, {"__ref": "wOvxKlbbKw9D"}, {"__ref": "MoRjBQCWu2e6"}, {"__ref": "wH4hE_RtXeLl"}, {"__ref": "ABAwdnJ2WtJV"}, {"__ref": "ajjDDU3fEyit"}, {"__ref": "8fNqRH_LIWEu"}, {"__ref": "Cwafg6vGdhNZ"}, {"__ref": "4tLKZIx1azF7"}, {"__ref": "voKiaf201qo5"}, {"__ref": "lcMsfaj21oCp"}, {"__ref": "GKfnsm3IU4IN"}, {"__ref": "m_q9uD2vdZAG"}, {"__ref": "-4huavY9r9Vc"}, {"__ref": "uITqzw_uujqU"}, {"__ref": "2b9DfpLcAAob"}, {"__ref": "BS-Lck4PEnjp"}, {"__ref": "qNI9pvANRT6Q"}, {"__ref": "K_ev4MtAqTXQ"}, {"__ref": "6xrqWV7lCJRG"}, {"__ref": "GiulHNRVXOPx"}, {"__ref": "rUkVtNsb3EBL"}, {"__ref": "TFslZnmWzPzd"}, {"__ref": "Bz6TEJPDvscT"}, {"__ref": "9m8PeAjGrrdD"}, {"__ref": "AsZnBqtBMJOj"}, {"__ref": "txnwKEHFRUV7"}, {"__ref": "61LnmROaM-PE"}, {"__ref": "zL6h96VilBUu"}, {"__ref": "AwCBGIbfverJ"}, {"__ref": "Cb_4BMr2Zuu9"}, {"__ref": "NRfb6dhasOQa"}, {"__ref": "uGWNuIQSlMd_"}, {"__ref": "hRY4jxQNcxLc"}, {"__ref": "lhG6qW5OaRDh"}, {"__ref": "yHqil5dJU95R"}, {"__ref": "aJt_JgLmcCn4"}, {"__ref": "0_DZg8doSgWA"}, {"__ref": "DK9XpknykPKi"}, {"__ref": "uzwOeWpqPH_f"}, {"__ref": "rM1OJMc9UCpd"}, {"__ref": "XscLBhVjCjZm"}, {"__ref": "2rueDsf13AFB"}, {"__ref": "aD8-wz0OhL5S"}, {"__ref": "y1HhOB_TIHbB"}, {"__ref": "HiyB6pUHoJ8-"}, {"__ref": "mLtQGRj_Jzpp"}, {"__ref": "Q9MXqHp-XoZ9"}, {"__ref": "yFyeJmbn_5dN"}, {"__ref": "HYmzWEZ9fmNs"}, {"__ref": "c23--BoKmdDc"}], "__type": "TplTag"}, "tV1959nPHhqn": {"uuid": "NULT3X0Le7eP", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "unYEfNHe7XRn": {"uuid": "fmU7vKRr4Net", "name": "", "selectors": [":focus-visible-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "98bCPPxdZwa4": {"uuid": "e8rAHDbpUbYG", "name": "", "selectors": [":focus-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "NlNWf7-MImkI": {"uuid": "YBWrgvpLC8qU", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "uxKEiZcqedp9": {"uuid": "v3NnWK02rkOS", "name": "", "selectors": [":active"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "zVHQZ_BbL9Dp": {"type": "component", "param": {"__ref": "fM3htqPnoo3O"}, "linkedState": {"__ref": "1BgMob2-0fko"}, "uuid": "n47nw5OBV4c4", "variants": [{"__ref": "pF6EHk9Uh077"}], "multi": false, "__type": "ComponentVariantGroup"}, "gb3mij5PVVAk": {"type": "component", "param": {"__ref": "tx2HNnXEFibk"}, "linkedState": {"__ref": "oy9VDHLftxEG"}, "uuid": "HaieLxVYOavg", "variants": [{"__ref": "dOBSQ071peO9"}], "multi": false, "__type": "ComponentVariantGroup"}, "3k2Lm6KBHQjO": {"type": "component", "param": {"__ref": "jTLIPe-WqvTW"}, "linkedState": {"__ref": "EJ5S1yrVYPU2"}, "uuid": "-nOuLCWT8fld", "variants": [{"__ref": "bi1zXsK_HcA4"}], "multi": false, "__type": "ComponentVariantGroup"}, "Nxmurwo5uasz": {"type": "component", "param": {"__ref": "mfUek-k0i6FJ"}, "linkedState": {"__ref": "uzIf7g-3Pa-X"}, "uuid": "jjNb2cbO-zzm", "variants": [{"__ref": "b7j7re9dG2TE"}, {"__ref": "8EgMkXce9H15"}, {"__ref": "_AU_l-6UE-d8"}], "multi": false, "__type": "ComponentVariantGroup"}, "oyZ8keBITnlE": {"type": "component", "param": {"__ref": "YErMFPjRvvOg"}, "linkedState": {"__ref": "3AdW8443Pxpw"}, "uuid": "tLUQEdkvM3Jf", "variants": [{"__ref": "pc8d1Faa0eKz"}, {"__ref": "KelsXKS-Sk5C"}], "multi": false, "__type": "ComponentVariantGroup"}, "as0v2YszzpAL": {"type": "component", "param": {"__ref": "182S1uz0gVw6"}, "linkedState": {"__ref": "v95Y7AuyihRO"}, "uuid": "XZrX_yMqky5R", "variants": [{"__ref": "O4dK_zihRgNS"}, {"__ref": "d9k4kNmKBrnH"}, {"__ref": "uSRzBesvIzds"}, {"__ref": "aW09_WHHY8No"}, {"__ref": "pFLgJ0hUYaIP"}, {"__ref": "Lz9juLj562BM"}, {"__ref": "W90MB8cWV6oZ"}, {"__ref": "R2Yy5npBRtiC"}, {"__ref": "6rtOzVF7i-g4"}, {"__ref": "RrdYYLlUF4He"}, {"__ref": "TxFEt7KZNNsK"}, {"__ref": "IToJ04sZLo_P"}, {"__ref": "gs8DsWBtgg2A"}], "multi": false, "__type": "ComponentVariantGroup"}, "ljokjc_MYqeW": {"type": "button", "__type": "PlumeInfo"}, "_sg_FTgt82hP": {"rows": [{"__ref": "kzxKGk-rQ0K2"}, {"__ref": "Zd__-2sapBjN"}, {"__ref": "10uPNoI31oj2"}, {"__ref": "iHhgxpk8MFIQ"}, {"__ref": "BRNEhQ21OJ6H"}, {"__ref": "lLZo2FwGALHv"}, {"__ref": "hwZgPal3NbFz"}], "__type": "ArenaFrameGrid"}, "3uFk3EhiZL8D": {"rows": [{"__ref": "zdIm-9vntXoH"}], "__type": "ArenaFrameGrid"}, "EpquudkamF19": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [{"__ref": "pG8utUOtcrSn"}, {"__ref": "t6MRzUkDp1Uh"}, {"__ref": "UgdNdTHJngeh"}, {"__ref": "mYNF11REy2Hm"}, {"__ref": "gdeUWViwEfIT"}, {"__ref": "8oDZD1HHGQxb"}], "attrs": {"onClick": {"__ref": "e9O8rdQhs40a"}}, "rs": {"__ref": "QmzXUucCWmAN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-vaHlI4w3CtU": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "feiKaLSDUy8K": {"name": "children", "uuid": "prbtUGk79tJH", "__type": "Var"}, "swTsoRaStxYw": {"name": "any", "__type": "AnyType"}, "LiQXDtO0IstC": {"name": "Show Start Icon", "uuid": "kT9JtVNCUI7X", "__type": "Var"}, "_PcRx3L2aBek": {"name": "any", "__type": "AnyType"}, "VwrQ2XXwrG9d": {"name": "Show End Icon", "uuid": "Eo6eY-Qkf8bv", "__type": "Var"}, "Bv_aTzNajWUx": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "X0FIQw9WhmmB": {"name": "start icon", "uuid": "srWirTjeRzgq", "__type": "Var"}, "IdLEfAUeaxxO": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "FRHt0pDC4wxX": {"name": "end icon", "uuid": "kGR8fGvbDD4O", "__type": "Var"}, "SnB-BNWlZjHo": {"name": "any", "__type": "AnyType"}, "Jf9hgcPLgk0s": {"name": "Is Disabled", "uuid": "8KX1qQP3CjS9", "__type": "Var"}, "gmGu4kQZo_C8": {"name": "href", "__type": "HrefType"}, "1K86vbrEI3Kz": {"name": "link", "uuid": "X3CU_dw97kCR", "__type": "Var"}, "duFWdbfVI3i3": {"name": "any", "__type": "AnyType"}, "bJt5TeqDC6tE": {"name": "Color", "uuid": "dZHOC2ca6Bkj", "__type": "Var"}, "s7tZ0U8bi8dl": {"name": "any", "__type": "AnyType"}, "RToa0vPz7dKl": {"name": "Size", "uuid": "TdHbcNuLIQCq", "__type": "Var"}, "Nz87imS00bUI": {"name": "any", "__type": "AnyType"}, "74ISIPN9jbZ4": {"name": "<PERSON><PERSON><PERSON>", "uuid": "NVKi3s5RsBvL", "__type": "Var"}, "SnFJU93eOfAP": {"name": "bool", "__type": "BoolType"}, "d1TaWPt9g52r": {"name": "submitsForm", "uuid": "F7noPJBAR38p", "__type": "Var"}, "nRkZSEjDtqPq": {"name": "bool", "__type": "BoolType"}, "RHUuUJ5Rv01s": {"name": "target", "uuid": "F2QU7RJUU5Ml", "__type": "Var"}, "NwReSVIB_p7l": {"name": "func", "params": [{"__ref": "KDWrUtGbU5cL"}], "__type": "FunctionType"}, "VY9dqOCbVyyk": {"name": "On Show Start Icon change", "uuid": "OyooY5SQH7nR", "__type": "Var"}, "E6yoROz7EL1_": {"name": "func", "params": [{"__ref": "2fIaGYQhBIVZ"}], "__type": "FunctionType"}, "NVLRM6xuiRc2": {"name": "On Show End Icon change", "uuid": "6idksv27Hqe0", "__type": "Var"}, "MbwAuz9Qwk7t": {"name": "func", "params": [{"__ref": "sU_LVT7WLMvk"}], "__type": "FunctionType"}, "u5lj2Sw1gJom": {"name": "On Is Disabled change", "uuid": "ATB6-1e4a1yj", "__type": "Var"}, "8w14zX148LWj": {"name": "func", "params": [{"__ref": "O11kQNmph6ki"}], "__type": "FunctionType"}, "EKxr6FHGNQw6": {"name": "On Shape change", "uuid": "fC5StfZITaJZ", "__type": "Var"}, "GoFXLxOiXQ5l": {"name": "func", "params": [{"__ref": "F0pU_Rir6zSj"}], "__type": "FunctionType"}, "rD3tS7nUvI2e": {"name": "On Size change", "uuid": "SgV6OMrKejdA", "__type": "Var"}, "q3jlRzko8D6m": {"name": "func", "params": [{"__ref": "IzGzi9qq5Uiv"}], "__type": "FunctionType"}, "zYwTADb7V2VY": {"name": "On Color change", "uuid": "HpfVo6GajWIs", "__type": "Var"}, "zjR2GOyoPiIp": {"tag": "div", "name": "start icon container", "children": [{"__ref": "NVB0ONqhxBF3"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "fPkXTUYOQmh-", "parent": {"__ref": "M-gX5_3ll-AQ"}, "locked": null, "vsettings": [{"__ref": "LHlOcs1ZGQgA"}, {"__ref": "cHjJXjUpBJ0A"}, {"__ref": "Mg_-fxExWOLM"}, {"__ref": "PBN9f7YRw8sL"}], "__type": "TplTag"}, "LL2FQeTNlfCw": {"tag": "div", "name": "content container", "children": [{"__ref": "UefTUd8b38Bc"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "_V_rkf6hwT2B", "parent": {"__ref": "M-gX5_3ll-AQ"}, "locked": null, "vsettings": [{"__ref": "ckasbCmP-1XJ"}, {"__ref": "hBZex34tElf8"}, {"__ref": "W-mPLqdhVaGK"}, {"__ref": "cPbxac1eGWJf"}, {"__ref": "IedXICp9HK8B"}], "__type": "TplTag"}, "Oflqu7F5tu41": {"tag": "div", "name": "end icon container", "children": [{"__ref": "opcPkVDFUkfp"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "kAWmVbkTY1pM", "parent": {"__ref": "M-gX5_3ll-AQ"}, "locked": null, "vsettings": [{"__ref": "PxTIhs8dbvQP"}, {"__ref": "StHqnGZNXAfT"}, {"__ref": "PA9P2SQUaCZ0"}, {"__ref": "-YLnweVrf2IN"}], "__type": "TplTag"}, "Ogv1DEgq72t8": {"variants": [{"__ref": "tV1959nPHhqn"}], "args": [], "attrs": {}, "rs": {"__ref": "oVlX18PPcH8K"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9MgY4ZQ46nqJ": {"variants": [{"__ref": "unYEfNHe7XRn"}], "args": [], "attrs": {}, "rs": {"__ref": "XRZA7fSApIdi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PZzHgzr2lU37": {"variants": [{"__ref": "98bCPPxdZwa4"}], "args": [], "attrs": {}, "rs": {"__ref": "hEtRdtzYkbWw"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eRYaHZT8vljf": {"variants": [{"__ref": "bi1zXsK_HcA4"}], "args": [], "attrs": {}, "rs": {"__ref": "zuSzwhhYdfrc"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "U4809ujTzGU-": {"variants": [{"__ref": "dOBSQ071peO9"}], "args": [], "attrs": {}, "rs": {"__ref": "zsVOB2pIyt-t"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "DPTKeuGSr5NU": {"variants": [{"__ref": "pF6EHk9Uh077"}], "args": [], "attrs": {}, "rs": {"__ref": "o39u2fmBKd61"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "M0pp9XTpRDEf": {"variants": [{"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "HMnKQudyeVgf"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "fqjfb8vTx5jr": {"variants": [{"__ref": "uxKEiZcqedp9"}], "args": [], "attrs": {}, "rs": {"__ref": "jiyCIvz9SrIC"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "hVeEh09vUcvb": {"variants": [{"__ref": "W90MB8cWV6oZ"}], "args": [], "attrs": {}, "rs": {"__ref": "4d85HdIwuyy6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "r3xrvtIEZvEQ": {"variants": [{"__ref": "W90MB8cWV6oZ"}, {"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "pi8SX4LTGKih"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "wOvxKlbbKw9D": {"variants": [{"__ref": "W90MB8cWV6oZ"}, {"__ref": "uxKEiZcqedp9"}], "args": [], "attrs": {}, "rs": {"__ref": "JKeUQuSx0amx"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MoRjBQCWu2e6": {"variants": [{"__ref": "R2Yy5npBRtiC"}], "args": [], "attrs": {}, "rs": {"__ref": "mIO_WZ4CgW8x"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "wH4hE_RtXeLl": {"variants": [{"__ref": "R2Yy5npBRtiC"}, {"__ref": "uxKEiZcqedp9"}], "args": [], "attrs": {}, "rs": {"__ref": "cBGAwu8BEK6P"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ABAwdnJ2WtJV": {"variants": [{"__ref": "R2Yy5npBRtiC"}, {"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "aJ0eqM06oYhw"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ajjDDU3fEyit": {"variants": [{"__ref": "uSRzBesvIzds"}], "args": [], "attrs": {}, "rs": {"__ref": "74kTZsIn79AD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "8fNqRH_LIWEu": {"variants": [{"__ref": "uSRzBesvIzds"}, {"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "ijYYHf54whCx"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Cwafg6vGdhNZ": {"variants": [{"__ref": "uSRzBesvIzds"}, {"__ref": "uxKEiZcqedp9"}], "args": [], "attrs": {}, "rs": {"__ref": "Gtg7HNHSUi4e"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4tLKZIx1azF7": {"variants": [{"__ref": "aW09_WHHY8No"}], "args": [], "attrs": {}, "rs": {"__ref": "bjqfHAEbFHTJ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "voKiaf201qo5": {"variants": [{"__ref": "aW09_WHHY8No"}, {"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "P-lecNa0rlG3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lcMsfaj21oCp": {"variants": [{"__ref": "aW09_WHHY8No"}, {"__ref": "uxKEiZcqedp9"}], "args": [], "attrs": {}, "rs": {"__ref": "nBZtEPo-fbDH"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "GKfnsm3IU4IN": {"variants": [{"__ref": "6rtOzVF7i-g4"}], "args": [], "attrs": {}, "rs": {"__ref": "QCThCajY34Im"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "m_q9uD2vdZAG": {"variants": [{"__ref": "6rtOzVF7i-g4"}, {"__ref": "uxKEiZcqedp9"}], "args": [], "attrs": {}, "rs": {"__ref": "0L8UIqnGiKem"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-4huavY9r9Vc": {"variants": [{"__ref": "6rtOzVF7i-g4"}, {"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "0x1wJWLpQr4-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "uITqzw_uujqU": {"variants": [{"__ref": "RrdYYLlUF4He"}], "args": [], "attrs": {}, "rs": {"__ref": "oR2kL-A_9L3X"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2b9DfpLcAAob": {"variants": [{"__ref": "RrdYYLlUF4He"}, {"__ref": "uxKEiZcqedp9"}], "args": [], "attrs": {}, "rs": {"__ref": "1KKxtSqVKSKP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "BS-Lck4PEnjp": {"variants": [{"__ref": "RrdYYLlUF4He"}, {"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "_vXMrEntt09Z"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "qNI9pvANRT6Q": {"variants": [{"__ref": "TxFEt7KZNNsK"}], "args": [], "attrs": {}, "rs": {"__ref": "OZtcKjaSVg1X"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "K_ev4MtAqTXQ": {"variants": [{"__ref": "TxFEt7KZNNsK"}, {"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "Meb5Y3_wixtW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "6xrqWV7lCJRG": {"variants": [{"__ref": "TxFEt7KZNNsK"}, {"__ref": "uxKEiZcqedp9"}], "args": [], "attrs": {}, "rs": {"__ref": "3Wz2EWNX5HxW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "GiulHNRVXOPx": {"variants": [{"__ref": "O4dK_zihRgNS"}], "args": [], "attrs": {}, "rs": {"__ref": "gRwBxQoqv6pL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "rUkVtNsb3EBL": {"variants": [{"__ref": "O4dK_zihRgNS"}, {"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "9jjaRA5D3San"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "TFslZnmWzPzd": {"variants": [{"__ref": "O4dK_zihRgNS"}, {"__ref": "uxKEiZcqedp9"}], "args": [], "attrs": {}, "rs": {"__ref": "UsukkpBUap5N"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Bz6TEJPDvscT": {"variants": [{"__ref": "d9k4kNmKBrnH"}], "args": [], "attrs": {}, "rs": {"__ref": "_R6WKv4cuKbg"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9m8PeAjGrrdD": {"variants": [{"__ref": "d9k4kNmKBrnH"}, {"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "UcoL2-9X6SNL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "AsZnBqtBMJOj": {"variants": [{"__ref": "d9k4kNmKBrnH"}, {"__ref": "uxKEiZcqedp9"}], "args": [], "attrs": {}, "rs": {"__ref": "KyaDF_rj8fWy"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "txnwKEHFRUV7": {"variants": [{"__ref": "pFLgJ0hUYaIP"}], "args": [], "attrs": {}, "rs": {"__ref": "-wbQOZr_60UC"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "61LnmROaM-PE": {"variants": [{"__ref": "pFLgJ0hUYaIP"}, {"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "t702_PWaEFrR"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zL6h96VilBUu": {"variants": [{"__ref": "pFLgJ0hUYaIP"}, {"__ref": "uxKEiZcqedp9"}], "args": [], "attrs": {}, "rs": {"__ref": "0lEf1Mo5pdQp"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "AwCBGIbfverJ": {"variants": [{"__ref": "pc8d1Faa0eKz"}], "args": [], "attrs": {}, "rs": {"__ref": "5BczgeicdSwv"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Cb_4BMr2Zuu9": {"variants": [{"__ref": "pc8d1Faa0eKz"}, {"__ref": "pF6EHk9Uh077"}], "args": [], "attrs": {}, "rs": {"__ref": "QVnc7ubV98FF"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "NRfb6dhasOQa": {"variants": [{"__ref": "pc8d1Faa0eKz"}, {"__ref": "pF6EHk9Uh077"}, {"__ref": "dOBSQ071peO9"}], "args": [], "attrs": {}, "rs": {"__ref": "Zdgb1GxpN8Ah"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "uGWNuIQSlMd_": {"variants": [{"__ref": "pc8d1Faa0eKz"}, {"__ref": "dOBSQ071peO9"}], "args": [], "attrs": {}, "rs": {"__ref": "cLonbf35sFYu"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "hRY4jxQNcxLc": {"variants": [{"__ref": "b7j7re9dG2TE"}], "args": [], "attrs": {}, "rs": {"__ref": "kBIhpAd30FFI"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lhG6qW5OaRDh": {"variants": [{"__ref": "b7j7re9dG2TE"}, {"__ref": "pF6EHk9Uh077"}], "args": [], "attrs": {}, "rs": {"__ref": "-Egni-DoD_3w"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yHqil5dJU95R": {"variants": [{"__ref": "dOBSQ071peO9"}, {"__ref": "b7j7re9dG2TE"}], "args": [], "attrs": {}, "rs": {"__ref": "XCWxE9MtlgHf"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "aJt_JgLmcCn4": {"variants": [{"__ref": "pc8d1Faa0eKz"}, {"__ref": "b7j7re9dG2TE"}], "args": [], "attrs": {}, "rs": {"__ref": "MwJIjIay0ZO0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "0_DZg8doSgWA": {"variants": [{"__ref": "IToJ04sZLo_P"}], "args": [], "attrs": {}, "rs": {"__ref": "6JUg9qs0fJm3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "DK9XpknykPKi": {"variants": [{"__ref": "IToJ04sZLo_P"}, {"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "dHZ6GB1BP7Ds"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "uzwOeWpqPH_f": {"variants": [{"__ref": "IToJ04sZLo_P"}, {"__ref": "uxKEiZcqedp9"}], "args": [], "attrs": {}, "rs": {"__ref": "vXNu1Z-xNeYm"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "rM1OJMc9UCpd": {"variants": [{"__ref": "8EgMkXce9H15"}], "args": [], "attrs": {}, "rs": {"__ref": "5UgYtsd95UtP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "XscLBhVjCjZm": {"variants": [{"__ref": "8EgMkXce9H15"}, {"__ref": "pc8d1Faa0eKz"}], "args": [], "attrs": {}, "rs": {"__ref": "IZdgY-HZ8C6y"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2rueDsf13AFB": {"variants": [{"__ref": "gs8DsWBtgg2A"}], "args": [], "attrs": {}, "rs": {"__ref": "KKFF1nYzowPt"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "aD8-wz0OhL5S": {"variants": [{"__ref": "gs8DsWBtgg2A"}, {"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "OCi1sa7y6_6d"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "y1HhOB_TIHbB": {"variants": [{"__ref": "gs8DsWBtgg2A"}, {"__ref": "uxKEiZcqedp9"}], "args": [], "attrs": {}, "rs": {"__ref": "gNfUYRHo-3Rq"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "HiyB6pUHoJ8-": {"variants": [{"__ref": "gs8DsWBtgg2A"}, {"__ref": "KelsXKS-Sk5C"}], "args": [], "attrs": {}, "rs": {"__ref": "J<PERSON><PERSON>"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "mLtQGRj_Jzpp": {"variants": [{"__ref": "KelsXKS-Sk5C"}], "args": [], "attrs": {}, "rs": {"__ref": "HsDXJMf3PA0W"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Q9MXqHp-XoZ9": {"variants": [{"__ref": "Lz9juLj562BM"}], "args": [], "attrs": {}, "rs": {"__ref": "J8iTbDuuQIW4"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yFyeJmbn_5dN": {"variants": [{"__ref": "Lz9juLj562BM"}, {"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "d_dgqyOXd0as"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "HYmzWEZ9fmNs": {"variants": [{"__ref": "Lz9juLj562BM"}, {"__ref": "uxKEiZcqedp9"}], "args": [], "attrs": {}, "rs": {"__ref": "chQ4zlTlhlsS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "c23--BoKmdDc": {"variants": [{"__ref": "_AU_l-6UE-d8"}], "args": [], "attrs": {}, "rs": {"__ref": "EsOCUb_SRugr"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "pF6EHk9Uh077": {"uuid": "wCIqpn5Nc7Mc", "name": "Show Start Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "zVHQZ_BbL9Dp"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "dOBSQ071peO9": {"uuid": "eVQrPszY8J3A", "name": "Show End Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "gb3mij5PVVAk"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "bi1zXsK_HcA4": {"uuid": "OCxjRqIUS6ap", "name": "Is Disabled", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "3k2Lm6KBHQjO"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "b7j7re9dG2TE": {"uuid": "nkHYqDLAtohr", "name": "Rounded", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "Nxmurwo5uasz"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "8EgMkXce9H15": {"uuid": "pna4-bSihHBJ", "name": "Round", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "Nxmurwo5uasz"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "_AU_l-6UE-d8": {"uuid": "Mt7jH7ujuYSM", "name": "<PERSON>", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "Nxmurwo5uasz"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "pc8d1Faa0eKz": {"uuid": "V2YLbj9ucpvr", "name": "Compact", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "oyZ8keBITnlE"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "KelsXKS-Sk5C": {"uuid": "npmF7rUbdUk7", "name": "Minimal", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "oyZ8keBITnlE"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "O4dK_zihRgNS": {"uuid": "icgpsi-g6FfN", "name": "Blue", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "as0v2YszzpAL"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "d9k4kNmKBrnH": {"uuid": "fwOGKoOEnglD", "name": "Green", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "as0v2YszzpAL"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "uSRzBesvIzds": {"uuid": "PDVRagFhnfK_", "name": "Yellow", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "as0v2YszzpAL"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "aW09_WHHY8No": {"uuid": "xQ_Rqd-o0U03", "name": "Red", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "as0v2YszzpAL"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "pFLgJ0hUYaIP": {"uuid": "T1B9LYwYXKXC", "name": "Sand", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "as0v2YszzpAL"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Lz9juLj562BM": {"uuid": "zTDrBqMNILNR", "name": "White", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "as0v2YszzpAL"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "W90MB8cWV6oZ": {"uuid": "ZQPnR-g11syE", "name": "Soft Blue", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "as0v2YszzpAL"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "R2Yy5npBRtiC": {"uuid": "F1fNRfCVNUbJ", "name": "Soft Green", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "as0v2YszzpAL"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "6rtOzVF7i-g4": {"uuid": "pTw4tlDg6mOg", "name": "Soft Yellow", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "as0v2YszzpAL"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "RrdYYLlUF4He": {"uuid": "rJVfHWnlXAGX", "name": "Soft Red", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "as0v2YszzpAL"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "TxFEt7KZNNsK": {"uuid": "dFq0FKogQSRo", "name": "Soft Sand", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "as0v2YszzpAL"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "IToJ04sZLo_P": {"uuid": "0Lk9bgqXXAKZ", "name": "Clear", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "as0v2YszzpAL"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "gs8DsWBtgg2A": {"uuid": "TuBAaK3zEWyv", "name": "Link", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "as0v2YszzpAL"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "kzxKGk-rQ0K2": {"cols": [{"__ref": "mhzpyCqbxbQ_"}, {"__ref": "9IDn4h9M_R1a"}, {"__ref": "X6cSMxtvwRmC"}, {"__ref": "HgTwXAkEvGtn"}, {"__ref": "RbKsr449Egn7"}], "rowKey": null, "__type": "ArenaFrameRow"}, "Zd__-2sapBjN": {"cols": [{"__ref": "NS7q6o3OR6_r"}], "rowKey": {"__ref": "zVHQZ_BbL9Dp"}, "__type": "ArenaFrameRow"}, "10uPNoI31oj2": {"cols": [{"__ref": "SvQEKEL9XolR"}], "rowKey": {"__ref": "gb3mij5PVVAk"}, "__type": "ArenaFrameRow"}, "iHhgxpk8MFIQ": {"cols": [{"__ref": "5kxFX5G2E7rt"}], "rowKey": {"__ref": "3k2Lm6KBHQjO"}, "__type": "ArenaFrameRow"}, "BRNEhQ21OJ6H": {"cols": [{"__ref": "OGfR86-J9ACf"}, {"__ref": "EG69xJFOvVi1"}, {"__ref": "J2ze2Ww9j5wp"}], "rowKey": {"__ref": "Nxmurwo5uasz"}, "__type": "ArenaFrameRow"}, "lLZo2FwGALHv": {"cols": [{"__ref": "vTV-xZ1hnvQg"}, {"__ref": "G5qRig5P2k6w"}], "rowKey": {"__ref": "oyZ8keBITnlE"}, "__type": "ArenaFrameRow"}, "hwZgPal3NbFz": {"cols": [{"__ref": "zT-ZNqR41bD8"}, {"__ref": "xCMhN_b0eLy8"}, {"__ref": "MWhf5XkfOERL"}, {"__ref": "YyrdIaeiZ7l8"}, {"__ref": "h2KXMPW5UXhz"}, {"__ref": "Yo2zoK9shJMn"}, {"__ref": "NZrRLG0lPUhh"}, {"__ref": "-C5pd-NkK9fL"}, {"__ref": "bmX6ZU3d2xeK"}, {"__ref": "moPqPySY-h6x"}, {"__ref": "cLo0AvI9SBBm"}, {"__ref": "Z5IG5cHKxHzE"}, {"__ref": "plcUL-XhCPJ0"}], "rowKey": {"__ref": "as0v2YszzpAL"}, "__type": "ArenaFrameRow"}, "zdIm-9vntXoH": {"cols": [{"__ref": "XxOL9yzgG4tf"}, {"__ref": "gCjJFHqnBVXT"}, {"__ref": "fHPM8tOUzlQV"}, {"__ref": "YmgDdVOHPiOQ"}, {"__ref": "CMu63Hfw618x"}, {"__ref": "OrEz6c8Zi-b4"}, {"__ref": "YcjQQaMwvaRD"}, {"__ref": "ZpYAcwtbdguR"}, {"__ref": "D4rQLEWb5E2U"}, {"__ref": "tdoGJ-x0_9GB"}, {"__ref": "cjJb2LxJ9EjK"}, {"__ref": "fUvzkSExrSwr"}, {"__ref": "8Z9SFkABYrqD"}, {"__ref": "Cp4FvD_pzV6a"}, {"__ref": "D3LWsi7DwbVx"}, {"__ref": "foYuVTiRVbq2"}, {"__ref": "LRWaNIpA0zLg"}, {"__ref": "uNNl4bmrGSp8"}, {"__ref": "ywLFePD_4I3G"}, {"__ref": "O3-7ZjJrWfyP"}, {"__ref": "yD_CrQTDecDf"}, {"__ref": "-RSKrfQVIvJ6"}, {"__ref": "NP4KZzmsha18"}, {"__ref": "i4yl6K2qhGvt"}, {"__ref": "jo0twr2D90Cq"}, {"__ref": "Ao_6rvhk8zyV"}, {"__ref": "H50XboaPX3G9"}, {"__ref": "0d84OiD9jQcc"}, {"__ref": "3E9ngeh-Nr-g"}], "rowKey": null, "__type": "ArenaFrameRow"}, "pG8utUOtcrSn": {"param": {"__ref": "8imcyzt3b8AA"}, "expr": {"__ref": "K-kKb4YerUfw"}, "__type": "Arg"}, "t6MRzUkDp1Uh": {"param": {"__ref": "wLlq7c86cpEZ"}, "expr": {"__ref": "OG-r7OsjkAzV"}, "__type": "Arg"}, "UgdNdTHJngeh": {"param": {"__ref": "PI0qZTj0EyKV"}, "expr": {"__ref": "bo47CF2WeQNf"}, "__type": "Arg"}, "QmzXUucCWmAN": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "KDWrUtGbU5cL": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "Wv_pLMSZj3Av"}, "__type": "ArgType"}, "2fIaGYQhBIVZ": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "S08VWh5zSnuX"}, "__type": "ArgType"}, "sU_LVT7WLMvk": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "BSdzBiRP94KR"}, "__type": "ArgType"}, "O11kQNmph6ki": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "ghPLy6rHxeHF"}, "__type": "ArgType"}, "F0pU_Rir6zSj": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "17IRWphc-aRM"}, "__type": "ArgType"}, "IzGzi9qq5Uiv": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "shSKezK9LGhH"}, "__type": "ArgType"}, "NVB0ONqhxBF3": {"param": {"__ref": "8imcyzt3b8AA"}, "defaultContents": [{"__ref": "e7FgHKYkKuLq"}], "uuid": "v-43U_mFVDPd", "parent": {"__ref": "zjR2GOyoPiIp"}, "locked": null, "vsettings": [{"__ref": "YfC7ICVlrBW0"}, {"__ref": "233A1La-aTVY"}, {"__ref": "4mw_WzmEKuDx"}, {"__ref": "UT1suafUDjEF"}, {"__ref": "-yKWXk94q25m"}, {"__ref": "2AtNOgx3cLZw"}, {"__ref": "o0KXxa1R6Hri"}, {"__ref": "Kxjzb78nR-7a"}, {"__ref": "3-6DfBuZcen3"}, {"__ref": "p8Mf8pU-edSt"}, {"__ref": "CV4tNT3iEtpn"}, {"__ref": "vBa4zn0F7duN"}, {"__ref": "VwmRHGA4R9db"}, {"__ref": "cWX9-Nnxg0Do"}], "__type": "TplSlot"}, "LHlOcs1ZGQgA": {"variants": [{"__ref": "tV1959nPHhqn"}], "args": [], "attrs": {}, "rs": {"__ref": "EXQHrUZfz476"}, "dataCond": {"__ref": "0vM5wm-edk7_"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "cHjJXjUpBJ0A": {"variants": [{"__ref": "pF6EHk9Uh077"}], "args": [], "attrs": {}, "rs": {"__ref": "vEC7kH6alKBY"}, "dataCond": {"__ref": "gmkN5yqiHUFk"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Mg_-fxExWOLM": {"variants": [{"__ref": "O4dK_zihRgNS"}], "args": [], "attrs": {}, "rs": {"__ref": "LEg4sDIaJ6k-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PBN9f7YRw8sL": {"variants": [{"__ref": "b7j7re9dG2TE"}, {"__ref": "pF6EHk9Uh077"}], "args": [], "attrs": {}, "rs": {"__ref": "XVO1Bc8Upiqo"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "UefTUd8b38Bc": {"param": {"__ref": "wLlq7c86cpEZ"}, "defaultContents": [{"__ref": "I5rLVWQxApNV"}], "uuid": "B7EqJ6B9mNJ6", "parent": {"__ref": "LL2FQeTNlfCw"}, "locked": null, "vsettings": [{"__ref": "x-lWH2QkhYdZ"}, {"__ref": "4YcInLMy0k1E"}, {"__ref": "9R7-ZabA8Yfa"}, {"__ref": "cvLCfvE089cK"}, {"__ref": "CnrE-n9U2xWW"}, {"__ref": "N1lAV2q0YREA"}, {"__ref": "jpqMd4wVrF2q"}, {"__ref": "MAp6md6o4ozg"}, {"__ref": "UBU3Y-8yjFqK"}, {"__ref": "1Y8EgM6WuLxU"}, {"__ref": "tOZpvLruxHwO"}, {"__ref": "XqKyUfeouu-k"}, {"__ref": "sxU_R7uzV2-c"}, {"__ref": "oRwtnPbV5tnj"}, {"__ref": "RSNhWFxchEUT"}, {"__ref": "IUo7o3gcHJl4"}, {"__ref": "TLeo5ZePWjtV"}, {"__ref": "we8Wr2VBcDE0"}, {"__ref": "bmDOfyKqMd0z"}, {"__ref": "X7zXjtyl0ckb"}, {"__ref": "vsvdiDb9yRpL"}, {"__ref": "EJzj0T43yHDc"}, {"__ref": "prEM6wJDZEIF"}, {"__ref": "91WTPjvuXg0k"}, {"__ref": "siSDDRkMv-Ve"}, {"__ref": "L8fAHy5NcNiM"}], "__type": "TplSlot"}, "ckasbCmP-1XJ": {"variants": [{"__ref": "tV1959nPHhqn"}], "args": [], "attrs": {}, "rs": {"__ref": "eA3b8TBpwwrq"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "hBZex34tElf8": {"variants": [{"__ref": "bi1zXsK_HcA4"}], "args": [], "attrs": {}, "rs": {"__ref": "enQxTmnbP24D"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "W-mPLqdhVaGK": {"variants": [{"__ref": "dOBSQ071peO9"}], "args": [], "attrs": {}, "rs": {"__ref": "F9X7Vgg4imEo"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "cPbxac1eGWJf": {"variants": [{"__ref": "unYEfNHe7XRn"}], "args": [], "attrs": {}, "rs": {"__ref": "JfLztoG3MMuD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "IedXICp9HK8B": {"variants": [{"__ref": "b7j7re9dG2TE"}], "args": [], "attrs": {}, "rs": {"__ref": "0r6MLy6v14ke"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "opcPkVDFUkfp": {"param": {"__ref": "PI0qZTj0EyKV"}, "defaultContents": [{"__ref": "sQpb0nY2QU09"}], "uuid": "yQZRDp1xg8V0", "parent": {"__ref": "Oflqu7F5tu41"}, "locked": null, "vsettings": [{"__ref": "IiU467mzwaJa"}, {"__ref": "FMDa_pMxbFbW"}, {"__ref": "EWn6NnFWBiGS"}, {"__ref": "PWFlO4_mGHpJ"}, {"__ref": "FciheY_IZ3O2"}, {"__ref": "K_PAuSAuNmvo"}, {"__ref": "yEehm61leTxs"}, {"__ref": "PRFjGWcValQa"}, {"__ref": "5dNpzjLWowNf"}, {"__ref": "Hn-lpd5LThDd"}, {"__ref": "V-nCFbWCD85U"}, {"__ref": "NW2SqpYtNC5p"}, {"__ref": "GghQNsoNQVlE"}], "__type": "TplSlot"}, "PxTIhs8dbvQP": {"variants": [{"__ref": "tV1959nPHhqn"}], "args": [], "attrs": {}, "rs": {"__ref": "2_sD1kMJz26b"}, "dataCond": {"__ref": "fm1mJWloJvR2"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "StHqnGZNXAfT": {"variants": [{"__ref": "dOBSQ071peO9"}], "args": [], "attrs": {}, "rs": {"__ref": "R9TL8T_a86bD"}, "dataCond": {"__ref": "EUW-mjWiZO12"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PA9P2SQUaCZ0": {"variants": [{"__ref": "uSRzBesvIzds"}], "args": [], "attrs": {}, "rs": {"__ref": "9iz-cJeAnJKs"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-YLnweVrf2IN": {"variants": [{"__ref": "Lz9juLj562BM"}], "args": [], "attrs": {}, "rs": {"__ref": "V7PkGjOuJZff"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "oVlX18PPcH8K": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "center", "justify-content": "center", "padding-top": "12px", "padding-right": "20px", "padding-bottom": "12px", "padding-left": "20px", "flex-column-gap": "8px", "background": "linear-gradient(#232320, #232320)", "border-top-width": "0px", "border-right-width": "0px", "border-bottom-width": "0px", "border-left-width": "0px", "cursor": "pointer", "transition-property": "background", "transition-duration": "0.1s", "border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px"}, "mixins": [], "__type": "RuleSet"}, "XRZA7fSApIdi": {"values": {"box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "hEtRdtzYkbWw": {"values": {"box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "zuSzwhhYdfrc": {"values": {"cursor": "not-allowed", "opacity": "0.6"}, "mixins": [], "__type": "RuleSet"}, "zsVOB2pIyt-t": {"values": {"padding-right": "16px"}, "mixins": [], "__type": "RuleSet"}, "o39u2fmBKd61": {"values": {"padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "HMnKQudyeVgf": {"values": {"background": "linear-gradient(#282826, #282826)"}, "mixins": [], "__type": "RuleSet"}, "jiyCIvz9SrIC": {"values": {"background": "linear-gradient(#2E2E2B, #2E2E2B)"}, "mixins": [], "__type": "RuleSet"}, "4d85HdIwuyy6": {"values": {"background": "linear-gradient(#EDF6FF, #EDF6FF)"}, "mixins": [], "__type": "RuleSet"}, "pi8SX4LTGKih": {"values": {"background": "linear-gradient(#E1F0FF, #E1F0FF)"}, "mixins": [], "__type": "RuleSet"}, "JKeUQuSx0amx": {"values": {"background": "linear-gradient(#CEE7FE, #CEE7FE)"}, "mixins": [], "__type": "RuleSet"}, "mIO_WZ4CgW8x": {"values": {"background": "linear-gradient(#E9F9EE, #E9F9EE)"}, "mixins": [], "__type": "RuleSet"}, "cBGAwu8BEK6P": {"values": {"background": "linear-gradient(#CCEBD7, #CCEBD7)"}, "mixins": [], "__type": "RuleSet"}, "aJ0eqM06oYhw": {"values": {"background": "linear-gradient(#DDF3E4, #DDF3E4)"}, "mixins": [], "__type": "RuleSet"}, "74kTZsIn79AD": {"values": {"background": "linear-gradient(#F5D90A, #F5D90A)"}, "mixins": [], "__type": "RuleSet"}, "ijYYHf54whCx": {"values": {"background": "linear-gradient(#FFEF5C, #FFEF5C)"}, "mixins": [], "__type": "RuleSet"}, "Gtg7HNHSUi4e": {"values": {"background": "linear-gradient(#F0C000, #F0C000)"}, "mixins": [], "__type": "RuleSet"}, "bjqfHAEbFHTJ": {"values": {"background": "linear-gradient(#E54D2E, #E54D2E)"}, "mixins": [], "__type": "RuleSet"}, "P-lecNa0rlG3": {"values": {"background": "linear-gradient(#EC5E41, #EC5E41)"}, "mixins": [], "__type": "RuleSet"}, "nBZtEPo-fbDH": {"values": {"background": "linear-gradient(#F16A50, #F16A50)"}, "mixins": [], "__type": "RuleSet"}, "QCThCajY34Im": {"values": {"background": "linear-gradient(#FFFBD1, #FFFBD1)"}, "mixins": [], "__type": "RuleSet"}, "0L8UIqnGiKem": {"values": {"background": "linear-gradient(#FEF2A4, #FEF2A4)"}, "mixins": [], "__type": "RuleSet"}, "0x1wJWLpQr4-": {"values": {"background": "linear-gradient(#FFF8BB, #FFF8BB)"}, "mixins": [], "__type": "RuleSet"}, "oR2kL-A_9L3X": {"values": {"background": "linear-gradient(#FFF0EE, #FFF0EE)"}, "mixins": [], "__type": "RuleSet"}, "1KKxtSqVKSKP": {"values": {"background": "linear-gradient(#FDD8D3, #FDD8D3)"}, "mixins": [], "__type": "RuleSet"}, "_vXMrEntt09Z": {"values": {"background": "linear-gradient(#FFE6E2, #FFE6E2)"}, "mixins": [], "__type": "RuleSet"}, "OZtcKjaSVg1X": {"values": {"background": "linear-gradient(#EEEEEC, #EEEEEC)"}, "mixins": [], "__type": "RuleSet"}, "Meb5Y3_wixtW": {"values": {"background": "linear-gradient(#E9E9E6, #E9E9E6)"}, "mixins": [], "__type": "RuleSet"}, "3Wz2EWNX5HxW": {"values": {"background": "linear-gradient(#E3E3E0, #E3E3E0)"}, "mixins": [], "__type": "RuleSet"}, "gRwBxQoqv6pL": {"values": {"background": "linear-gradient(#0091FF, #0091FF)"}, "mixins": [], "__type": "RuleSet"}, "9jjaRA5D3San": {"values": {"background": "linear-gradient(#369EFF, #369EFF)"}, "mixins": [], "__type": "RuleSet"}, "UsukkpBUap5N": {"values": {"background": "linear-gradient(#52A9FF, #52A9FF)"}, "mixins": [], "__type": "RuleSet"}, "_R6WKv4cuKbg": {"values": {"background": "linear-gradient(#30A46C, #30A46C)"}, "mixins": [], "__type": "RuleSet"}, "UcoL2-9X6SNL": {"values": {"background": "linear-gradient(#3CB179, #3CB179)"}, "mixins": [], "__type": "RuleSet"}, "KyaDF_rj8fWy": {"values": {"background": "linear-gradient(#4CC38A, #4CC38A)"}, "mixins": [], "__type": "RuleSet"}, "-wbQOZr_60UC": {"values": {"background": "linear-gradient(#717069, #717069)"}, "mixins": [], "__type": "RuleSet"}, "t702_PWaEFrR": {"values": {"background": "linear-gradient(#7F7E77, #7F7E77)"}, "mixins": [], "__type": "RuleSet"}, "0lEf1Mo5pdQp": {"values": {"background": "linear-gradient(#A1A09A, #A1A09A)"}, "mixins": [], "__type": "RuleSet"}, "5BczgeicdSwv": {"values": {"padding-top": "6px", "padding-right": "16px", "padding-bottom": "6px", "padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "QVnc7ubV98FF": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Zdgb1GxpN8Ah": {"values": {}, "mixins": [], "__type": "RuleSet"}, "cLonbf35sFYu": {"values": {}, "mixins": [], "__type": "RuleSet"}, "kBIhpAd30FFI": {"values": {"border-top-left-radius": "999px", "border-top-right-radius": "999px", "border-bottom-right-radius": "999px", "border-bottom-left-radius": "999px", "padding-left": "20px", "padding-right": "20px", "min-width": "100px"}, "mixins": [], "__type": "RuleSet"}, "-Egni-DoD_3w": {"values": {"padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "XCWxE9MtlgHf": {"values": {"padding-right": "16px"}, "mixins": [], "__type": "RuleSet"}, "MwJIjIay0ZO0": {"values": {}, "mixins": [], "__type": "RuleSet"}, "6JUg9qs0fJm3": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "dHZ6GB1BP7Ds": {"values": {"background": "linear-gradient(#E9E9E6, #E9E9E6)"}, "mixins": [], "__type": "RuleSet"}, "vXNu1Z-xNeYm": {"values": {"background": "linear-gradient(#E3E3E0, #E3E3E0)"}, "mixins": [], "__type": "RuleSet"}, "5UgYtsd95UtP": {"values": {"padding-top": "12px", "padding-right": "12px", "padding-bottom": "12px", "padding-left": "12px", "border-top-left-radius": "50%", "border-top-right-radius": "50%", "border-bottom-right-radius": "50%", "border-bottom-left-radius": "50%"}, "mixins": [], "__type": "RuleSet"}, "IZdgY-HZ8C6y": {"values": {"padding-top": "6px", "padding-right": "6px", "padding-bottom": "6px", "padding-left": "6px"}, "mixins": [], "__type": "RuleSet"}, "KKFF1nYzowPt": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "OCi1sa7y6_6d": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "gNfUYRHo-3Rq": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "JOrleENinZrf": {"values": {}, "mixins": [], "__type": "RuleSet"}, "HsDXJMf3PA0W": {"values": {"padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px"}, "mixins": [], "__type": "RuleSet"}, "J8iTbDuuQIW4": {"values": {"background": "linear-gradient(#FFFFFF, #FFFFFF)"}, "mixins": [], "__type": "RuleSet"}, "d_dgqyOXd0as": {"values": {"background": "linear-gradient(#FFEF5C, #FFEF5C)"}, "mixins": [], "__type": "RuleSet"}, "chQ4zlTlhlsS": {"values": {"background": "linear-gradient(#F0C000, #F0C000)"}, "mixins": [], "__type": "RuleSet"}, "EsOCUb_SRugr": {"values": {"border-top-left-radius": "0px", "border-top-right-radius": "0px", "border-bottom-right-radius": "0px", "border-bottom-left-radius": "0px"}, "mixins": [], "__type": "RuleSet"}, "mhzpyCqbxbQ_": {"frame": {"__ref": "AWK2ieJfcWLM"}, "cellKey": {"__ref": "tV1959nPHhqn"}, "__type": "ArenaFrameCell"}, "9IDn4h9M_R1a": {"frame": {"__ref": "XCDwMtNGjZI9"}, "cellKey": {"__ref": "unYEfNHe7XRn"}, "__type": "ArenaFrameCell"}, "X6cSMxtvwRmC": {"frame": {"__ref": "N6rOPe7dBf1L"}, "cellKey": {"__ref": "98bCPPxdZwa4"}, "__type": "ArenaFrameCell"}, "HgTwXAkEvGtn": {"frame": {"__ref": "pyao8NTEBIDL"}, "cellKey": {"__ref": "NlNWf7-MImkI"}, "__type": "ArenaFrameCell"}, "RbKsr449Egn7": {"frame": {"__ref": "SEho5bqTSFhi"}, "cellKey": {"__ref": "uxKEiZcqedp9"}, "__type": "ArenaFrameCell"}, "NS7q6o3OR6_r": {"frame": {"__ref": "4ovzAEPomziz"}, "cellKey": {"__ref": "pF6EHk9Uh077"}, "__type": "ArenaFrameCell"}, "SvQEKEL9XolR": {"frame": {"__ref": "a3g08qQExUMa"}, "cellKey": {"__ref": "dOBSQ071peO9"}, "__type": "ArenaFrameCell"}, "5kxFX5G2E7rt": {"frame": {"__ref": "zLvC_gCXpCvx"}, "cellKey": {"__ref": "bi1zXsK_HcA4"}, "__type": "ArenaFrameCell"}, "OGfR86-J9ACf": {"frame": {"__ref": "Gldfh4pTyoZ4"}, "cellKey": {"__ref": "b7j7re9dG2TE"}, "__type": "ArenaFrameCell"}, "EG69xJFOvVi1": {"frame": {"__ref": "MaQaSWDtMqkV"}, "cellKey": {"__ref": "8EgMkXce9H15"}, "__type": "ArenaFrameCell"}, "J2ze2Ww9j5wp": {"frame": {"__ref": "g0LvLVlAoMCO"}, "cellKey": {"__ref": "_AU_l-6UE-d8"}, "__type": "ArenaFrameCell"}, "vTV-xZ1hnvQg": {"frame": {"__ref": "t853-x4jVtYX"}, "cellKey": {"__ref": "pc8d1Faa0eKz"}, "__type": "ArenaFrameCell"}, "G5qRig5P2k6w": {"frame": {"__ref": "M7807I3sEKi-"}, "cellKey": {"__ref": "KelsXKS-Sk5C"}, "__type": "ArenaFrameCell"}, "zT-ZNqR41bD8": {"frame": {"__ref": "NfH3r6szmdOg"}, "cellKey": {"__ref": "O4dK_zihRgNS"}, "__type": "ArenaFrameCell"}, "xCMhN_b0eLy8": {"frame": {"__ref": "dxr7d5xYCTD5"}, "cellKey": {"__ref": "d9k4kNmKBrnH"}, "__type": "ArenaFrameCell"}, "MWhf5XkfOERL": {"frame": {"__ref": "nRHIe7CyRe0Y"}, "cellKey": {"__ref": "uSRzBesvIzds"}, "__type": "ArenaFrameCell"}, "YyrdIaeiZ7l8": {"frame": {"__ref": "_xtq406VOQVz"}, "cellKey": {"__ref": "aW09_WHHY8No"}, "__type": "ArenaFrameCell"}, "h2KXMPW5UXhz": {"frame": {"__ref": "C9rCe65LlQ2O"}, "cellKey": {"__ref": "pFLgJ0hUYaIP"}, "__type": "ArenaFrameCell"}, "Yo2zoK9shJMn": {"frame": {"__ref": "4ePI-VSkm7ZE"}, "cellKey": {"__ref": "Lz9juLj562BM"}, "__type": "ArenaFrameCell"}, "NZrRLG0lPUhh": {"frame": {"__ref": "iPc9e_dGsImF"}, "cellKey": {"__ref": "W90MB8cWV6oZ"}, "__type": "ArenaFrameCell"}, "-C5pd-NkK9fL": {"frame": {"__ref": "6myngZEr_kMG"}, "cellKey": {"__ref": "R2Yy5npBRtiC"}, "__type": "ArenaFrameCell"}, "bmX6ZU3d2xeK": {"frame": {"__ref": "v5OtOYpVGFlk"}, "cellKey": {"__ref": "6rtOzVF7i-g4"}, "__type": "ArenaFrameCell"}, "moPqPySY-h6x": {"frame": {"__ref": "9wS1Vd-izIBA"}, "cellKey": {"__ref": "RrdYYLlUF4He"}, "__type": "ArenaFrameCell"}, "cLo0AvI9SBBm": {"frame": {"__ref": "JlU5b9DBQ4Ru"}, "cellKey": {"__ref": "TxFEt7KZNNsK"}, "__type": "ArenaFrameCell"}, "Z5IG5cHKxHzE": {"frame": {"__ref": "ex6YOGxuAQCL"}, "cellKey": {"__ref": "IToJ04sZLo_P"}, "__type": "ArenaFrameCell"}, "plcUL-XhCPJ0": {"frame": {"__ref": "5_Dc0WdjDKLF"}, "cellKey": {"__ref": "gs8DsWBtgg2A"}, "__type": "ArenaFrameCell"}, "XxOL9yzgG4tf": {"frame": {"__ref": "6ABVczUo4GNX"}, "cellKey": [{"__ref": "W90MB8cWV6oZ"}, {"__ref": "NlNWf7-MImkI"}], "__type": "ArenaFrameCell"}, "gCjJFHqnBVXT": {"frame": {"__ref": "eTZ_ugmScjag"}, "cellKey": [{"__ref": "W90MB8cWV6oZ"}, {"__ref": "uxKEiZcqedp9"}], "__type": "ArenaFrameCell"}, "fHPM8tOUzlQV": {"frame": {"__ref": "U5zbdr_VqDdk"}, "cellKey": [{"__ref": "R2Yy5npBRtiC"}, {"__ref": "uxKEiZcqedp9"}], "__type": "ArenaFrameCell"}, "YmgDdVOHPiOQ": {"frame": {"__ref": "DAolsztNl540"}, "cellKey": [{"__ref": "R2Yy5npBRtiC"}, {"__ref": "NlNWf7-MImkI"}], "__type": "ArenaFrameCell"}, "CMu63Hfw618x": {"frame": {"__ref": "NUjf97nzWJkH"}, "cellKey": [{"__ref": "uSRzBesvIzds"}, {"__ref": "NlNWf7-MImkI"}], "__type": "ArenaFrameCell"}, "OrEz6c8Zi-b4": {"frame": {"__ref": "WUI3K-YsGzsL"}, "cellKey": [{"__ref": "uSRzBesvIzds"}, {"__ref": "uxKEiZcqedp9"}], "__type": "ArenaFrameCell"}, "YcjQQaMwvaRD": {"frame": {"__ref": "oB4h1W12VaRw"}, "cellKey": [{"__ref": "aW09_WHHY8No"}, {"__ref": "NlNWf7-MImkI"}], "__type": "ArenaFrameCell"}, "ZpYAcwtbdguR": {"frame": {"__ref": "dmURfBU7K4Yz"}, "cellKey": [{"__ref": "aW09_WHHY8No"}, {"__ref": "uxKEiZcqedp9"}], "__type": "ArenaFrameCell"}, "D4rQLEWb5E2U": {"frame": {"__ref": "T4Sm13FovSI4"}, "cellKey": [{"__ref": "6rtOzVF7i-g4"}, {"__ref": "uxKEiZcqedp9"}], "__type": "ArenaFrameCell"}, "tdoGJ-x0_9GB": {"frame": {"__ref": "myHfzZ82gmLW"}, "cellKey": [{"__ref": "6rtOzVF7i-g4"}, {"__ref": "NlNWf7-MImkI"}], "__type": "ArenaFrameCell"}, "cjJb2LxJ9EjK": {"frame": {"__ref": "jFJ2Pm3fkrSB"}, "cellKey": [{"__ref": "RrdYYLlUF4He"}, {"__ref": "uxKEiZcqedp9"}], "__type": "ArenaFrameCell"}, "fUvzkSExrSwr": {"frame": {"__ref": "YmZ3bSo_y9mx"}, "cellKey": [{"__ref": "RrdYYLlUF4He"}, {"__ref": "NlNWf7-MImkI"}], "__type": "ArenaFrameCell"}, "8Z9SFkABYrqD": {"frame": {"__ref": "XjnTkFfiTd6k"}, "cellKey": [{"__ref": "TxFEt7KZNNsK"}, {"__ref": "NlNWf7-MImkI"}], "__type": "ArenaFrameCell"}, "Cp4FvD_pzV6a": {"frame": {"__ref": "cTFVMTDq3pmz"}, "cellKey": [{"__ref": "TxFEt7KZNNsK"}, {"__ref": "uxKEiZcqedp9"}], "__type": "ArenaFrameCell"}, "D3LWsi7DwbVx": {"frame": {"__ref": "O5JwMVWhE5d7"}, "cellKey": [{"__ref": "O4dK_zihRgNS"}, {"__ref": "NlNWf7-MImkI"}], "__type": "ArenaFrameCell"}, "foYuVTiRVbq2": {"frame": {"__ref": "V8j1dFMDQUHV"}, "cellKey": [{"__ref": "O4dK_zihRgNS"}, {"__ref": "uxKEiZcqedp9"}], "__type": "ArenaFrameCell"}, "LRWaNIpA0zLg": {"frame": {"__ref": "AHjyFjAjiUqS"}, "cellKey": [{"__ref": "d9k4kNmKBrnH"}, {"__ref": "NlNWf7-MImkI"}], "__type": "ArenaFrameCell"}, "uNNl4bmrGSp8": {"frame": {"__ref": "v_bqEZG8fwjo"}, "cellKey": [{"__ref": "d9k4kNmKBrnH"}, {"__ref": "uxKEiZcqedp9"}], "__type": "ArenaFrameCell"}, "ywLFePD_4I3G": {"frame": {"__ref": "JLX1oxtB27zc"}, "cellKey": [{"__ref": "pFLgJ0hUYaIP"}, {"__ref": "NlNWf7-MImkI"}], "__type": "ArenaFrameCell"}, "O3-7ZjJrWfyP": {"frame": {"__ref": "0b3ECaCkqSYG"}, "cellKey": [{"__ref": "pFLgJ0hUYaIP"}, {"__ref": "uxKEiZcqedp9"}], "__type": "ArenaFrameCell"}, "yD_CrQTDecDf": {"frame": {"__ref": "0683td4Trep2"}, "cellKey": [{"__ref": "b7j7re9dG2TE"}, {"__ref": "pF6EHk9Uh077"}], "__type": "ArenaFrameCell"}, "-RSKrfQVIvJ6": {"frame": {"__ref": "Hqed1ExWi2Vi"}, "cellKey": [{"__ref": "dOBSQ071peO9"}, {"__ref": "b7j7re9dG2TE"}], "__type": "ArenaFrameCell"}, "NP4KZzmsha18": {"frame": {"__ref": "FsvzeLzz6xOH"}, "cellKey": [{"__ref": "IToJ04sZLo_P"}, {"__ref": "NlNWf7-MImkI"}], "__type": "ArenaFrameCell"}, "i4yl6K2qhGvt": {"frame": {"__ref": "VTsUohH2v0oQ"}, "cellKey": [{"__ref": "IToJ04sZLo_P"}, {"__ref": "uxKEiZcqedp9"}], "__type": "ArenaFrameCell"}, "jo0twr2D90Cq": {"frame": {"__ref": "6KAXRqhK_o9X"}, "cellKey": [{"__ref": "8EgMkXce9H15"}, {"__ref": "pc8d1Faa0eKz"}], "__type": "ArenaFrameCell"}, "Ao_6rvhk8zyV": {"frame": {"__ref": "UqKCLlgyRZ0u"}, "cellKey": [{"__ref": "gs8DsWBtgg2A"}, {"__ref": "NlNWf7-MImkI"}], "__type": "ArenaFrameCell"}, "H50XboaPX3G9": {"frame": {"__ref": "gxv7ri1N3NSJ"}, "cellKey": [{"__ref": "gs8DsWBtgg2A"}, {"__ref": "uxKEiZcqedp9"}], "__type": "ArenaFrameCell"}, "0d84OiD9jQcc": {"frame": {"__ref": "Rb61H090BAkL"}, "cellKey": [{"__ref": "Lz9juLj562BM"}, {"__ref": "NlNWf7-MImkI"}], "__type": "ArenaFrameCell"}, "3E9ngeh-Nr-g": {"frame": {"__ref": "5zz42VzzgW4G"}, "cellKey": [{"__ref": "Lz9juLj562BM"}, {"__ref": "uxKEiZcqedp9"}], "__type": "ArenaFrameCell"}, "Wv_pLMSZj3Av": {"name": "any", "__type": "AnyType"}, "S08VWh5zSnuX": {"name": "any", "__type": "AnyType"}, "BSdzBiRP94KR": {"name": "any", "__type": "AnyType"}, "ghPLy6rHxeHF": {"name": "any", "__type": "AnyType"}, "17IRWphc-aRM": {"name": "any", "__type": "AnyType"}, "shSKezK9LGhH": {"name": "any", "__type": "AnyType"}, "e7FgHKYkKuLq": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "BPkGRaSuOp3G", "parent": {"__ref": "NVB0ONqhxBF3"}, "locked": null, "vsettings": [{"__ref": "BkKQ3JPgIyn1"}], "__type": "TplTag"}, "YfC7ICVlrBW0": {"variants": [{"__ref": "tV1959nPHhqn"}], "args": [], "attrs": {}, "rs": {"__ref": "B0WJs6gYwONn"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "233A1La-aTVY": {"variants": [{"__ref": "pF6EHk9Uh077"}], "args": [], "attrs": {}, "rs": {"__ref": "gsopkaZ5tYHD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4mw_WzmEKuDx": {"variants": [{"__ref": "O4dK_zihRgNS"}], "args": [], "attrs": {}, "rs": {"__ref": "DRg7Mx0y0eVO"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "UT1suafUDjEF": {"variants": [{"__ref": "W90MB8cWV6oZ"}], "args": [], "attrs": {}, "rs": {"__ref": "PFhPj0317owB"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-yKWXk94q25m": {"variants": [{"__ref": "R2Yy5npBRtiC"}], "args": [], "attrs": {}, "rs": {"__ref": "q0c8kvtsdL9r"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2AtNOgx3cLZw": {"variants": [{"__ref": "6rtOzVF7i-g4"}], "args": [], "attrs": {}, "rs": {"__ref": "FHqFcp81vUhq"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "o0KXxa1R6Hri": {"variants": [{"__ref": "RrdYYLlUF4He"}], "args": [], "attrs": {}, "rs": {"__ref": "SoXWxe0xvkD2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Kxjzb78nR-7a": {"variants": [{"__ref": "TxFEt7KZNNsK"}], "args": [], "attrs": {}, "rs": {"__ref": "UCfZN5H-YtvM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3-6DfBuZcen3": {"variants": [{"__ref": "uSRzBesvIzds"}], "args": [], "attrs": {}, "rs": {"__ref": "mwxgLyye-UHS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "p8Mf8pU-edSt": {"variants": [{"__ref": "gs8DsWBtgg2A"}], "args": [], "attrs": {}, "rs": {"__ref": "2w9_nbz55qfa"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "CV4tNT3iEtpn": {"variants": [{"__ref": "gs8DsWBtgg2A"}, {"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "bP5wv5gqzK9i"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "vBa4zn0F7duN": {"variants": [{"__ref": "gs8DsWBtgg2A"}, {"__ref": "uxKEiZcqedp9"}], "args": [], "attrs": {}, "rs": {"__ref": "n8owzzXKtlWQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "VwmRHGA4R9db": {"variants": [{"__ref": "IToJ04sZLo_P"}], "args": [], "attrs": {}, "rs": {"__ref": "Zge7Kl2W4zJq"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "cWX9-Nnxg0Do": {"variants": [{"__ref": "Lz9juLj562BM"}], "args": [], "attrs": {}, "rs": {"__ref": "d9ZoDYbSK9GH"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "EXQHrUZfz476": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "0vM5wm-edk7_": {"code": "false", "fallback": null, "__type": "CustomCode"}, "vEC7kH6alKBY": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "gmkN5yqiHUFk": {"code": "true", "fallback": null, "__type": "CustomCode"}, "LEg4sDIaJ6k-": {"values": {}, "mixins": [], "__type": "RuleSet"}, "XVO1Bc8Upiqo": {"values": {}, "mixins": [], "__type": "RuleSet"}, "I5rLVWQxApNV": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "xkGrmN7HWmnc", "parent": {"__ref": "UefTUd8b38Bc"}, "locked": null, "vsettings": [{"__ref": "AuK2fKwUm5tq"}], "__type": "TplTag"}, "x-lWH2QkhYdZ": {"variants": [{"__ref": "tV1959nPHhqn"}], "args": [], "attrs": {}, "rs": {"__ref": "ktFCMSg_t_y6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4YcInLMy0k1E": {"variants": [{"__ref": "98bCPPxdZwa4"}], "args": [], "attrs": {}, "rs": {"__ref": "Jh2P3IBxY_f0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9R7-ZabA8Yfa": {"variants": [{"__ref": "unYEfNHe7XRn"}], "args": [], "attrs": {}, "rs": {"__ref": "sP_yyDgV7WFT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "cvLCfvE089cK": {"variants": [{"__ref": "pF6EHk9Uh077"}], "args": [], "attrs": {}, "rs": {"__ref": "u9vcDqdkrksA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "CnrE-n9U2xWW": {"variants": [{"__ref": "dOBSQ071peO9"}], "args": [], "attrs": {}, "rs": {"__ref": "pWqS5qCsYyuQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "N1lAV2q0YREA": {"variants": [{"__ref": "bi1zXsK_HcA4"}], "args": [], "attrs": {}, "rs": {"__ref": "VrwVhJN6ValQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "jpqMd4wVrF2q": {"variants": [{"__ref": "W90MB8cWV6oZ"}], "args": [], "attrs": {}, "rs": {"__ref": "uRzW8olMTOR9"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MAp6md6o4ozg": {"variants": [{"__ref": "R2Yy5npBRtiC"}], "args": [], "attrs": {}, "rs": {"__ref": "TXQC2yyw1vQd"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "UBU3Y-8yjFqK": {"variants": [{"__ref": "uSRzBesvIzds"}], "args": [], "attrs": {}, "rs": {"__ref": "LVgh1xXuDsZX"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "1Y8EgM6WuLxU": {"variants": [{"__ref": "6rtOzVF7i-g4"}], "args": [], "attrs": {}, "rs": {"__ref": "w1b0HLiK0hh6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "tOZpvLruxHwO": {"variants": [{"__ref": "RrdYYLlUF4He"}], "args": [], "attrs": {}, "rs": {"__ref": "IIPMBXuIGO2b"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "XqKyUfeouu-k": {"variants": [{"__ref": "TxFEt7KZNNsK"}], "args": [], "attrs": {}, "rs": {"__ref": "FV4mqjyZ0N9S"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "sxU_R7uzV2-c": {"variants": [{"__ref": "O4dK_zihRgNS"}], "args": [], "attrs": {}, "rs": {"__ref": "PxmW0LJlf3dv"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "oRwtnPbV5tnj": {"variants": [{"__ref": "d9k4kNmKBrnH"}], "args": [], "attrs": {}, "rs": {"__ref": "IuffRkH08OWN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "RSNhWFxchEUT": {"variants": [{"__ref": "pFLgJ0hUYaIP"}], "args": [], "attrs": {}, "rs": {"__ref": "B1GE-yeU5WFQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "IUo7o3gcHJl4": {"variants": [{"__ref": "aW09_WHHY8No"}], "args": [], "attrs": {}, "rs": {"__ref": "HWdzygm_PU6H"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "TLeo5ZePWjtV": {"variants": [{"__ref": "b7j7re9dG2TE"}], "args": [], "attrs": {}, "rs": {"__ref": "8GLxjsJ11wML"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "we8Wr2VBcDE0": {"variants": [{"__ref": "IToJ04sZLo_P"}], "args": [], "attrs": {}, "rs": {"__ref": "6ZxxZtY_2Jte"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bmDOfyKqMd0z": {"variants": [{"__ref": "gs8DsWBtgg2A"}], "args": [], "attrs": {}, "rs": {"__ref": "sZT6Eq0kCHwd"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "X7zXjtyl0ckb": {"variants": [{"__ref": "gs8DsWBtgg2A"}, {"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "xXGOCmnPptlk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "vsvdiDb9yRpL": {"variants": [{"__ref": "gs8DsWBtgg2A"}, {"__ref": "uxKEiZcqedp9"}], "args": [], "attrs": {}, "rs": {"__ref": "0Fl0kndznIFf"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "EJzj0T43yHDc": {"variants": [{"__ref": "gs8DsWBtgg2A"}, {"__ref": "KelsXKS-Sk5C"}], "args": [], "attrs": {}, "rs": {"__ref": "SXeZNw6UyO_B"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "prEM6wJDZEIF": {"variants": [{"__ref": "gs8DsWBtgg2A"}, {"__ref": "KelsXKS-Sk5C"}, {"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "xpOEmOTYbtiK"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "91WTPjvuXg0k": {"variants": [{"__ref": "KelsXKS-Sk5C"}], "args": [], "attrs": {}, "rs": {"__ref": "HkU6wUOcVDzA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "siSDDRkMv-Ve": {"variants": [{"__ref": "KelsXKS-Sk5C"}, {"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "meP3rdRJr4vt"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "L8fAHy5NcNiM": {"variants": [{"__ref": "Lz9juLj562BM"}], "args": [], "attrs": {}, "rs": {"__ref": "9S7ITgKXFF_L"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eA3b8TBpwwrq": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "enQxTmnbP24D": {"values": {}, "mixins": [], "__type": "RuleSet"}, "F9X7Vgg4imEo": {"values": {}, "mixins": [], "__type": "RuleSet"}, "JfLztoG3MMuD": {"values": {}, "mixins": [], "__type": "RuleSet"}, "0r6MLy6v14ke": {"values": {}, "mixins": [], "__type": "RuleSet"}, "sQpb0nY2QU09": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "YEnEo46Lxjm8", "parent": {"__ref": "opcPkVDFUkfp"}, "locked": null, "vsettings": [{"__ref": "8e5LzYP8n9Qj"}], "__type": "TplTag"}, "IiU467mzwaJa": {"variants": [{"__ref": "tV1959nPHhqn"}], "args": [], "attrs": {}, "rs": {"__ref": "xM0B9MyYsaFj"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "FMDa_pMxbFbW": {"variants": [{"__ref": "dOBSQ071peO9"}], "args": [], "attrs": {}, "rs": {"__ref": "t8lDKU-DWWmd"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "EWn6NnFWBiGS": {"variants": [{"__ref": "W90MB8cWV6oZ"}], "args": [], "attrs": {}, "rs": {"__ref": "nmc0dNofrJ50"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PWFlO4_mGHpJ": {"variants": [{"__ref": "R2Yy5npBRtiC"}], "args": [], "attrs": {}, "rs": {"__ref": "0gkAUBIeVaCD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "FciheY_IZ3O2": {"variants": [{"__ref": "6rtOzVF7i-g4"}], "args": [], "attrs": {}, "rs": {"__ref": "SA-GhXWh5IaB"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "K_PAuSAuNmvo": {"variants": [{"__ref": "RrdYYLlUF4He"}], "args": [], "attrs": {}, "rs": {"__ref": "Pc5CNcwJ4RjM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yEehm61leTxs": {"variants": [{"__ref": "TxFEt7KZNNsK"}], "args": [], "attrs": {}, "rs": {"__ref": "4INNKSfK9_2q"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PRFjGWcValQa": {"variants": [{"__ref": "uSRzBesvIzds"}], "args": [], "attrs": {}, "rs": {"__ref": "Y8nQaVZKokU-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5dNpzjLWowNf": {"variants": [{"__ref": "gs8DsWBtgg2A"}], "args": [], "attrs": {}, "rs": {"__ref": "Xi5s8osMp6fe"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Hn-lpd5LThDd": {"variants": [{"__ref": "gs8DsWBtgg2A"}, {"__ref": "NlNWf7-MImkI"}], "args": [], "attrs": {}, "rs": {"__ref": "vkZ4aXqQ6B7Z"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "V-nCFbWCD85U": {"variants": [{"__ref": "gs8DsWBtgg2A"}, {"__ref": "uxKEiZcqedp9"}], "args": [], "attrs": {}, "rs": {"__ref": "3q4UYgm5MqgI"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "NW2SqpYtNC5p": {"variants": [{"__ref": "IToJ04sZLo_P"}], "args": [], "attrs": {}, "rs": {"__ref": "WxyC4t4P-IbR"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "GghQNsoNQVlE": {"variants": [{"__ref": "Lz9juLj562BM"}], "args": [], "attrs": {}, "rs": {"__ref": "4f9YBIPEo6cJ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2_sD1kMJz26b": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "fm1mJWloJvR2": {"code": "false", "fallback": null, "__type": "CustomCode"}, "R9TL8T_a86bD": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "EUW-mjWiZO12": {"code": "true", "fallback": null, "__type": "CustomCode"}, "9iz-cJeAnJKs": {"values": {}, "mixins": [], "__type": "RuleSet"}, "V7PkGjOuJZff": {"values": {}, "mixins": [], "__type": "RuleSet"}, "AWK2ieJfcWLM": {"uuid": "BXM20u5mVyic", "width": 1180, "height": 540, "container": {"__ref": "Gg9h19hyJrYl"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "tV1959nPHhqn"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "XCDwMtNGjZI9": {"uuid": "hWbJrLHixE0j", "width": 1180, "height": 540, "container": {"__ref": "s4nS273sFSXW"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "unYEfNHe7XRn"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "N6rOPe7dBf1L": {"uuid": "EpNeUF23DeWJ", "width": 1180, "height": 540, "container": {"__ref": "jP1JZouOlWwz"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "98bCPPxdZwa4"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "pyao8NTEBIDL": {"uuid": "UDQLCc6ipIWB", "width": 1180, "height": 540, "container": {"__ref": "vFQRkTE-nQ8f"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "NlNWf7-MImkI"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "SEho5bqTSFhi": {"uuid": "VL0eRhmIC-Dv", "width": 1180, "height": 540, "container": {"__ref": "-1UpYhnlCaE4"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "uxKEiZcqedp9"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "4ovzAEPomziz": {"uuid": "-0gfvJlaiIw6", "width": 1180, "height": 540, "container": {"__ref": "zyeZFTvFx91G"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "pF6EHk9Uh077"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "a3g08qQExUMa": {"uuid": "EsYJ0oYxYGOw", "width": 1180, "height": 540, "container": {"__ref": "YH1WFOUcQm8Y"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "dOBSQ071peO9"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "zLvC_gCXpCvx": {"uuid": "M-hQ0lV6LLtT", "width": 1180, "height": 540, "container": {"__ref": "mjzpOFMoZ_MY"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "bi1zXsK_HcA4"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "Gldfh4pTyoZ4": {"uuid": "7IAd1pFu28tY", "width": 1180, "height": 540, "container": {"__ref": "hUvqdTO0rLiS"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "b7j7re9dG2TE"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "MaQaSWDtMqkV": {"uuid": "pqavAj4e7VnQ", "width": 1180, "height": 540, "container": {"__ref": "GCVSH72a1PHz"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "8EgMkXce9H15"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "g0LvLVlAoMCO": {"uuid": "CMiebi4ZIy_U", "width": 1180, "height": 540, "container": {"__ref": "Sihvyp-a4ecc"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "_AU_l-6UE-d8"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "t853-x4jVtYX": {"uuid": "4n3sMNhTUwgM", "width": 1180, "height": 540, "container": {"__ref": "KR0fAanc9Pt2"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "pc8d1Faa0eKz"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "M7807I3sEKi-": {"uuid": "-m-JHf-CXOxK", "width": 1180, "height": 540, "container": {"__ref": "PDADfg7SAD2o"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "KelsXKS-Sk5C"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "NfH3r6szmdOg": {"uuid": "dq1DMIpALfmx", "width": 1180, "height": 540, "container": {"__ref": "M1NXLhdxHuDY"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "O4dK_zihRgNS"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "dxr7d5xYCTD5": {"uuid": "Sm7556Q9DQED", "width": 1180, "height": 540, "container": {"__ref": "x4r-_e1aZKV7"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "d9k4kNmKBrnH"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "nRHIe7CyRe0Y": {"uuid": "G9PCbHRMJ2aZ", "width": 1180, "height": 540, "container": {"__ref": "djvg0U_Rxh5x"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "uSRzBesvIzds"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "_xtq406VOQVz": {"uuid": "f8Bm5vsJBh9c", "width": 1180, "height": 540, "container": {"__ref": "D0hbOBjRZ_Aw"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "aW09_WHHY8No"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "C9rCe65LlQ2O": {"uuid": "njjAJRWdz18v", "width": 1180, "height": 540, "container": {"__ref": "NuE0pu2R579A"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "pFLgJ0hUYaIP"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "4ePI-VSkm7ZE": {"uuid": "Wrjug4WvlIVD", "width": 1180, "height": 540, "container": {"__ref": "uiu2RxG5U64e"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "Lz9juLj562BM"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "iPc9e_dGsImF": {"uuid": "V7ekCBbJoYg_", "width": 1180, "height": 540, "container": {"__ref": "lxSHDEvieRVz"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "W90MB8cWV6oZ"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "6myngZEr_kMG": {"uuid": "EoHAyL1lc3IN", "width": 1180, "height": 540, "container": {"__ref": "dvN928mDkb33"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "R2Yy5npBRtiC"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "v5OtOYpVGFlk": {"uuid": "LONzXRcWtDrp", "width": 1180, "height": 540, "container": {"__ref": "bu5xK351vZxV"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "6rtOzVF7i-g4"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "9wS1Vd-izIBA": {"uuid": "WG7OAybymF3o", "width": 1180, "height": 540, "container": {"__ref": "VwA0i8X-ajII"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "RrdYYLlUF4He"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "JlU5b9DBQ4Ru": {"uuid": "yLw19Fg4bGt2", "width": 1180, "height": 540, "container": {"__ref": "B32tZpa_WEmI"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "TxFEt7KZNNsK"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "ex6YOGxuAQCL": {"uuid": "MVvfZ5kcHcSZ", "width": 1180, "height": 540, "container": {"__ref": "Qb0UCzEKQ82O"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "IToJ04sZLo_P"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5_Dc0WdjDKLF": {"uuid": "6XqpFBIw62QG", "width": 1180, "height": 540, "container": {"__ref": "Td5IGQvXSFeN"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "gs8DsWBtgg2A"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "6ABVczUo4GNX": {"uuid": "QZfEIKdq5L-f", "width": 1180, "height": 540, "container": {"__ref": "U6EnRUIuorVV"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "W90MB8cWV6oZ"}, {"__ref": "NlNWf7-MImkI"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "eTZ_ugmScjag": {"uuid": "cZmhfT5lXRm-", "width": 1180, "height": 540, "container": {"__ref": "dZl7R3wm-6C0"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "W90MB8cWV6oZ"}, {"__ref": "uxKEiZcqedp9"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "U5zbdr_VqDdk": {"uuid": "4zwJFo6fRUmL", "width": 1180, "height": 540, "container": {"__ref": "7kRHDcTA3HXv"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "R2Yy5npBRtiC"}, {"__ref": "uxKEiZcqedp9"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "DAolsztNl540": {"uuid": "yEvThLdyrsg0", "width": 1180, "height": 540, "container": {"__ref": "ckJBPg6YGNmz"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "R2Yy5npBRtiC"}, {"__ref": "NlNWf7-MImkI"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "NUjf97nzWJkH": {"uuid": "2Zmy3-cxR--P", "width": 1180, "height": 540, "container": {"__ref": "XIzoybab3p_4"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "uSRzBesvIzds"}, {"__ref": "NlNWf7-MImkI"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "WUI3K-YsGzsL": {"uuid": "rLKw2otyregx", "width": 1180, "height": 540, "container": {"__ref": "Hr4bnWL6hu1L"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "uSRzBesvIzds"}, {"__ref": "uxKEiZcqedp9"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "oB4h1W12VaRw": {"uuid": "Y8QBiWED4rM7", "width": 1180, "height": 540, "container": {"__ref": "XvqalIjOI_1V"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "aW09_WHHY8No"}, {"__ref": "NlNWf7-MImkI"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "dmURfBU7K4Yz": {"uuid": "T7jgBv7DgA9o", "width": 1180, "height": 540, "container": {"__ref": "hZAfla85lktL"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "aW09_WHHY8No"}, {"__ref": "uxKEiZcqedp9"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "T4Sm13FovSI4": {"uuid": "2uuAp4dclD4F", "width": 1180, "height": 540, "container": {"__ref": "AQMJykQop546"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "6rtOzVF7i-g4"}, {"__ref": "uxKEiZcqedp9"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "myHfzZ82gmLW": {"uuid": "4U3a5pivFMHt", "width": 1180, "height": 540, "container": {"__ref": "zNfT24UhSWAJ"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "6rtOzVF7i-g4"}, {"__ref": "NlNWf7-MImkI"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "jFJ2Pm3fkrSB": {"uuid": "HRvb_aNOdEme", "width": 1180, "height": 540, "container": {"__ref": "ptyzzFwKwVci"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "RrdYYLlUF4He"}, {"__ref": "uxKEiZcqedp9"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "YmZ3bSo_y9mx": {"uuid": "mB3HyrarXyyb", "width": 1180, "height": 540, "container": {"__ref": "TOGu0F_146bW"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "RrdYYLlUF4He"}, {"__ref": "NlNWf7-MImkI"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "XjnTkFfiTd6k": {"uuid": "pyKKuLIZSqRR", "width": 1180, "height": 540, "container": {"__ref": "7bchaelY0fyo"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "TxFEt7KZNNsK"}, {"__ref": "NlNWf7-MImkI"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "cTFVMTDq3pmz": {"uuid": "yI2B5AqxgHrQ", "width": 1180, "height": 540, "container": {"__ref": "1uN9cKpBTJu9"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "TxFEt7KZNNsK"}, {"__ref": "uxKEiZcqedp9"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "O5JwMVWhE5d7": {"uuid": "ZP4Ltcr6KWh1", "width": 1180, "height": 540, "container": {"__ref": "Y8DVFfDn7pMb"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "O4dK_zihRgNS"}, {"__ref": "NlNWf7-MImkI"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "V8j1dFMDQUHV": {"uuid": "Nok9TrDhMgjL", "width": 1180, "height": 540, "container": {"__ref": "6PSqOFf8ycm-"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "O4dK_zihRgNS"}, {"__ref": "uxKEiZcqedp9"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "AHjyFjAjiUqS": {"uuid": "ov2Jn9ov1ZlE", "width": 1180, "height": 540, "container": {"__ref": "d3AwK_hRrlRd"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "d9k4kNmKBrnH"}, {"__ref": "NlNWf7-MImkI"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "v_bqEZG8fwjo": {"uuid": "woD3FcTrYmZg", "width": 1180, "height": 540, "container": {"__ref": "H-slF076PELU"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "d9k4kNmKBrnH"}, {"__ref": "uxKEiZcqedp9"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "JLX1oxtB27zc": {"uuid": "gbFk2KtyWbpz", "width": 1180, "height": 540, "container": {"__ref": "lMM-N2cvSFHJ"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "pFLgJ0hUYaIP"}, {"__ref": "NlNWf7-MImkI"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "0b3ECaCkqSYG": {"uuid": "jz3plPYODgzu", "width": 1180, "height": 540, "container": {"__ref": "gpvnQJbbXcwx"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "pFLgJ0hUYaIP"}, {"__ref": "uxKEiZcqedp9"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "0683td4Trep2": {"uuid": "pSKFwru5kAxE", "width": 1180, "height": 540, "container": {"__ref": "N9DJ3uO4oN_W"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "b7j7re9dG2TE"}, {"__ref": "pF6EHk9Uh077"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "Hqed1ExWi2Vi": {"uuid": "beZnGB1Qa6uA", "width": 1180, "height": 540, "container": {"__ref": "h1GTnE9k3nRf"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "dOBSQ071peO9"}, {"__ref": "b7j7re9dG2TE"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "FsvzeLzz6xOH": {"uuid": "ab02fScuCcvF", "width": 1180, "height": 540, "container": {"__ref": "jmI143OfRMNf"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "IToJ04sZLo_P"}, {"__ref": "NlNWf7-MImkI"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "VTsUohH2v0oQ": {"uuid": "lx7fYY18v78A", "width": 1180, "height": 540, "container": {"__ref": "Qe_rsUgRC19l"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "IToJ04sZLo_P"}, {"__ref": "uxKEiZcqedp9"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "6KAXRqhK_o9X": {"uuid": "PCQZAOKhmPtQ", "width": 1180, "height": 540, "container": {"__ref": "eynaWG2ACmAq"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "8EgMkXce9H15"}, {"__ref": "pc8d1Faa0eKz"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "UqKCLlgyRZ0u": {"uuid": "WKZoT_EQUzUG", "width": 1180, "height": 540, "container": {"__ref": "UUdv2vpeKQCA"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "gs8DsWBtgg2A"}, {"__ref": "NlNWf7-MImkI"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "gxv7ri1N3NSJ": {"uuid": "52ceBhmC6YhU", "width": 1180, "height": 540, "container": {"__ref": "ar7xsmnoSWBC"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "gs8DsWBtgg2A"}, {"__ref": "uxKEiZcqedp9"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "Rb61H090BAkL": {"uuid": "nRgkTEjQkRFr", "width": 1180, "height": 540, "container": {"__ref": "RFsTpTVKAnFs"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "Lz9juLj562BM"}, {"__ref": "NlNWf7-MImkI"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5zz42VzzgW4G": {"uuid": "GBCtlmEtlli8", "width": 1180, "height": 540, "container": {"__ref": "JJEaW3CkR8bD"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "Lz9juLj562BM"}, {"__ref": "uxKEiZcqedp9"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "s35l4rH7ZIfM": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "jp2di9qRl3gX", "parent": {"__ref": "ahJBCyYiLSyn"}, "locked": null, "vsettings": [{"__ref": "LlDr5Vmnr1MS"}], "__type": "TplTag"}, "BkKQ3JPgIyn1": {"variants": [{"__ref": "tV1959nPHhqn"}], "args": [], "attrs": {"outerHTML": {"__ref": "iE6keewIrTwL"}}, "rs": {"__ref": "Q7d7F-SDA_UD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "B0WJs6gYwONn": {"values": {"color": "#EDEDEC"}, "mixins": [], "__type": "RuleSet"}, "gsopkaZ5tYHD": {"values": {}, "mixins": [], "__type": "RuleSet"}, "DRg7Mx0y0eVO": {"values": {}, "mixins": [], "__type": "RuleSet"}, "PFhPj0317owB": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "q0c8kvtsdL9r": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "FHqFcp81vUhq": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "SoXWxe0xvkD2": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "UCfZN5H-YtvM": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "mwxgLyye-UHS": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "2w9_nbz55qfa": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "bP5wv5gqzK9i": {"values": {"color": "#0081F1"}, "mixins": [], "__type": "RuleSet"}, "n8owzzXKtlWQ": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "Zge7Kl2W4zJq": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "d9ZoDYbSK9GH": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "AuK2fKwUm5tq": {"variants": [{"__ref": "tV1959nPHhqn"}], "args": [], "attrs": {}, "rs": {"__ref": "h7LX_IVUpX_1"}, "dataCond": null, "dataRep": null, "text": {"__ref": "V9eT-bEoX4-9"}, "columnsConfig": null, "__type": "VariantSetting"}, "ktFCMSg_t_y6": {"values": {"color": "#EDEDEC", "font-weight": "500", "white-space": "nowrap"}, "mixins": [], "__type": "RuleSet"}, "Jh2P3IBxY_f0": {"values": {}, "mixins": [], "__type": "RuleSet"}, "sP_yyDgV7WFT": {"values": {}, "mixins": [], "__type": "RuleSet"}, "u9vcDqdkrksA": {"values": {}, "mixins": [], "__type": "RuleSet"}, "pWqS5qCsYyuQ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "VrwVhJN6ValQ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "uRzW8olMTOR9": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "TXQC2yyw1vQd": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "LVgh1xXuDsZX": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "w1b0HLiK0hh6": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "IIPMBXuIGO2b": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "FV4mqjyZ0N9S": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "PxmW0LJlf3dv": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "IuffRkH08OWN": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "B1GE-yeU5WFQ": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "HWdzygm_PU6H": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "8GLxjsJ11wML": {"values": {}, "mixins": [], "__type": "RuleSet"}, "6ZxxZtY_2Jte": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "sZT6Eq0kCHwd": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "xXGOCmnPptlk": {"values": {"color": "#0081F1", "text-decoration-line": "underline"}, "mixins": [], "__type": "RuleSet"}, "0Fl0kndznIFf": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "SXeZNw6UyO_B": {"values": {}, "mixins": [], "__type": "RuleSet"}, "xpOEmOTYbtiK": {"values": {}, "mixins": [], "__type": "RuleSet"}, "HkU6wUOcVDzA": {"values": {}, "mixins": [], "__type": "RuleSet"}, "meP3rdRJr4vt": {"values": {}, "mixins": [], "__type": "RuleSet"}, "9S7ITgKXFF_L": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "8e5LzYP8n9Qj": {"variants": [{"__ref": "tV1959nPHhqn"}], "args": [], "attrs": {"outerHTML": {"__ref": "lhkIJLZqbrD3"}}, "rs": {"__ref": "pStFjcU-oep4"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "xM0B9MyYsaFj": {"values": {"color": "#EDEDEC"}, "mixins": [], "__type": "RuleSet"}, "t8lDKU-DWWmd": {"values": {}, "mixins": [], "__type": "RuleSet"}, "nmc0dNofrJ50": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "0gkAUBIeVaCD": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "SA-GhXWh5IaB": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "Pc5CNcwJ4RjM": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "4INNKSfK9_2q": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "Y8nQaVZKokU-": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "Xi5s8osMp6fe": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "vkZ4aXqQ6B7Z": {"values": {"color": "#0081F1"}, "mixins": [], "__type": "RuleSet"}, "3q4UYgm5MqgI": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "WxyC4t4P-IbR": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "4f9YBIPEo6cJ": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "Gg9h19hyJrYl": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "FzNkkP9GY-D6", "parent": null, "locked": null, "vsettings": [{"__ref": "wKqH8qFhPQ_7"}], "__type": "TplComponent"}, "s4nS273sFSXW": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "uXB0MbuZxi3N", "parent": null, "locked": null, "vsettings": [{"__ref": "cM72wNhq1p5m"}], "__type": "TplComponent"}, "jP1JZouOlWwz": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "ni4nG1VwtFF2", "parent": null, "locked": null, "vsettings": [{"__ref": "hxNkvS7EEKWb"}], "__type": "TplComponent"}, "vFQRkTE-nQ8f": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "0pNP2UFbx1N_", "parent": null, "locked": null, "vsettings": [{"__ref": "9Ulv5QtNA_tj"}], "__type": "TplComponent"}, "-1UpYhnlCaE4": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "cj8Qto2N1al4", "parent": null, "locked": null, "vsettings": [{"__ref": "fZ-mGGCwWULD"}], "__type": "TplComponent"}, "zyeZFTvFx91G": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "YcmV8d0aAlM9", "parent": null, "locked": null, "vsettings": [{"__ref": "iDN9HIviqgK7"}], "__type": "TplComponent"}, "YH1WFOUcQm8Y": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "O1qM9QPStRFI", "parent": null, "locked": null, "vsettings": [{"__ref": "XOYg39W5HTc2"}], "__type": "TplComponent"}, "mjzpOFMoZ_MY": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "quDjea7idKF_", "parent": null, "locked": null, "vsettings": [{"__ref": "EjnTlP2PSSUT"}], "__type": "TplComponent"}, "hUvqdTO0rLiS": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "v-qJrcbi6nSx", "parent": null, "locked": null, "vsettings": [{"__ref": "gbplMHeDeBte"}], "__type": "TplComponent"}, "GCVSH72a1PHz": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "sVK4SdnIx7f2", "parent": null, "locked": null, "vsettings": [{"__ref": "6tG2ckIgnvjL"}], "__type": "TplComponent"}, "Sihvyp-a4ecc": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "d00UwEm0ZTmt", "parent": null, "locked": null, "vsettings": [{"__ref": "ecj4VtIcQHbM"}], "__type": "TplComponent"}, "KR0fAanc9Pt2": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "ABwAy_4dUGmc", "parent": null, "locked": null, "vsettings": [{"__ref": "r6BndGZQUvwE"}], "__type": "TplComponent"}, "PDADfg7SAD2o": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "pu0YBZrtOMSk", "parent": null, "locked": null, "vsettings": [{"__ref": "3WvteWQUo8_7"}], "__type": "TplComponent"}, "M1NXLhdxHuDY": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "6hQGAOc084b2", "parent": null, "locked": null, "vsettings": [{"__ref": "dxk2dvRlYLgn"}], "__type": "TplComponent"}, "x4r-_e1aZKV7": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "vCBZPDW_uKgg", "parent": null, "locked": null, "vsettings": [{"__ref": "bDY6dhYfFbjR"}], "__type": "TplComponent"}, "djvg0U_Rxh5x": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "LLKEosCW08Ry", "parent": null, "locked": null, "vsettings": [{"__ref": "WxECYyWE8br_"}], "__type": "TplComponent"}, "D0hbOBjRZ_Aw": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "9sFmUw7W5aa2", "parent": null, "locked": null, "vsettings": [{"__ref": "sIGjqmO7zZ7G"}], "__type": "TplComponent"}, "NuE0pu2R579A": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "3N5maSSVk4Qi", "parent": null, "locked": null, "vsettings": [{"__ref": "lIeVWp_7j_Cx"}], "__type": "TplComponent"}, "uiu2RxG5U64e": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "hgUfEMSnBbXu", "parent": null, "locked": null, "vsettings": [{"__ref": "OQmDnXKVL2zY"}], "__type": "TplComponent"}, "lxSHDEvieRVz": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "GvOkstDmaugl", "parent": null, "locked": null, "vsettings": [{"__ref": "WCtUEJ47Hdp9"}], "__type": "TplComponent"}, "dvN928mDkb33": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "Jualv-mDWCdw", "parent": null, "locked": null, "vsettings": [{"__ref": "veYrRTNjJy7w"}], "__type": "TplComponent"}, "bu5xK351vZxV": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "4KEifm_9ut-s", "parent": null, "locked": null, "vsettings": [{"__ref": "baTNtemRm3St"}], "__type": "TplComponent"}, "VwA0i8X-ajII": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "irk9Zm3ZrQt1", "parent": null, "locked": null, "vsettings": [{"__ref": "z7kSP6HLClwV"}], "__type": "TplComponent"}, "B32tZpa_WEmI": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "MDzFvLhuR_Sn", "parent": null, "locked": null, "vsettings": [{"__ref": "OraQVyp3OhmK"}], "__type": "TplComponent"}, "Qb0UCzEKQ82O": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "pxiz0aPJOwqF", "parent": null, "locked": null, "vsettings": [{"__ref": "7hpNUf9UBbBd"}], "__type": "TplComponent"}, "Td5IGQvXSFeN": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "Zg2S6_OUNfxp", "parent": null, "locked": null, "vsettings": [{"__ref": "qMeOZutPubch"}], "__type": "TplComponent"}, "U6EnRUIuorVV": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "ENCIUKU_2bWG", "parent": null, "locked": null, "vsettings": [{"__ref": "9Rjp-pwVPM7J"}], "__type": "TplComponent"}, "dZl7R3wm-6C0": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "xT8EksMwq4Wz", "parent": null, "locked": null, "vsettings": [{"__ref": "hwSXVNFEBZsb"}], "__type": "TplComponent"}, "7kRHDcTA3HXv": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "Z3EwMEHFcHJg", "parent": null, "locked": null, "vsettings": [{"__ref": "PByvL2gCc6IB"}], "__type": "TplComponent"}, "ckJBPg6YGNmz": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "3X_mlO1AnFQn", "parent": null, "locked": null, "vsettings": [{"__ref": "gdomSuUCRDRG"}], "__type": "TplComponent"}, "XIzoybab3p_4": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "Sa_i7SzTFwts", "parent": null, "locked": null, "vsettings": [{"__ref": "8SBklYwzo0SR"}], "__type": "TplComponent"}, "Hr4bnWL6hu1L": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "f5kOyTmgM2EL", "parent": null, "locked": null, "vsettings": [{"__ref": "tCEA-LT6EHQ5"}], "__type": "TplComponent"}, "XvqalIjOI_1V": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "UPOJ3Gq-7V1e", "parent": null, "locked": null, "vsettings": [{"__ref": "xitBGfhlhC4r"}], "__type": "TplComponent"}, "hZAfla85lktL": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "0namOCEfs3NB", "parent": null, "locked": null, "vsettings": [{"__ref": "1UMdhMDlKnys"}], "__type": "TplComponent"}, "AQMJykQop546": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "eJIYVmnV87KD", "parent": null, "locked": null, "vsettings": [{"__ref": "3-ilbwXq0ar7"}], "__type": "TplComponent"}, "zNfT24UhSWAJ": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "g35sJQT48QuL", "parent": null, "locked": null, "vsettings": [{"__ref": "fwo5KEojWn66"}], "__type": "TplComponent"}, "ptyzzFwKwVci": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "sS4OBCSJtbIj", "parent": null, "locked": null, "vsettings": [{"__ref": "gvZWzW-y6jGx"}], "__type": "TplComponent"}, "TOGu0F_146bW": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "LrOpJxRfXEc1", "parent": null, "locked": null, "vsettings": [{"__ref": "FsGddVVRIH2Q"}], "__type": "TplComponent"}, "7bchaelY0fyo": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "-mFfEo4A_t49", "parent": null, "locked": null, "vsettings": [{"__ref": "ObXuwVaP6CED"}], "__type": "TplComponent"}, "1uN9cKpBTJu9": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "svyPyZn79G5b", "parent": null, "locked": null, "vsettings": [{"__ref": "bd-19-RS7uqS"}], "__type": "TplComponent"}, "Y8DVFfDn7pMb": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "erOdvIlzpDuT", "parent": null, "locked": null, "vsettings": [{"__ref": "BNMn10unIimC"}], "__type": "TplComponent"}, "6PSqOFf8ycm-": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "MgpuNg-4Gurv", "parent": null, "locked": null, "vsettings": [{"__ref": "vZelHY3XPBU1"}], "__type": "TplComponent"}, "d3AwK_hRrlRd": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "oMiQsJhyLLVM", "parent": null, "locked": null, "vsettings": [{"__ref": "2cAAMEYRMEL3"}], "__type": "TplComponent"}, "H-slF076PELU": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "mogOPu-z5F1Q", "parent": null, "locked": null, "vsettings": [{"__ref": "9Evy6jq0XOtZ"}], "__type": "TplComponent"}, "lMM-N2cvSFHJ": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "mWj41sYVDRVH", "parent": null, "locked": null, "vsettings": [{"__ref": "s5yVzP5eLeqG"}], "__type": "TplComponent"}, "gpvnQJbbXcwx": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "hjKSsm4k_vwX", "parent": null, "locked": null, "vsettings": [{"__ref": "X5HMQJsrj4XX"}], "__type": "TplComponent"}, "N9DJ3uO4oN_W": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "6Ujk1BTKjRMD", "parent": null, "locked": null, "vsettings": [{"__ref": "CQ2OWBqCwJIS"}], "__type": "TplComponent"}, "h1GTnE9k3nRf": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "yRBLk0eRzgEy", "parent": null, "locked": null, "vsettings": [{"__ref": "Bs2Y1RU4vrgz"}], "__type": "TplComponent"}, "jmI143OfRMNf": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "dKA9ZA4IVJhF", "parent": null, "locked": null, "vsettings": [{"__ref": "auQ2OhNC1gQv"}], "__type": "TplComponent"}, "Qe_rsUgRC19l": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "qV7TiNWKk77L", "parent": null, "locked": null, "vsettings": [{"__ref": "gZNI6S6ZUbWp"}], "__type": "TplComponent"}, "eynaWG2ACmAq": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "8Dm_xZ_qpHmd", "parent": null, "locked": null, "vsettings": [{"__ref": "KpB78hmfC9fg"}], "__type": "TplComponent"}, "UUdv2vpeKQCA": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "biRMaH7bFSUC", "parent": null, "locked": null, "vsettings": [{"__ref": "asXeXBp5cFHc"}], "__type": "TplComponent"}, "ar7xsmnoSWBC": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "aOmbrQq0Ml94", "parent": null, "locked": null, "vsettings": [{"__ref": "zPo0qxTRftDr"}], "__type": "TplComponent"}, "RFsTpTVKAnFs": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "3vHyC-17xH1y", "parent": null, "locked": null, "vsettings": [{"__ref": "YeLI_GKgiCc1"}], "__type": "TplComponent"}, "JJEaW3CkR8bD": {"name": null, "component": {"__ref": "b8iFGp3XGZsu"}, "uuid": "cz2Xrfcsg4N_", "parent": null, "locked": null, "vsettings": [{"__ref": "JnqUAiLLMJBB"}], "__type": "TplComponent"}, "LlDr5Vmnr1MS": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "kfbIFP37KrqZ"}, "dataCond": null, "dataRep": null, "text": {"__ref": "LeYl74Nat8ly"}, "columnsConfig": null, "__type": "VariantSetting"}, "iE6keewIrTwL": {"asset": {"__ref": "rxUOs-eIvLZ9"}, "__type": "ImageAssetRef"}, "Q7d7F-SDA_UD": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "h7LX_IVUpX_1": {"values": {}, "mixins": [], "__type": "RuleSet"}, "V9eT-bEoX4-9": {"markers": [], "text": "<PERSON><PERSON>", "__type": "RawText"}, "lhkIJLZqbrD3": {"asset": {"__ref": "K2AgylbEF-8g"}, "__type": "ImageAssetRef"}, "pStFjcU-oep4": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "wKqH8qFhPQ_7": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "dPV9UXz-Jm_o"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "cM72wNhq1p5m": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "GkSKPFnGM7Jo"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "hxNkvS7EEKWb": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "Wz0N8D6M0mE7"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9Ulv5QtNA_tj": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "9Vvg1Tx8JeWm"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "fZ-mGGCwWULD": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "zK8IiKZf2Tjm"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "iDN9HIviqgK7": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "Oy0h3jO_epXL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "XOYg39W5HTc2": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "Z5SKuywYCHvX"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "EjnTlP2PSSUT": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "vyamDxdkpCye"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "gbplMHeDeBte": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "72EFOM2ZhQKu"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "6tG2ckIgnvjL": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "uk4rvJ68nazM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ecj4VtIcQHbM": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "ZbEWgPKzrU3_"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "r6BndGZQUvwE": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "6x8se-TyndpJ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3WvteWQUo8_7": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "-i64FQf_qx69"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "dxk2dvRlYLgn": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "xdW5hRrc_xOv"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bDY6dhYfFbjR": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "GwfFwbeiVCEn"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "WxECYyWE8br_": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "txxwe9cWuDz1"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "sIGjqmO7zZ7G": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "5DNByMk22Njo"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lIeVWp_7j_Cx": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "FXmMaAnnQaxj"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "OQmDnXKVL2zY": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "ta5XdrTbXpk_"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "WCtUEJ47Hdp9": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "pw-a1QObEdhr"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "veYrRTNjJy7w": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "CmwSKVDc61Gq"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "baTNtemRm3St": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "HAgosu0W7BHj"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "z7kSP6HLClwV": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "7HU7yPTxhO_n"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "OraQVyp3OhmK": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "FD7SQw_VOqo0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7hpNUf9UBbBd": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "7OEiFQzzVYN2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "qMeOZutPubch": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "1g0evrbbbVAW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9Rjp-pwVPM7J": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "yYcf3wo-RXGL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "hwSXVNFEBZsb": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "s8atcqDyv_yb"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PByvL2gCc6IB": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "6HxSGUjawjpc"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "gdomSuUCRDRG": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "NfRBEDvk98hZ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "8SBklYwzo0SR": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "LZ8TlBN_7AZi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "tCEA-LT6EHQ5": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "rqmE5Hkl8cb6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "xitBGfhlhC4r": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "2ULhZQ62msWv"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "1UMdhMDlKnys": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "p9aS9BqUsP1Q"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3-ilbwXq0ar7": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "Hh1WFEMLB-um"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "fwo5KEojWn66": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "2iwyMt5Mku5a"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "gvZWzW-y6jGx": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "v9XiOZJFKpHq"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "FsGddVVRIH2Q": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "wtjlTPcRk2zi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ObXuwVaP6CED": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "dwGDIALue48H"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bd-19-RS7uqS": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "PiH3Lm5tmJD5"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "BNMn10unIimC": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "EDM50We7qeHa"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "vZelHY3XPBU1": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "4s8iyLI3462a"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2cAAMEYRMEL3": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "4Y3jCYgT7tDu"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9Evy6jq0XOtZ": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "X1yHyYfXsbLh"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "s5yVzP5eLeqG": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "QUXs3il451QD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "X5HMQJsrj4XX": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "uBX4qCZ6fK_e"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "CQ2OWBqCwJIS": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "KzIInH-kvhdX"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Bs2Y1RU4vrgz": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "5s2omyGRsyIF"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "auQ2OhNC1gQv": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "Zb131YkKSJOl"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "gZNI6S6ZUbWp": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "BjlLs2NTz8IY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "KpB78hmfC9fg": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "03QqcgLLXYXj"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "asXeXBp5cFHc": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "zj8Qo6a4KRe2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zPo0qxTRftDr": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "YaSrMJo0jPcs"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "YeLI_GKgiCc1": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "VRHzf9Y9yXyC"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "JnqUAiLLMJBB": {"variants": [{"__ref": "YaH5yOkOuXCR"}], "args": [], "attrs": {}, "rs": {"__ref": "Ol5SMDNZKQ4Y"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kfbIFP37KrqZ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "dPV9UXz-Jm_o": {"values": {}, "mixins": [], "__type": "RuleSet"}, "GkSKPFnGM7Jo": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Wz0N8D6M0mE7": {"values": {}, "mixins": [], "__type": "RuleSet"}, "9Vvg1Tx8JeWm": {"values": {}, "mixins": [], "__type": "RuleSet"}, "zK8IiKZf2Tjm": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Oy0h3jO_epXL": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Z5SKuywYCHvX": {"values": {}, "mixins": [], "__type": "RuleSet"}, "vyamDxdkpCye": {"values": {}, "mixins": [], "__type": "RuleSet"}, "72EFOM2ZhQKu": {"values": {}, "mixins": [], "__type": "RuleSet"}, "uk4rvJ68nazM": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ZbEWgPKzrU3_": {"values": {}, "mixins": [], "__type": "RuleSet"}, "6x8se-TyndpJ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "-i64FQf_qx69": {"values": {}, "mixins": [], "__type": "RuleSet"}, "xdW5hRrc_xOv": {"values": {}, "mixins": [], "__type": "RuleSet"}, "GwfFwbeiVCEn": {"values": {}, "mixins": [], "__type": "RuleSet"}, "txxwe9cWuDz1": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5DNByMk22Njo": {"values": {}, "mixins": [], "__type": "RuleSet"}, "FXmMaAnnQaxj": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ta5XdrTbXpk_": {"values": {}, "mixins": [], "__type": "RuleSet"}, "pw-a1QObEdhr": {"values": {}, "mixins": [], "__type": "RuleSet"}, "CmwSKVDc61Gq": {"values": {}, "mixins": [], "__type": "RuleSet"}, "HAgosu0W7BHj": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7HU7yPTxhO_n": {"values": {}, "mixins": [], "__type": "RuleSet"}, "FD7SQw_VOqo0": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7OEiFQzzVYN2": {"values": {}, "mixins": [], "__type": "RuleSet"}, "1g0evrbbbVAW": {"values": {}, "mixins": [], "__type": "RuleSet"}, "yYcf3wo-RXGL": {"values": {}, "mixins": [], "__type": "RuleSet"}, "s8atcqDyv_yb": {"values": {}, "mixins": [], "__type": "RuleSet"}, "6HxSGUjawjpc": {"values": {}, "mixins": [], "__type": "RuleSet"}, "NfRBEDvk98hZ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "LZ8TlBN_7AZi": {"values": {}, "mixins": [], "__type": "RuleSet"}, "rqmE5Hkl8cb6": {"values": {}, "mixins": [], "__type": "RuleSet"}, "2ULhZQ62msWv": {"values": {}, "mixins": [], "__type": "RuleSet"}, "p9aS9BqUsP1Q": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Hh1WFEMLB-um": {"values": {}, "mixins": [], "__type": "RuleSet"}, "2iwyMt5Mku5a": {"values": {}, "mixins": [], "__type": "RuleSet"}, "v9XiOZJFKpHq": {"values": {}, "mixins": [], "__type": "RuleSet"}, "wtjlTPcRk2zi": {"values": {}, "mixins": [], "__type": "RuleSet"}, "dwGDIALue48H": {"values": {}, "mixins": [], "__type": "RuleSet"}, "PiH3Lm5tmJD5": {"values": {}, "mixins": [], "__type": "RuleSet"}, "EDM50We7qeHa": {"values": {}, "mixins": [], "__type": "RuleSet"}, "4s8iyLI3462a": {"values": {}, "mixins": [], "__type": "RuleSet"}, "4Y3jCYgT7tDu": {"values": {}, "mixins": [], "__type": "RuleSet"}, "X1yHyYfXsbLh": {"values": {}, "mixins": [], "__type": "RuleSet"}, "QUXs3il451QD": {"values": {}, "mixins": [], "__type": "RuleSet"}, "uBX4qCZ6fK_e": {"values": {}, "mixins": [], "__type": "RuleSet"}, "KzIInH-kvhdX": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5s2omyGRsyIF": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Zb131YkKSJOl": {"values": {}, "mixins": [], "__type": "RuleSet"}, "BjlLs2NTz8IY": {"values": {}, "mixins": [], "__type": "RuleSet"}, "03QqcgLLXYXj": {"values": {}, "mixins": [], "__type": "RuleSet"}, "zj8Qo6a4KRe2": {"values": {}, "mixins": [], "__type": "RuleSet"}, "YaSrMJo0jPcs": {"values": {}, "mixins": [], "__type": "RuleSet"}, "VRHzf9Y9yXyC": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Ol5SMDNZKQ4Y": {"values": {}, "mixins": [], "__type": "RuleSet"}, "LeYl74Nat8ly": {"markers": [], "text": "Copy \"abacaba\" to clipboard", "__type": "RawText"}, "OG-r7OsjkAzV": {"tpl": [{"__ref": "s35l4rH7ZIfM"}], "__type": "RenderExpr"}, "mYNF11REy2Hm": {"param": {"__ref": "mfUek-k0i6FJ"}, "expr": {"__ref": "uh8eTWfuGwHW"}, "__type": "Arg"}, "uh8eTWfuGwHW": {"variants": [{"__ref": "b7j7re9dG2TE"}], "__type": "VariantsRef"}, "gdeUWViwEfIT": {"param": {"__ref": "YErMFPjRvvOg"}, "expr": {"__ref": "JEeg7gUZHbWb"}, "__type": "Arg"}, "JEeg7gUZHbWb": {"variants": [{"__ref": "pc8d1Faa0eKz"}], "__type": "VariantsRef"}, "8oDZD1HHGQxb": {"param": {"__ref": "182S1uz0gVw6"}, "expr": {"__ref": "F_81jmajew82"}, "__type": "Arg"}, "F_81jmajew82": {"variants": [{"__ref": "W90MB8cWV6oZ"}], "__type": "VariantsRef"}, "vXWl7ouW-J8q": {"tag": "div", "name": null, "children": [{"__ref": "Jw2mg-GF6dhl"}, {"__ref": "ahJBCyYiLSyn"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Vb7Oej09psQb", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "_CG_Sm0Nc3Uo"}], "__type": "TplTag"}, "K-kKb4YerUfw": {"tpl": [{"__ref": "Cju46F_rD_bG"}], "__type": "VirtualRenderExpr"}, "bo47CF2WeQNf": {"tpl": [{"__ref": "pmiPLXOgB8Vy"}], "__type": "VirtualRenderExpr"}, "_CG_Sm0Nc3Uo": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "14ccvIM_laGi"}, "dataCond": {"__ref": "2cDxIH4YI9M6"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Cju46F_rD_bG": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "1QQfvn7SzQLd", "parent": {"__ref": "ahJBCyYiLSyn"}, "locked": null, "vsettings": [{"__ref": "8v3CEBnhJxzb"}], "__type": "TplTag"}, "pmiPLXOgB8Vy": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "5SVcqte3um8M", "parent": {"__ref": "ahJBCyYiLSyn"}, "locked": null, "vsettings": [{"__ref": "DdrtcND6PmxW"}], "__type": "TplTag"}, "14ccvIM_laGi": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "2cDxIH4YI9M6": {"code": "true", "fallback": null, "__type": "CustomCode"}, "8v3CEBnhJxzb": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {"outerHTML": {"__ref": "iJoHWeYTyRKy"}}, "rs": {"__ref": "4vXTHZbRBK3c"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "DdrtcND6PmxW": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {"outerHTML": {"__ref": "zD5HU3C7bdpd"}}, "rs": {"__ref": "yMrqKm28h7Nb"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "iJoHWeYTyRKy": {"asset": {"__ref": "rxUOs-eIvLZ9"}, "__type": "ImageAssetRef"}, "4vXTHZbRBK3c": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "zD5HU3C7bdpd": {"asset": {"__ref": "K2AgylbEF-8g"}, "__type": "ImageAssetRef"}, "yMrqKm28h7Nb": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "e9O8rdQhs40a": {"interactions": [{"__ref": "Mji3ll66GzNp"}], "__type": "EventHandler"}, "Mji3ll66GzNp": {"interactionName": "Run code", "actionName": "customFunction", "args": [{"__ref": "MrrYTx85ETFK"}], "condExpr": null, "conditionalMode": "always", "uuid": "gF5l1U716ejp", "parent": {"__ref": "e9O8rdQhs40a"}, "__type": "Interaction"}, "MrrYTx85ETFK": {"name": "customFunction", "expr": {"__ref": "Szpu42VjszOg"}, "__type": "NameArg"}, "Szpu42VjszOg": {"argNames": [], "bodyExpr": {"__ref": "ZKKe1gwGJKhi"}, "__type": "FunctionExpr"}, "ZKKe1gwGJKhi": {"code": "($$.copyToClipboard(\"abacaba\"))", "fallback": null, "__type": "CustomCode"}, "AhwOKQpbUH2c": {"param": {"__ref": "k-FfROanlGzR"}, "accessType": "private", "variableType": "number", "onChangeParam": {"__ref": "z39jU4WU0JIz"}, "tplNode": null, "implicitState": null, "__type": "State"}, "k-FfROanlGzR": {"type": {"__ref": "pHEc1BtayWtG"}, "state": {"__ref": "AhwOKQpbUH2c"}, "variable": {"__ref": "f2v9DP1Fo50x"}, "uuid": "-ITMJuU-Ie5k", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": {"__ref": "sM3d_T2d1npV"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "z39jU4WU0JIz": {"type": {"__ref": "ZV1gZVAr67Nw"}, "state": {"__ref": "AhwOKQpbUH2c"}, "variable": {"__ref": "Ueo_zmXerwxu"}, "uuid": "uoELBTqPRhpH", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "f2v9DP1Fo50x": {"name": "forceRerenderCount", "uuid": "uqMj9-trWzYj", "__type": "Var"}, "ZV1gZVAr67Nw": {"name": "func", "params": [{"__ref": "wdAB81664ErI"}], "__type": "FunctionType"}, "Ueo_zmXerwxu": {"name": "On forceRerenderCount Change", "uuid": "x-HKE0UWed7b", "__type": "Var"}, "wdAB81664ErI": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "-8JAbies1gqi"}, "__type": "ArgType"}, "-8JAbies1gqi": {"name": "text", "__type": "Text"}, "pHEc1BtayWtG": {"name": "num", "__type": "<PERSON><PERSON>"}, "sM3d_T2d1npV": {"code": "0", "fallback": null, "__type": "CustomCode"}, "YUQ9_L0YDQYW": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "N3fSRyXhEMpZ", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "zgb88xgXZTNX"}], "__type": "TplTag"}, "zgb88xgXZTNX": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "hU54Q2zub5jb"}, "dataCond": null, "dataRep": null, "text": {"__ref": "QQ_VIa4sX4s9"}, "columnsConfig": null, "__type": "VariantSetting"}, "hU54Q2zub5jb": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "QQ_VIa4sX4s9": {"expr": {"__ref": "6oXb-R4pzfk7"}, "html": false, "__type": "ExprText"}, "6oXb-R4pzfk7": {"code": "(const tomorrow = $$.dateFns.startOfTomorrow();\nconst threeDaysFromNow = $$.dateFns.add(tomorrow, {\n    days: 2,\n});\n`date-fns result: ${$$.dateFns.differenceInHours(threeDaysFromNow, tomorrow)} hours`)", "fallback": {"__ref": "O65Ze_xNyZXW"}, "__type": "CustomCode"}, "O65Ze_xNyZXW": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "nws3nHJS50oH": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "xpuqfnE4tVVh", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "NMGh6bpm3swr"}], "__type": "TplTag"}, "NMGh6bpm3swr": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "TuqS01jLH4sy"}, "dataCond": null, "dataRep": null, "text": {"__ref": "8rg3t2hFSTxY"}, "columnsConfig": null, "__type": "VariantSetting"}, "TuqS01jLH4sy": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "8rg3t2hFSTxY": {"expr": {"__ref": "wdPmiTMKIvA-"}, "html": false, "__type": "ExprText"}, "wdPmiTMKIvA-": {"code": "(`day.js number of days in August: ${$$.dayjs(\"2018-08-28\").daysInMonth()}`)", "fallback": {"__ref": "a4JrcidP10BM"}, "__type": "CustomCode"}, "a4JrcidP10BM": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "zkFy1agBmPAy": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "UO26FuRqZffP", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "BUJfV6jB_jgZ"}], "__type": "TplTag"}, "BUJfV6jB_jgZ": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "q4Bh0ZI7LrtC"}, "dataCond": null, "dataRep": null, "text": {"__ref": "BuH032VdrjhA"}, "columnsConfig": null, "__type": "VariantSetting"}, "q4Bh0ZI7LrtC": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "BuH032VdrjhA": {"expr": {"__ref": "030r-9LKgVSG"}, "html": false, "__type": "ExprText"}, "030r-9LKgVSG": {"code": "($$.fakerJs.seed(1000);\n$$.fakerJsExtras.fakerPT_BR.seed(1000);\n`Faker name: \"${$$.fakerJs.person.firstName()}\", PT-BR name: \"${$$.fakerJsExtras.fakerPT_BR.person.firstName()}\"`)", "fallback": {"__ref": "f2L8nrsu0OyI"}, "__type": "CustomCode"}, "f2L8nrsu0OyI": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "6EdDYdqr1VxK": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "x74vNT9rgvDq", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "0LR6Zg6BIBU6"}], "__type": "TplTag"}, "0LR6Zg6BIBU6": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "XN3_4H51nTLB"}, "dataCond": null, "dataRep": null, "text": {"__ref": "rczz1Ce4OmOc"}, "columnsConfig": null, "__type": "VariantSetting"}, "XN3_4H51nTLB": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "rczz1Ce4OmOc": {"expr": {"__ref": "uLzmKpiZNwEx"}, "html": false, "__type": "ExprText"}, "uLzmKpiZNwEx": {"code": "(const foo = {};\nconst bar = {};\nfoo.foo = foo;\nbar.bar = bar;\nfoo.bar = bar;\nbar.foo = foo;\n`fast-stringify: ${$$.stringify(foo)}`)", "fallback": {"__ref": "hWH5QUdtNrNN"}, "__type": "CustomCode"}, "hWH5QUdtNrNN": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "bbgy06ErXTK6": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "Lgs4o_jb4wxT", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "azh4tHk3rYJp"}], "__type": "TplTag"}, "azh4tHk3rYJp": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "hiQuqGmaAd7S"}, "dataCond": null, "dataRep": null, "text": {"__ref": "HdWFC0aZBBPp"}, "columnsConfig": null, "__type": "VariantSetting"}, "hiQuqGmaAd7S": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "HdWFC0aZBBPp": {"expr": {"__ref": "rl6UiQaMocAk"}, "html": false, "__type": "ExprText"}, "rl6UiQaMocAk": {"code": "(const baseState = [\n    {\n        title: \"Learn TypeScript\",\n        done: true\n    },\n    {\n        title: \"Try Immer\",\n        done: false\n    }\n]\nconst nextState = $$.immer.produce(baseState, draft => {\n    draft[1].done = true\n    draft.push({title: \"Tweet about it\"})\n});\n`Immer - state before: \"done === ${baseState[1].done}\"; state after: \"done === ${nextState[1].done}\"`)", "fallback": {"__ref": "vcp-bmIiMJBU"}, "__type": "CustomCode"}, "vcp-bmIiMJBU": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "iQDRtlSZfCF-": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "3GKE7iTYJoub", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "3JpGwNQuiFrr"}], "__type": "TplTag"}, "3JpGwNQuiFrr": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "8efriXaBc1ts"}, "dataCond": null, "dataRep": null, "text": {"__ref": "uDyPx2kK-3wh"}, "columnsConfig": null, "__type": "VariantSetting"}, "8efriXaBc1ts": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "zgBFCeattg8x": {"param": {"__ref": "R9GlckkK7pKv"}, "accessType": "private", "variableType": "boolean", "onChangeParam": {"__ref": "WEL5gPlgV--8"}, "tplNode": null, "implicitState": null, "__type": "State"}, "R9GlckkK7pKv": {"type": {"__ref": "cl4rbn1DKCdg"}, "state": {"__ref": "zgBFCeattg8x"}, "variable": {"__ref": "WrS1se3hjCje"}, "uuid": "3OtmMrq8lfMW", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": {"__ref": "TUIxyk5nox5c"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "WEL5gPlgV--8": {"type": {"__ref": "VJhaZZNRktpV"}, "state": {"__ref": "zgBFCeattg8x"}, "variable": {"__ref": "FE_ZYLzsoEk_"}, "uuid": "8Dw74jQNCwNC", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "WrS1se3hjCje": {"name": "mounted", "uuid": "69JeT3jih0Uc", "__type": "Var"}, "VJhaZZNRktpV": {"name": "func", "params": [{"__ref": "oRa6GSMrDVNa"}], "__type": "FunctionType"}, "FE_ZYLzsoEk_": {"name": "On mounted Change", "uuid": "o-PdSIrfCKty", "__type": "Var"}, "oRa6GSMrDVNa": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "3L7lhaRewyJl"}, "__type": "ArgType"}, "3L7lhaRewyJl": {"name": "text", "__type": "Text"}, "cl4rbn1DKCdg": {"name": "bool", "__type": "BoolType"}, "TUIxyk5nox5c": {"code": "false", "fallback": null, "__type": "CustomCode"}, "Fdm5kSmFzYOl": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "-oAZm0PcE-lt", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "5BPdpX9qxZ4t"}], "__type": "TplTag"}, "5BPdpX9qxZ4t": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {"className": {"__ref": "415H6EXB1nm-"}}, "rs": {"__ref": "MtvPM4B1xhDl"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MtvPM4B1xhDl": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "50px", "height": "50px", "max-width": "50px", "min-width": "50px", "min-height": "50px", "max-height": "50px", "background": "linear-gradient(#FFC1C1, #FFC1C1)"}, "mixins": [], "__type": "RuleSet"}, "415H6EXB1nm-": {"text": ["red-box"], "__type": "TemplatedString"}, "GtNB6ykibwpx": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "6uSnTY7RNGWq", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "GeeJ2AAxL23h"}], "__type": "TplTag"}, "GeeJ2AAxL23h": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "5AoxdcxPI27D"}, "dataCond": null, "dataRep": null, "text": {"__ref": "D-qR1MchNOSl"}, "columnsConfig": null, "__type": "VariantSetting"}, "5AoxdcxPI27D": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "D-qR1MchNOSl": {"expr": {"__ref": "K8-bgYP8eHvT"}, "html": false, "__type": "ExprText"}, "K8-bgYP8eHvT": {"code": "(`jquery: ${!$state.mounted ? \"mounting...\" : `red box width: ${$$.jquery(\".red-box\").get(0).getBoundingClientRect().width}`}`)", "fallback": {"__ref": "f04RZmzkP0qX"}, "__type": "CustomCode"}, "f04RZmzkP0qX": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "qzX_KOZa8W_G": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "eCr_We23uy6h", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "mWxWsBKf3roH"}], "__type": "TplTag"}, "mWxWsBKf3roH": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "1pBXBj9GyXm1"}, "dataCond": null, "dataRep": null, "text": {"__ref": "AZoIectKBwGy"}, "columnsConfig": null, "__type": "VariantSetting"}, "1pBXBj9GyXm1": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "AZoIectKBwGy": {"expr": {"__ref": "J0tmq6gpWQnP"}, "html": false, "__type": "ExprText"}, "J0tmq6gpWQnP": {"code": "(`lodash partition: ${JSON.stringify($$.lodash.partition([1, 2, 3, 4], n => n % 2))}`;)", "fallback": {"__ref": "qEUBu4ibpwJn"}, "__type": "CustomCode"}, "qEUBu4ibpwJn": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "eDH3neF0EPxT": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "a6OnixtR915N", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "WnUfibvIHGkg"}], "__type": "TplTag"}, "WnUfibvIHGkg": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "cSpRPY77CtZs"}, "dataCond": null, "dataRep": null, "text": {"__ref": "waSbZMNgSsXE"}, "columnsConfig": null, "__type": "VariantSetting"}, "cSpRPY77CtZs": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "waSbZMNgSsXE": {"expr": {"__ref": "NH-PljBby4IT"}, "html": false, "__type": "ExprText"}, "NH-PljBby4IT": {"code": "(`marked: ${$$.marked(`This text is ***really important***`).trim()}`)", "fallback": {"__ref": "d-9u_ROl8UYu"}, "__type": "CustomCode"}, "d-9u_ROl8UYu": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "BylsKX3Xpmaf": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "IMV4jzvbyvMR", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "CNAuDYXOG3Ep"}], "__type": "TplTag"}, "CNAuDYXOG3Ep": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "ROdeP35sx57p"}, "dataCond": null, "dataRep": null, "text": {"__ref": "xw-sGK9n_Ugz"}, "columnsConfig": null, "__type": "VariantSetting"}, "ROdeP35sx57p": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "xw-sGK9n_Ugz": {"expr": {"__ref": "q8K5yZS7KyaK"}, "html": false, "__type": "ExprText"}, "q8K5yZS7KyaK": {"code": "(`MD5 hash: ${$$.md5(\"~~Super random text~~\")}`)", "fallback": {"__ref": "b-rTy_Xt4w2t"}, "__type": "CustomCode"}, "b-rTy_Xt4w2t": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "dXTbidJ3j01n": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "_1n6ZmHghMRE", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "rrKWFq-f-J2U"}], "__type": "TplTag"}, "rrKWFq-f-J2U": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "rXiEMrwoHdKt"}, "dataCond": null, "dataRep": null, "text": {"__ref": "0WQYMvQ57Idh"}, "columnsConfig": null, "__type": "VariantSetting"}, "rXiEMrwoHdKt": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "0WQYMvQ57Idh": {"expr": {"__ref": "RMw1ZREkAU6h"}, "html": false, "__type": "ExprText"}, "RMw1ZREkAU6h": {"code": "(`nanoid with single-character alphabet for stable results: ${$$.nanoid.customAlphabet(\"0\")(6)}`)", "fallback": {"__ref": "6lltx3SlmhR0"}, "__type": "CustomCode"}, "6lltx3SlmhR0": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "wkuMVdYIK9KL": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "KE_eS-ZXGsiW", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "wRJvdGmhgB2k"}], "__type": "TplTag"}, "wRJvdGmhgB2k": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "VKtHht5APC2q"}, "dataCond": null, "dataRep": null, "text": {"__ref": "yQ5BBkZNv1D8"}, "columnsConfig": null, "__type": "VariantSetting"}, "VKtHht5APC2q": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "VZPr57zkPFB8": {"name": null, "component": {"__xref": {"uuid": "7bfdcdde-66e0-469e-9022-02955a3f8f15", "iid": "IO7KN06x7tsN"}}, "uuid": "72V5HeNejsDD", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "5sUd4c573Ojw"}], "__type": "TplComponent"}, "5sUd4c573Ojw": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [{"__ref": "9flQLjuIl2eQ"}], "attrs": {}, "rs": {"__ref": "09lLIZUcEAY0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9flQLjuIl2eQ": {"param": {"__xref": {"uuid": "7bfdcdde-66e0-469e-9022-02955a3f8f15", "iid": "JvX8g98s8CCN"}}, "expr": {"__ref": "QnHnambpv9HD"}, "__type": "Arg"}, "09lLIZUcEAY0": {"values": {"max-width": "100%", "object-fit": "cover", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "QnHnambpv9HD": {"interactions": [{"__ref": "hvrJ-FdTznyE"}], "__type": "EventHandler"}, "hvrJ-FdTznyE": {"interactionName": "Update mounted", "actionName": "updateVariable", "args": [{"__ref": "dcGmA874wd8x"}, {"__ref": "48D7RxeYaXdu"}, {"__ref": "HMfyNiJwRcJl"}], "condExpr": null, "conditionalMode": "always", "uuid": "7DM2zPBcnQAM", "parent": {"__ref": "QnHnambpv9HD"}, "__type": "Interaction"}, "dcGmA874wd8x": {"name": "variable", "expr": {"__ref": "bqDIB46YXveC"}, "__type": "NameArg"}, "48D7RxeYaXdu": {"name": "operation", "expr": {"__ref": "HDgXfCiXrC4V"}, "__type": "NameArg"}, "HMfyNiJwRcJl": {"name": "value", "expr": {"__ref": "3tuoX6Sow1bV"}, "__type": "NameArg"}, "bqDIB46YXveC": {"path": ["$state", "mounted"], "fallback": null, "__type": "ObjectPath"}, "HDgXfCiXrC4V": {"code": "0", "fallback": null, "__type": "CustomCode"}, "3tuoX6Sow1bV": {"code": "(true)", "fallback": null, "__type": "CustomCode"}, "haxzZ8r7pFzz": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "5tJIGUAC_nSv", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "jvgqSlffPGxG"}], "__type": "TplTag"}, "jvgqSlffPGxG": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "aAnv4KE9z2Tm"}, "dataCond": null, "dataRep": null, "text": {"__ref": "93vp2VUhg-xc"}, "columnsConfig": null, "__type": "VariantSetting"}, "aAnv4KE9z2Tm": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "93vp2VUhg-xc": {"expr": {"__ref": "0xOW1QPO54wE"}, "html": false, "__type": "ExprText"}, "0xOW1QPO54wE": {"code": "(`pluralize \"house\": \"${$$.pluralize(\"house\", 2)}\"`)", "fallback": {"__ref": "gbNGhq0XsUxa"}, "__type": "CustomCode"}, "gbNGhq0XsUxa": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "y_b2HY480hF4": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "5myGMshdy11C", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "G1bBSfWRxHWa"}], "__type": "TplTag"}, "G1bBSfWRxHWa": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "OtsY0WBrsvJS"}, "dataCond": null, "dataRep": null, "text": {"__ref": "4Ft8IJhnZjaM"}, "columnsConfig": null, "__type": "VariantSetting"}, "OtsY0WBrsvJS": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "4Ft8IJhnZjaM": {"expr": {"__ref": "FFO383_EdJUv"}, "html": false, "__type": "ExprText"}, "FFO383_EdJUv": {"code": "(class ConstRNG extends $$.randomExtras.RNG {\n  get name() { return 'default'; }\n  next() { return 0.1;}\n  clone() { return new ConstRNG() }\n}\n\n$$.random.use(new ConstRNG())\nreturn `random: ${$$.random.int(50, 200)}`;)", "fallback": {"__ref": "c9pzuy0rrF_T"}, "__type": "CustomCode"}, "c9pzuy0rrF_T": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "KmQ54ZOjb4Tb": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "nQiNa3f084UI", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "EXN5rzyqlEx8"}], "__type": "TplTag"}, "EXN5rzyqlEx8": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "RdSZVIOLAAHS"}, "dataCond": null, "dataRep": null, "text": {"__ref": "rsNyBf7LzzOc"}, "columnsConfig": null, "__type": "VariantSetting"}, "RdSZVIOLAAHS": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "rsNyBf7LzzOc": {"expr": {"__ref": "ptUDcVBJNZQL"}, "html": false, "__type": "ExprText"}, "ptUDcVBJNZQL": {"code": "(`semver: ${$$.semver.inc(\"3.2.1\", \"minor\")}`)", "fallback": {"__ref": "a98Uc0e-VSAJ"}, "__type": "CustomCode"}, "a98Uc0e-VSAJ": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "L5z0U_TjSR5-": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "HPF4e7ipqouG", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "imHOcASXxPmX"}], "__type": "TplTag"}, "imHOcASXxPmX": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "ozSW1OFM2uC_"}, "dataCond": null, "dataRep": null, "text": {"__ref": "pndSf1M-8oDp"}, "columnsConfig": null, "__type": "VariantSetting"}, "ozSW1OFM2uC_": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "pndSf1M-8oDp": {"expr": {"__ref": "pHW-6fMuYA7a"}, "html": false, "__type": "ExprText"}, "pHW-6fMuYA7a": {"code": "(`tinycolor2: ${$$.tinycolor2({ r: 255, g: 0, b: 0 })}`)", "fallback": {"__ref": "57_5BHKKkD_N"}, "__type": "CustomCode"}, "57_5BHKKkD_N": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "kEIT9W3d7-FQ": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "JkP7e23FiZjl", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "bODXF46cTcBp"}], "__type": "TplTag"}, "bODXF46cTcBp": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "OLEpmOLeIvfv"}, "dataCond": null, "dataRep": null, "text": {"__ref": "xXniNWeFG6LP"}, "columnsConfig": null, "__type": "VariantSetting"}, "OLEpmOLeIvfv": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "xXniNWeFG6LP": {"expr": {"__ref": "cehvQddKDngn"}, "html": false, "__type": "ExprText"}, "cehvQddKDngn": {"code": "(`uuid NIL: ${$$.uuid.NIL}, validate: ${$$.uuid.validate($$.uuid.v4())}`)", "fallback": {"__ref": "HP3v35fpBPIB"}, "__type": "CustomCode"}, "HP3v35fpBPIB": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "td8Blm8kozNk": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "KOuUvNS16UgD", "parent": {"__ref": "HrtMv6GQLRZI"}, "locked": null, "vsettings": [{"__ref": "gSTtGnB7r7x5"}], "__type": "TplTag"}, "gSTtGnB7r7x5": {"variants": [{"__ref": "hVgCKqmRll81"}], "args": [], "attrs": {}, "rs": {"__ref": "r2KBVvvJQ-Sy"}, "dataCond": null, "dataRep": null, "text": {"__ref": "JELp2yCg1TP3"}, "columnsConfig": null, "__type": "VariantSetting"}, "r2KBVvvJQ-Sy": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "JELp2yCg1TP3": {"expr": {"__ref": "-lB_Zzn4_WzT"}, "html": false, "__type": "ExprText"}, "-lB_Zzn4_WzT": {"code": "(const mySchema = $$.zod.object({\n  username: $$.zod.string(),\n});\n\n`zod parse valid: ${JSON.stringify(mySchema.parse({ username: \"Test\" }))}, safeParse with invalid data success: ${mySchema.safeParse({ username: 123 }).success}`;\n)", "fallback": {"__ref": "INX0b72gJQv0"}, "__type": "CustomCode"}, "INX0b72gJQv0": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "yQ5BBkZNv1D8": {"expr": {"__ref": "g7LwHi3SCGu8"}, "html": false, "__type": "ExprText"}, "g7LwHi3SCGu8": {"code": "(const dataStr = `Column 1,Column 2,Column 3,Column 4\n1-1,1-2,1-3,1-4\n2-1,2-2,2-3,2-4\n3-1,3-2,3-3,3-4\n4,5,6,7`;\nconst parsedData = $$.papaParse.parse(dataStr);\n`papaparse: ${parsedData.data.length} rows, ${parsedData.data[0].length} cols`)", "fallback": {"__ref": "EwevVaU5xuS1"}, "__type": "CustomCode"}, "EwevVaU5xuS1": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "kaLmR87q1RlP": {"expr": {"__ref": "AAydMXuAIrH9"}, "html": false, "__type": "ExprText"}, "AAydMXuAIrH9": {"code": "(if (!globalThis.testAxiosPromise) {\n    globalThis.testAxiosResult = \"loading...\"\n    globalThis.testAxiosPromise = $$.axios(\"https://api.publicapis.org/entries?title=cats\").then((res) => {\n        if (typeof window !== \"undefined\") {\n            globalThis.testAxiosResult = res.data.entries[0].Category;\n            $state.forceRerenderCount++;\n        }\n    });\n}\n`Axios response: \"${globalThis.testAxiosResult}\"`)", "fallback": {"__ref": "7JohYpgBDUkp"}, "__type": "CustomCode"}, "7JohYpgBDUkp": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "uDyPx2kK-3wh": {"expr": {"__ref": "JHC6Av4HZcj5"}, "html": false, "__type": "ExprText"}, "JHC6Av4HZcj5": {"code": "(/*\nDepending on: https://linear.app/plasmic/issue/PLA-10153\nif (!globalThis.testIsomorphicFetchPromise) {\n    globalThis.testIsomorphicFetchResult = \"loading...\"\n    globalThis.testIsomorphicFetchPromise = $$.fetch(\"https://api.publicapis.org/entries?title=cats\").then((res) => res.json()).then((res) => {\n        if (typeof window !== \"undefined\") {\n            globalThis.testIsomorphicFetchResult = res.entries[0].Category;\n            $state.forceRerenderCount++;\n        }\n    });\n}\n`Isomorphic-fetch response: \"${globalThis.testIsomorphicFetchResult}\"`\n*/\nreturn \"TODO: isomorphic-fetch\")", "fallback": {"__ref": "YV6ktF54R-Z8"}, "__type": "CustomCode"}, "YV6ktF54R-Z8": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}}, "deps": ["220522b3-6ffd-4033-bed2-11941b5fdc8d", "c54f9d3e-56d6-4751-baa0-8b2a93ae8e0c", "6a1dbdef-31d7-48d6-af32-426d27ae140d", "b12aeddb-2fe6-4c26-8574-36f3b8b72685", "2e3e634b-0431-4dc5-842b-a652da3a62ca", "822c0aa6-7b6c-4ddf-b468-e1583a1ee933", "5399a8a4-8a49-4637-a6bb-b1e3e071c73f", "65946b0b-b4c4-492a-80ee-a7736ae3a735", "388ed04e-61fd-4211-8b2d-352ee2cfd5b8", "c128d63f-01ec-4d8b-a3d9-f1991d044290", "6cd11ba1-885b-4a6c-82fa-fbf5829e5ce5", "13bb2354-599c-43e0-a9b8-c4f3733e78f5", "ea7d9df6-a1da-4893-8bcc-a04096a23010", "de7a14b8-3322-4ce1-9b9a-cc95f35bfd4b", "e19497fe-1015-4c06-bb56-693e52674193", "6718f4f5-7b8d-4fd0-a544-9ba8a7269ac4", "f28b72d1-77e8-4010-9170-a783c5560c08", "d52194cc-ad47-46c8-8d5d-955ad010b9e7", "17eae61b-545f-4d2a-a33e-7637d79212ba", "ea54091c-dcfb-47e2-86b4-fe360c62f6ec", "7bfdcdde-66e0-469e-9022-02955a3f8f15"], "version": "245-code-component-meta-add-refActions"}]]