// @ts-nocheck
/* eslint-disable */
/* tslint:disable */
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: 2gYaa1FsuykK8CmmDLsakd
// Component: dLse-ef_CN

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/router";

import * as p from "@plasmicapp/react-web";
import * as ph from "@plasmicapp/react-web/lib/host";

import { usePlasmicDataSourceContext } from "@plasmicapp/data-sources-context";
import {
  usePlasmicDataConfig,
  executePlasmicDataOp,
  usePlasmicDataOp,
} from "@plasmicapp/react-web/lib/data-sources";

import {
  hasVariant,
  classNames,
  wrapWithClassName,
  createPlasmicElementProxy,
  makeFragment,
  MultiChoiceArg,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  pick,
  omit,
  useTrigger,
  StrictProps,
  deriveRenderOpts,
  ensureGlobalVariants,
} from "@plasmicapp/react-web";
import PageLayout from "../../PageLayout"; // plasmic-import: gQYo0-cHLDSSGC/component
import { AntdCheckbox } from "@plasmicpkgs/antd5/skinny/registerCheckbox"; // plasmic-import: aFHKFWNvs7/codeComponent
import { FormWrapper } from "@plasmicpkgs/antd5/skinny/registerForm"; // plasmic-import: TgJFzUZpvQ/codeComponent
import { AntdButton } from "@plasmicpkgs/antd5/skinny/registerButton"; // plasmic-import: bx9Xzvf5_eu/codeComponent
import { Fetcher } from "@plasmicapp/react-web/lib/data-sources"; // plasmic-import: zjI_-5P37VCl92/codeComponent

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_antd_5_hostless_css from "../antd_5_hostless/plasmic_antd_5_hostless.module.css"; // plasmic-import: ohDidvG9XsCeFumugENU3J/projectcss
import plasmic_plasmic_rich_components_css from "../plasmic_rich_components/plasmic_plasmic_rich_components.module.css"; // plasmic-import: jkU633o1Cz7HrJdwdxhVHk/projectcss
import projectcss from "./plasmic_supabase_auth.module.css"; // plasmic-import: 2gYaa1FsuykK8CmmDLsakd/projectcss
import sty from "./PlasmicSsr.module.css"; // plasmic-import: dLse-ef_CN/css

export type PlasmicSsr__VariantMembers = {};
export type PlasmicSsr__VariantsArgs = {};
type VariantPropType = keyof PlasmicSsr__VariantsArgs;
export const PlasmicSsr__VariantProps = new Array<VariantPropType>();

export type PlasmicSsr__ArgsType = {};
type ArgPropType = keyof PlasmicSsr__ArgsType;
export const PlasmicSsr__ArgProps = new Array<ArgPropType>();

export type PlasmicSsr__OverridesType = {
  root?: p.Flex<"div">;
  pageLayout?: p.Flex<typeof PageLayout>;
  section?: p.Flex<"section">;
  h3?: p.Flex<"h3">;
  checkbox?: p.Flex<typeof AntdCheckbox>;
  form?: p.Flex<typeof FormWrapper>;
  button?: p.Flex<typeof AntdButton>;
};

export interface DefaultSsrProps {}

const __wrapUserFunction =
  globalThis.__PlasmicWrapUserFunction ?? ((loc, fn) => fn());
const __wrapUserPromise =
  globalThis.__PlasmicWrapUserPromise ??
  (async (loc, promise) => {
    return await promise;
  });

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSsr__RenderFunc(props: {
  variants: PlasmicSsr__VariantsArgs;
  args: PlasmicSsr__ArgsType;
  overrides: PlasmicSsr__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;
  const __nextRouter = useNextRouter();

  const $ctx = ph.useDataEnv?.() || {};
  const args = React.useMemo(() => Object.assign({}, props.args), [props.args]);
  const $props = {
    ...args,
    ...variants,
  };
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const currentUser = p.useCurrentUser?.() || {};

  const [$queries, setDollarQueries] = React.useState({});

  const stateSpecs = React.useMemo(
    () => [
      {
        path: "form.value",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
      {
        path: "checkbox[].checked",
        type: "private",
        variableType: "boolean",
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = p.useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries,
    $refs,
  });
  const dataSourcesCtx = usePlasmicDataSourceContext();
  const { cache, mutate: swrMutate } = usePlasmicDataConfig();
  const mutate = swrMutate;

  const new$Queries = {
    todos: usePlasmicDataOp(
      (() => {
        try {
          return {
            sourceId: "n3qrDyDvVso8Mp2RkKk12n",
            opId: "3e841457-d91f-4191-9e29-50a93571ffb6",
            userArgs: {},
            cacheKey: "plasmic.$.167X-Xs6Y.$.",
            invalidatedKeys: null,
            roleId: "122c4676-991a-4fa8-887c-2d5489ecbbe4",
          };
        } catch (e) {
          if (
            e instanceof TypeError ||
            e?.plasmicType === "PlasmicUndefinedDataError"
          ) {
            return undefined;
          } else {
            throw e;
          }
        }
      })()
    ),
  };
  if (Object.keys(new$Queries).some((k) => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);
  }

  return (
    <React.Fragment>
      <Head></Head>

      <style>{`
        body {
          margin: 0;
        }
      `}</style>

      <div className={projectcss.plasmic_page_wrapper}>
        <div
          data-plasmic-name={"root"}
          data-plasmic-override={overrides.root}
          data-plasmic-root={true}
          data-plasmic-for-node={forNode}
          className={classNames(
            projectcss.all,
            projectcss.root_reset,
            projectcss.plasmic_default_styles,
            projectcss.plasmic_mixins,
            projectcss.plasmic_tokens,
            plasmic_antd_5_hostless_css.plasmic_tokens,
            plasmic_plasmic_rich_components_css.plasmic_tokens,
            sty.root
          )}
        >
          <PageLayout
            data-plasmic-name={"pageLayout"}
            data-plasmic-override={overrides.pageLayout}
          >
            <ph.DataCtxReader>
              {($ctx) => (
                <section
                  data-plasmic-name={"section"}
                  data-plasmic-override={overrides.section}
                  className={classNames(projectcss.all, sty.section)}
                >
                  <div
                    className={classNames(projectcss.all, sty.freeBox___8OpC9)}
                  >
                    <h3
                      data-plasmic-name={"h3"}
                      data-plasmic-override={overrides.h3}
                      className={classNames(
                        projectcss.all,
                        projectcss.h3,
                        projectcss.__wab_text,
                        sty.h3
                      )}
                    >
                      {"Todo list"}
                    </h3>
                    {(
                      (() => {
                        try {
                          return $queries.todos.data;
                        } catch (e) {
                          if (
                            e instanceof TypeError ||
                            e?.plasmicType === "PlasmicUndefinedDataError"
                          ) {
                            return [];
                          }
                          throw e;
                        }
                      })() ?? []
                    ).map((todo, currentIndex) => (
                      <div
                        className={classNames(
                          projectcss.all,
                          sty.freeBox__tifO0
                        )}
                        key={currentIndex}
                      >
                        <div
                          className={classNames(
                            projectcss.all,
                            projectcss.__wab_text,
                            sty.text__knZkz
                          )}
                        >
                          <React.Fragment>
                            {(() => {
                              try {
                                return todo.description;
                              } catch (e) {
                                if (
                                  e instanceof TypeError ||
                                  e?.plasmicType === "PlasmicUndefinedDataError"
                                ) {
                                  return "";
                                }
                                throw e;
                              }
                            })()}
                          </React.Fragment>
                        </div>
                        {(() => {
                          const child$Props = {
                            checked: p.generateStateValueProp($state, [
                              "checkbox",
                              currentIndex,
                              "checked",
                            ]),
                            className: classNames(
                              "__wab_instance",
                              sty.checkbox
                            ),
                            defaultChecked: (() => {
                              try {
                                return todo.done;
                              } catch (e) {
                                if (
                                  e instanceof TypeError ||
                                  e?.plasmicType === "PlasmicUndefinedDataError"
                                ) {
                                  return undefined;
                                }
                                throw e;
                              }
                            })(),
                            onChange: p.generateStateOnChangeProp($state, [
                              "checkbox",
                              currentIndex,
                              "checked",
                            ]),
                          };
                          p.initializeCodeComponentStates(
                            $state,
                            [
                              {
                                name: "checked",
                                plasmicStateName: "checkbox[].checked",
                              },
                            ],
                            [currentIndex],
                            undefined ?? {},
                            child$Props
                          );
                          p.initializePlasmicStates(
                            $state,
                            [
                              {
                                name: "checkbox[].checked",
                                initFunc: ({ $props, $state, $queries }) =>
                                  (() => {
                                    try {
                                      return todo.done;
                                    } catch (e) {
                                      if (
                                        e instanceof TypeError ||
                                        e?.plasmicType ===
                                          "PlasmicUndefinedDataError"
                                      ) {
                                        return undefined;
                                      }
                                      throw e;
                                    }
                                  })(),
                              },
                            ],
                            [currentIndex]
                          );
                          return (
                            <AntdCheckbox
                              data-plasmic-name={"checkbox"}
                              data-plasmic-override={overrides.checkbox}
                              {...child$Props}
                            >
                              <div
                                className={classNames(
                                  projectcss.all,
                                  projectcss.__wab_text,
                                  sty.text__m3P63
                                )}
                              >
                                {"Done"}
                              </div>
                            </AntdCheckbox>
                          );
                        })()}
                      </div>
                    ))}
                  </div>
                  {true ? (
                    <div
                      className={classNames(projectcss.all, sty.freeBox__fdpKh)}
                    >
                      <FormWrapper
                        data-plasmic-name={"form"}
                        data-plasmic-override={overrides.form}
                        className={classNames("__wab_instance", sty.form)}
                        extendedOnValuesChange={p.generateStateOnChangeProp(
                          $state,
                          ["form", "value"]
                        )}
                        formItems={(() => {
                          const __composite = [
                            { label: null, name: null, inputType: null },
                            { label: null, name: null, inputType: null },
                          ];
                          __composite["0"]["label"] = "Description";
                          __composite["0"]["name"] = "description";
                          __composite["0"]["inputType"] = "Text Area";
                          __composite["1"]["label"] = "Done";
                          __composite["1"]["name"] = "done";
                          __composite["1"]["inputType"] = "Checkbox";
                          return __composite;
                        })()}
                        labelCol={{ span: 8, horizontalOnly: true }}
                        layout={"vertical" as const}
                        mode={"simplified" as const}
                        submitSlot={
                          <AntdButton
                            data-plasmic-name={"button"}
                            data-plasmic-override={overrides.button}
                            className={classNames("__wab_instance", sty.button)}
                            onClick={async () => {
                              const $steps = {};
                              $steps["tutorialdbCreate"] = true
                                ? (() => {
                                    const actionArgs = {
                                      dataOp: __wrapUserFunction(
                                        {
                                          type: "InteractionArgLoc",
                                          actionName: "dataSourceOp",
                                          interactionUuid: "loWFJ-TMx",
                                          componentUuid: "dLse-ef_CN",
                                          argName: "dataOp",
                                        },
                                        () => ({
                                          sourceId: "n3qrDyDvVso8Mp2RkKk12n",
                                          opId: "2513138d-0056-4403-8acb-5e36939dec7f",
                                          userArgs: {
                                            variables: [
                                              $state.form.value.description,
                                            ],
                                          },
                                          cacheKey: null,
                                          invalidatedKeys: [
                                            "plasmic_refresh_all",
                                          ],
                                          roleId:
                                            "122c4676-991a-4fa8-887c-2d5489ecbbe4",
                                        })
                                      ),
                                    };
                                    return __wrapUserFunction(
                                      {
                                        type: "InteractionLoc",
                                        actionName: "dataSourceOp",
                                        interactionUuid: "loWFJ-TMx",
                                        componentUuid: "dLse-ef_CN",
                                      },
                                      () =>
                                        (async ({
                                          dataOp,
                                          continueOnError,
                                        }) => {
                                          try {
                                            const response =
                                              await executePlasmicDataOp(
                                                dataOp,
                                                {
                                                  userAuthToken:
                                                    dataSourcesCtx?.userAuthToken,
                                                  user: dataSourcesCtx?.user,
                                                }
                                              );
                                            if (
                                              dataOp.invalidatedKeys &&
                                              dataOp.invalidatedKeys.find(
                                                (key) =>
                                                  key === "plasmic_refresh_all"
                                              )
                                            ) {
                                              await Promise.all(
                                                Array.from(cache.keys()).map(
                                                  async (key) => mutate(key)
                                                )
                                              );
                                              return response;
                                            }
                                            if (dataOp.invalidatedKeys) {
                                              await Promise.all(
                                                dataOp.invalidatedKeys.map(
                                                  async (invalidateKey) =>
                                                    Promise.all(
                                                      Array.from(
                                                        cache.keys()
                                                      ).map(async (key) => {
                                                        if (
                                                          typeof key ===
                                                            "string" &&
                                                          key.includes(
                                                            `.$.${invalidateKey}.$.`
                                                          )
                                                        ) {
                                                          return mutate(key);
                                                        }
                                                        return Promise.resolve();
                                                      })
                                                    )
                                                )
                                              );
                                            }
                                            return response;
                                          } catch (e) {
                                            if (!continueOnError) {
                                              throw e;
                                            }
                                            return e;
                                          }
                                        })?.apply(null, [actionArgs]),
                                      actionArgs
                                    );
                                  })()
                                : undefined;
                              if (
                                typeof $steps["tutorialdbCreate"] ===
                                  "object" &&
                                typeof $steps["tutorialdbCreate"].then ===
                                  "function"
                              ) {
                                $steps["tutorialdbCreate"] =
                                  await __wrapUserPromise(
                                    {
                                      type: "InteractionLoc",
                                      actionName: "dataSourceOp",
                                      interactionUuid: "loWFJ-TMx",
                                      componentUuid: "dLse-ef_CN",
                                    },
                                    $steps["tutorialdbCreate"]
                                  );
                              }
                            }}
                            submitsForm={true}
                            type={"primary" as const}
                          >
                            <div
                              className={classNames(
                                projectcss.all,
                                projectcss.__wab_text,
                                sty.text___4NkC
                              )}
                            >
                              {"Create new todo"}
                            </div>
                          </AntdButton>
                        }
                        wrapperCol={{ span: 16, horizontalOnly: true }}
                      />
                    </div>
                  ) : null}
                </section>
              )}
            </ph.DataCtxReader>
          </PageLayout>
        </div>
      </div>
    </React.Fragment>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "pageLayout", "section", "h3", "checkbox", "form", "button"],
  pageLayout: ["pageLayout", "section", "h3", "checkbox", "form", "button"],
  section: ["section", "h3", "checkbox", "form", "button"],
  h3: ["h3"],
  checkbox: ["checkbox"],
  form: ["form", "button"],
  button: ["button"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  pageLayout: typeof PageLayout;
  section: "section";
  h3: "h3";
  checkbox: typeof AntdCheckbox;
  form: typeof FormWrapper;
  button: typeof AntdButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSsr__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSsr__VariantsArgs;
    args?: PlasmicSsr__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSsr__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicSsr__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: [...PlasmicDescendants[nodeName]],
          internalArgPropNames: PlasmicSsr__ArgProps,
          internalVariantPropNames: PlasmicSsr__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicSsr__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicSsr";
  } else {
    func.displayName = `PlasmicSsr.${nodeName}`;
  }
  return func;
}

function withPlasmicPageGuard<P extends object>(
  WrappedComponent: React.ComponentType<P>
) {
  const PageGuard: React.FC<P> = (props) => (
    <p.PlasmicPageGuard
      minRole={"122c4676-991a-4fa8-887c-2d5489ecbbe4"}
      appId={"2gYaa1FsuykK8CmmDLsakd"}
      authorizeEndpoint={"https://studio.plasmic.app/authorize"}
      canTriggerLogin={false}
    >
      <WrappedComponent {...props} />
    </p.PlasmicPageGuard>
  );

  return PageGuard;
}

export const PlasmicSsr = Object.assign(
  // Top-level PlasmicSsr renders the root element
  withPlasmicPageGuard(makeNodeComponent("root")),
  {
    // Helper components rendering sub-elements
    pageLayout: makeNodeComponent("pageLayout"),
    section: makeNodeComponent("section"),
    h3: makeNodeComponent("h3"),
    checkbox: makeNodeComponent("checkbox"),
    form: makeNodeComponent("form"),
    button: makeNodeComponent("button"),

    // Metadata about props expected for PlasmicSsr
    internalVariantProps: PlasmicSsr__VariantProps,
    internalArgProps: PlasmicSsr__ArgProps,

    // Page metadata
    pageMetadata: {
      title: "",
      description: "",
      ogImageSrc: "",
      canonical: "",
    },
  }
);

export default PlasmicSsr;
/* prettier-ignore-end */
