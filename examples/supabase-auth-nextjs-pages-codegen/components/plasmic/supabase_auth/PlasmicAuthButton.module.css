.root {
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: flex-start;
  align-items: center;
  min-width: 0;
  padding: 8px;
}
.freeBox___82Umc {
  display: flex;
  position: relative;
  flex-direction: row;
  width: auto;
  height: auto;
  max-width: 100%;
}
.freeBox___82Umc > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  margin-left: calc(0px - 8px);
  width: calc(100% + 8px);
}
.freeBox___82Umc > :global(.__wab_flex-container) > *,
.freeBox___82Umc > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox___82Umc > :global(.__wab_flex-container) > picture > img,
.freeBox___82Umc
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: 8px;
}
.text__aCq2U {
  width: auto;
  height: auto;
  max-width: 100%;
  color: var(--token-zy6xhYnWF8Y7);
}
.logoutBtn:global(.__wab_instance) {
  max-width: 100%;
  object-fit: cover;
  position: relative;
}
.text__f530Z {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.freeBox__nzFoq {
  display: flex;
  position: relative;
  flex-direction: row;
  width: auto;
  height: auto;
  max-width: 100%;
}
.freeBox__nzFoq > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  margin-left: calc(0px - 8px);
  width: calc(100% + 8px);
}
.freeBox__nzFoq > :global(.__wab_flex-container) > *,
.freeBox__nzFoq > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox__nzFoq > :global(.__wab_flex-container) > picture > img,
.freeBox__nzFoq
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: 8px;
}
.loginBtn:global(.__wab_instance) {
  max-width: 100%;
  object-fit: cover;
  position: relative;
}
.text__btXwf {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
