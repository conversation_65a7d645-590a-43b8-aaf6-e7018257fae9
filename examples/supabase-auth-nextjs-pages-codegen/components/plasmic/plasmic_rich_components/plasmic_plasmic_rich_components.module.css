.plasmic_tokens {
  --token-L7dndXjC5Kpy: var(--antd-colorPrimary);
  --plasmic-token-system-primary: var(--token-L7dndXjC5Kpy);
  --token-jl4No1xTHeNZ: var(--antd-colorSuccess);
  --plasmic-token-system-success: var(--token-jl4No1xTHeNZ);
  --token-Now1bq9CP9om: var(--antd-colorWarning);
  --plasmic-token-system-warning: var(--token-Now1bq9CP9om);
  --token-MUywrh7yUXU4: var(--antd-colorError);
  --plasmic-token-system-error: var(--token-MUywrh7yUXU4);
  --token-G7-1LxsPbxMN: var(--antd-colorInfo);
  --plasmic-token-system-info: var(--token-G7-1LxsPbxMN);
  --token-3NCAjaVr7NDw: var(--antd-colorText);
  --plasmic-token-system-text: var(--token-3NCAjaVr7NDw);
  --token-MqfBJxIomHsP: var(--antd-colorTextSecondary);
  --plasmic-token-system-text-secondary: var(--token-MqfBJxIomHsP);
  --token-zCNbPmsEHKHI: var(--antd-colorTextTertiary);
  --plasmic-token-system-text-tertiary: var(--token-zCNbPmsEHKHI);
  --token-ul1cD4QIhyP3: var(--antd-colorTextQuaternary);
  --plasmic-token-system-text-quaternary: var(--token-ul1cD4QIhyP3);
  --token-OJVSLjx6EX74: var(--antd-colorBorder);
  --plasmic-token-system-border: var(--token-OJVSLjx6EX74);
  --token-3N9uR9_kZJno: var(--antd-colorBorderSecondary);
  --plasmic-token-system-border-secondary: var(--token-3N9uR9_kZJno);
  --token-QDlAQUi3SEFJ: var(--antd-colorFill);
  --plasmic-token-system-fill: var(--token-QDlAQUi3SEFJ);
  --token-sbLcXAxlx-YH: var(--antd-colorFillSecondary);
  --plasmic-token-system-fill-secondary: var(--token-sbLcXAxlx-YH);
  --token-IOi5Dt4kX0fO: var(--antd-colorFillTertiary);
  --plasmic-token-system-fill-tertiary: var(--token-IOi5Dt4kX0fO);
  --token-eWvxZjHKMbWm: var(--antd-colorFillQuaternary);
  --plasmic-token-system-fill-quaternary: var(--token-eWvxZjHKMbWm);
  --token-wNbqyq0xI1z2: var(--antd-colorBgLayout);
  --plasmic-token-system-bg-layout: var(--token-wNbqyq0xI1z2);
  --token-Tfbxlb0F2dcO: var(--antd-colorBgContainer);
  --plasmic-token-system-bg-container: var(--token-Tfbxlb0F2dcO);
  --token-aA1k7o3K3v7E: var(--antd-colorBgElevated);
  --plasmic-token-system-bg-elevated: var(--token-aA1k7o3K3v7E);
  --token-1YVVEp57xTbH: var(--antd-colorBgSpotlight);
  --plasmic-token-system-bg-spotlight: var(--token-1YVVEp57xTbH);
  --token-TXTQAQurVinS: var(--antd-colorPrimaryBg);
  --plasmic-token-system-primary-bg: var(--token-TXTQAQurVinS);
  --token-Ueq1imuLWzWB: var(--antd-colorPrimaryBgHover);
  --plasmic-token-system-primary-bg-hover: var(--token-Ueq1imuLWzWB);
  --token-3Y2b4mad3tpr: var(--antd-colorPrimaryBorder);
  --plasmic-token-system-primary-border: var(--token-3Y2b4mad3tpr);
  --token-zG6bsJPKPM6G: var(--antd-colorPrimaryBorderHover);
  --plasmic-token-system-primary-border-hover: var(--token-zG6bsJPKPM6G);
  --token-CoIxVf33fjYR: var(--antd-colorPrimaryHover);
  --plasmic-token-system-primary-hover: var(--token-CoIxVf33fjYR);
  --token-VYKHjgvt3baV: var(--antd-colorPrimaryActive);
  --plasmic-token-system-primary-active: var(--token-VYKHjgvt3baV);
  --token-Ceis2wpshROe: var(--antd-colorPrimaryTextHover);
  --plasmic-token-system-primary-text-hover: var(--token-Ceis2wpshROe);
  --token-cQtzATPhD0yY: var(--antd-colorPrimaryText);
  --plasmic-token-system-primary-text: var(--token-cQtzATPhD0yY);
  --token-svk-C6VNkXld: var(--antd-colorPrimaryTextActive);
  --plasmic-token-system-primary-text-active: var(--token-svk-C6VNkXld);
  --token-eiLEAeAvRYow: var(--antd-colorSuccessBg);
  --plasmic-token-system-success-bg: var(--token-eiLEAeAvRYow);
  --token-3-aYLZS6bASk: var(--antd-colorSuccessBgHover);
  --plasmic-token-system-success-bg-hover: var(--token-3-aYLZS6bASk);
  --token-ttdOZaL973AS: var(--antd-colorSuccessBorder);
  --plasmic-token-system-success-border: var(--token-ttdOZaL973AS);
  --token-HSyBAyh40ZSg: var(--antd-colorSuccessBorderHover);
  --plasmic-token-system-success-border-hover: var(--token-HSyBAyh40ZSg);
  --token-nM6GRn59Q_v_: var(--antd-colorSuccessHover);
  --plasmic-token-system-success-hover: var(--token-nM6GRn59Q_v_);
  --token-EobLc69YjvoC: var(--antd-colorSuccessActive);
  --plasmic-token-system-success-active: var(--token-EobLc69YjvoC);
  --token-TxMYYub7qpFv: var(--antd-colorSuccessTextHover);
  --plasmic-token-system-success-text-hover: var(--token-TxMYYub7qpFv);
  --token-UvzcBZsqXJID: var(--antd-colorSuccessText);
  --plasmic-token-system-success-text: var(--token-UvzcBZsqXJID);
  --token-y0h3Oin2UCCg: var(--antd-colorSuccessTextActive);
  --plasmic-token-system-success-text-active: var(--token-y0h3Oin2UCCg);
  --token-T86hDKfiip0s: var(--antd-colorWarningBg);
  --plasmic-token-system-warning-bg: var(--token-T86hDKfiip0s);
  --token-C3fqz3nOjb1t: var(--antd-colorWarningBgHover);
  --plasmic-token-system-warning-bg-hover: var(--token-C3fqz3nOjb1t);
  --token-5znIhhSsa1SZ: var(--antd-colorWarningBorder);
  --plasmic-token-system-warning-border: var(--token-5znIhhSsa1SZ);
  --token-iPU6BDSElThA: var(--antd-colorWarningBorderHover);
  --plasmic-token-system-warning-border-hover: var(--token-iPU6BDSElThA);
  --token-CvX6DGdWw0AT: var(--antd-colorWarningHover);
  --plasmic-token-system-warning-hover: var(--token-CvX6DGdWw0AT);
  --token-AVqO54txUK8v: var(--antd-colorWarningActive);
  --plasmic-token-system-warning-active: var(--token-AVqO54txUK8v);
  --token-9MTnmjTfHUcx: var(--antd-colorWarningTextHover);
  --plasmic-token-system-warning-text-hover: var(--token-9MTnmjTfHUcx);
  --token-NiUcrCx-GH7L: var(--antd-colorWarningText);
  --plasmic-token-system-warning-text: var(--token-NiUcrCx-GH7L);
  --token-zxEsoy4Pdoll: var(--antd-colorWarningTextActive);
  --plasmic-token-system-warning-text-active: var(--token-zxEsoy4Pdoll);
  --token-plffEnS5KNJz: var(--antd-colorInfoBg);
  --plasmic-token-system-info-bg: var(--token-plffEnS5KNJz);
  --token-XCtaE8rwyRcH: var(--antd-colorInfoBgHover);
  --plasmic-token-system-info-bg-hover: var(--token-XCtaE8rwyRcH);
  --token-ygH7JwyC0498: var(--antd-colorInfoBorder);
  --plasmic-token-system-info-border: var(--token-ygH7JwyC0498);
  --token-jJPlBVl3-Wdo: var(--antd-colorInfoBorderHover);
  --plasmic-token-system-info-border-hover: var(--token-jJPlBVl3-Wdo);
  --token-tdP5BYw5yHGs: var(--antd-colorInfoHover);
  --plasmic-token-system-info-hover: var(--token-tdP5BYw5yHGs);
  --token-utaEsVudZukD: var(--antd-colorInfoActive);
  --plasmic-token-system-info-active: var(--token-utaEsVudZukD);
  --token-QrsAGO2JxptN: var(--antd-colorInfoTextHover);
  --plasmic-token-system-info-text-hover: var(--token-QrsAGO2JxptN);
  --token-QZexpRqvQk6i: var(--antd-colorInfoText);
  --plasmic-token-system-info-text: var(--token-QZexpRqvQk6i);
  --token-UMPMgFm8WzfF: var(--antd-colorInfoTextActive);
  --plasmic-token-system-info-text-active: var(--token-UMPMgFm8WzfF);
  --token-DPZ8I9JQqKuy: var(--antd-colorErrorBg);
  --plasmic-token-system-error-bg: var(--token-DPZ8I9JQqKuy);
  --token-pXbcJ4-Dwgal: var(--antd-colorErrorBgHover);
  --plasmic-token-system-error-bg-hover: var(--token-pXbcJ4-Dwgal);
  --token-soUSk4Q7UU3k: var(--antd-colorErrorBorder);
  --plasmic-token-system-error-border: var(--token-soUSk4Q7UU3k);
  --token-0WmmJKd6y8QB: var(--antd-colorErrorBorderHover);
  --plasmic-token-system-error-border-hover: var(--token-0WmmJKd6y8QB);
  --token-rx-ZAUQjgDMN: var(--antd-colorErrorHover);
  --plasmic-token-system-error-hover: var(--token-rx-ZAUQjgDMN);
  --token-GE3SUTkeB6UT: var(--antd-colorErrorActive);
  --plasmic-token-system-error-active: var(--token-GE3SUTkeB6UT);
  --token-QiW374JoPcBP: var(--antd-colorErrorTextHover);
  --plasmic-token-system-error-text-hover: var(--token-QiW374JoPcBP);
  --token-sRVz6punRNva: var(--antd-colorErrorText);
  --plasmic-token-system-error-text: var(--token-sRVz6punRNva);
  --token-mguUuWIjva-K: var(--antd-colorErrorTextActive);
  --plasmic-token-system-error-text-active: var(--token-mguUuWIjva-K);
  --token-LWbg9fWguJI3: var(--antd-colorWhite);
  --plasmic-token-system-white: var(--token-LWbg9fWguJI3);
  --token-wBm6eYlYM3Zc: var(--antd-colorBgMask);
  --plasmic-token-system-bg-mask: var(--token-wBm6eYlYM3Zc);
  --token-Nn1jCA1FwWIa: var(--antd-colorIcon);
  --plasmic-token-system-icon: var(--token-Nn1jCA1FwWIa);
  --token-H00cJtVGlxr3: var(--antd-colorIconHover);
  --plasmic-token-system-icon-hover: var(--token-H00cJtVGlxr3);
  --token-n-yIwEBjbiOm: var(--antd-colorLink);
  --plasmic-token-system-link: var(--token-n-yIwEBjbiOm);
  --token-BX3ZN9wALylT: var(--antd-colorLinkHover);
  --plasmic-token-system-link-hover: var(--token-BX3ZN9wALylT);
  --token-b_8eiuEJ3SPV: var(--antd-paddingXXS);
  --plasmic-token-system-padding-xxs: var(--token-b_8eiuEJ3SPV);
  --token-oczojNz2r0X8: var(--antd-paddingXS);
  --plasmic-token-system-padding-xs: var(--token-oczojNz2r0X8);
  --token-zH85E1Av1Pv3: var(--antd-paddingSM);
  --plasmic-token-system-padding-sm: var(--token-zH85E1Av1Pv3);
  --token-zwpWpnntuAB2: var(--antd-padding);
  --plasmic-token-system-padding-m: var(--token-zwpWpnntuAB2);
  --token-dM4wviNfUM54: var(--antd-paddingMD);
  --plasmic-token-system-padding-md: var(--token-dM4wviNfUM54);
  --token-lmW-tjePDQLh: var(--antd-paddingLG);
  --plasmic-token-system-padding-lg: var(--token-lmW-tjePDQLh);
  --token-lJZIixe8Y5i0: var(--antd-paddingXL);
  --plasmic-token-system-padding-xl: var(--token-lJZIixe8Y5i0);
  --token-MI3HaXlGr9Kl: var(--antd-marginXXS);
  --plasmic-token-system-margin-xxs: var(--token-MI3HaXlGr9Kl);
  --token-gaqg5OuiNd0O: var(--antd-marginXS);
  --plasmic-token-system-margin-xs: var(--token-gaqg5OuiNd0O);
  --token-CUFwnxFn33aU: var(--antd-marginSM);
  --plasmic-token-system-margin-sm: var(--token-CUFwnxFn33aU);
  --token-rTAQWPSFAOaL: var(--antd-margin);
  --plasmic-token-system-margin-m: var(--token-rTAQWPSFAOaL);
  --token-qqpOGelncSrZ: var(--antd-marginMD);
  --plasmic-token-system-margin-md: var(--token-qqpOGelncSrZ);
  --token-wrWyDbIAVLJd: var(--antd-marginLG);
  --plasmic-token-system-margin-lg: var(--token-wrWyDbIAVLJd);
  --token-YWfUU08xljqE: var(--antd-marginXL);
  --plasmic-token-system-margin-xl: var(--token-YWfUU08xljqE);
  --token-pP1q6GiQcuNU: var(--antd-marginXXL);
  --plasmic-token-system-margin-xxl: var(--token-pP1q6GiQcuNU);
  --token-LR96Vo-6x_h3: var(--antd-fontSize);
  --plasmic-token-system-m: var(--token-LR96Vo-6x_h3);
  --token-AsVyYsQxfCaA: var(--antd-fontSizeSM);
  --plasmic-token-system-sm: var(--token-AsVyYsQxfCaA);
  --token-eEwLS7YNsqde: var(--antd-fontSizeLG);
  --plasmic-token-system-lg: var(--token-eEwLS7YNsqde);
  --token-GYut2VMTH5pt: var(--antd-fontSizeXL);
  --plasmic-token-system-xl: var(--token-GYut2VMTH5pt);
  --token-dmnigInkcqrD: var(--antd-fontSizeHeading1);
  --plasmic-token-system-heading-1: var(--token-dmnigInkcqrD);
  --token-U5-nLftIVlWG: var(--antd-fontSizeHeading2);
  --plasmic-token-system-heading-2: var(--token-U5-nLftIVlWG);
  --token-_EDCSH5HhRRv: var(--antd-fontSizeHeading3);
  --plasmic-token-system-heading-3: var(--token-_EDCSH5HhRRv);
  --token-imk7VDpyZvwC: var(--antd-fontSizeHeading4);
  --plasmic-token-system-heading-4: var(--token-imk7VDpyZvwC);
  --token-UdskaI49wicV: var(--antd-fontSizeHeading5);
  --plasmic-token-system-heading-5: var(--token-UdskaI49wicV);
  --token-RXEIiRqqPu0C: var(--antd-lineHeight);
  --plasmic-token-system-m: var(--token-RXEIiRqqPu0C);
  --token-EOi-oQ4GqgVn: var(--antd-lineHeightLG);
  --plasmic-token-system-lg: var(--token-EOi-oQ4GqgVn);
  --token-RsvMZJHWim2g: var(--antd-lineHeightSM);
  --plasmic-token-system-sm: var(--token-RsvMZJHWim2g);
  --token-I7O8WLvjd9Fp: var(--antd-lineHeightHeading1);
  --plasmic-token-system-heading-1: var(--token-I7O8WLvjd9Fp);
  --token-9NGDVfNrUaWD: var(--antd-lineHeightHeading2);
  --plasmic-token-system-heading-2: var(--token-9NGDVfNrUaWD);
  --token-AMYZ6X915hOB: var(--antd-lineHeightHeading3);
  --plasmic-token-system-heading-3: var(--token-AMYZ6X915hOB);
  --token-H-84i59Ul8kH: var(--antd-lineHeightHeading4);
  --plasmic-token-system-heading-4: var(--token-H-84i59Ul8kH);
  --token-ZHLAl2AKUGhV: var(--antd-lineHeightHeading5);
  --plasmic-token-system-heading-5: var(--token-ZHLAl2AKUGhV);
}

:where(.all) {
  display: block;
  white-space: inherit;
  grid-row: auto;
  grid-column: auto;
  position: relative;
  background: none;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  box-shadow: none;
  box-sizing: border-box;
  text-decoration-line: none;
  margin: 0;
  border-width: 0px;
}
:where(.__wab_expr_html_text *) {
  white-space: inherit;
  grid-row: auto;
  grid-column: auto;
  background: none;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  box-shadow: none;
  box-sizing: border-box;
  text-decoration-line: none;
  margin: 0;
  border-width: 0px;
}

:where(.img) {
  display: inline-block;
}
:where(.__wab_expr_html_text img) {
  white-space: inherit;
}

:where(.li) {
  display: list-item;
}
:where(.__wab_expr_html_text li) {
  white-space: inherit;
}

:where(.span) {
  display: inline;
  position: static;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
}
:where(.__wab_expr_html_text span) {
  white-space: inherit;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
}

:where(.input) {
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
  background-image: linear-gradient(#ffffff, #ffffff);
  padding: 2px;
  border: 1px solid lightgray;
}
:where(.__wab_expr_html_text input) {
  white-space: inherit;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
  background-image: linear-gradient(#ffffff, #ffffff);
  padding: 2px;
  border: 1px solid lightgray;
}

:where(.textarea) {
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
  padding: 2px;
  border: 1px solid lightgray;
}
:where(.__wab_expr_html_text textarea) {
  white-space: inherit;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
  padding: 2px;
  border: 1px solid lightgray;
}

:where(.button) {
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
  background-image: none;
  align-items: flex-start;
  text-align: center;
  padding: 2px 6px;
  border: 1px solid lightgray;
}
:where(.__wab_expr_html_text button) {
  white-space: inherit;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
  background-image: none;
  align-items: flex-start;
  text-align: center;
  padding: 2px 6px;
  border: 1px solid lightgray;
}

:where(.code) {
  font-family: inherit;
  line-height: inherit;
}
:where(.__wab_expr_html_text code) {
  white-space: inherit;
  font-family: inherit;
  line-height: inherit;
}

:where(.pre) {
  font-family: inherit;
  line-height: inherit;
}
:where(.__wab_expr_html_text pre) {
  white-space: inherit;
  font-family: inherit;
  line-height: inherit;
}

:where(.p) {
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
}
:where(.__wab_expr_html_text p) {
  white-space: inherit;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
}

:where(.h1) {
  font-size: inherit;
  font-weight: inherit;
}
:where(.__wab_expr_html_text h1) {
  white-space: inherit;
  font-size: inherit;
  font-weight: inherit;
}

:where(.h2) {
  font-size: inherit;
  font-weight: inherit;
}
:where(.__wab_expr_html_text h2) {
  white-space: inherit;
  font-size: inherit;
  font-weight: inherit;
}

:where(.h3) {
  font-size: inherit;
  font-weight: inherit;
}
:where(.__wab_expr_html_text h3) {
  white-space: inherit;
  font-size: inherit;
  font-weight: inherit;
}

:where(.h4) {
  font-size: inherit;
  font-weight: inherit;
}
:where(.__wab_expr_html_text h4) {
  white-space: inherit;
  font-size: inherit;
  font-weight: inherit;
}

:where(.h5) {
  font-size: inherit;
  font-weight: inherit;
}
:where(.__wab_expr_html_text h5) {
  white-space: inherit;
  font-size: inherit;
  font-weight: inherit;
}

:where(.h6) {
  font-size: inherit;
  font-weight: inherit;
}
:where(.__wab_expr_html_text h6) {
  white-space: inherit;
  font-size: inherit;
  font-weight: inherit;
}

:where(.address) {
  font-style: inherit;
}
:where(.__wab_expr_html_text address) {
  white-space: inherit;
  font-style: inherit;
}

:where(.a) {
  color: inherit;
}
:where(.__wab_expr_html_text a) {
  white-space: inherit;
  color: inherit;
}

:where(.ol) {
  list-style-type: none;
  padding: 0;
}
:where(.__wab_expr_html_text ol) {
  white-space: inherit;
  list-style-type: none;
  padding: 0;
}

:where(.ul) {
  list-style-type: none;
  padding: 0;
}
:where(.__wab_expr_html_text ul) {
  white-space: inherit;
  list-style-type: none;
  padding: 0;
}

:where(.select) {
  padding: 2px 6px;
}
:where(.__wab_expr_html_text select) {
  white-space: inherit;
  padding: 2px 6px;
}

.plasmic_default__component_wrapper {
  display: grid;
}
.plasmic_default__inline {
  display: inline;
}
.plasmic_page_wrapper {
  display: flex;
  width: 100%;
  min-height: 100vh;
  align-items: stretch;
  align-self: start;
}
.plasmic_page_wrapper > * {
  height: auto !important;
}
.__wab_expr_html_text {
  white-space: normal;
}
:where(.root_reset) {
}
