/** @format */

import { initPlasmicLoader } from "@plasmicapp/loader-nextjs";
// import "@plasmicpkgs/react-awesome-reveal";
// import "@plasmicpkgs/react-parallax-tilt";
// import "@plasmicpkgs/react-scroll-parallax";
// import {
//   registerParallaxProvider,
//   registerParallaxWrapper,
// } from "@plasmicpkgs/react-scroll-parallax";
// import "@plasmicpkgs/react-slick";
// import {
//   registerFollowWrapper,
//   registerTimelineWrapper,
//   registerTweetWrapper,
// } from "@plasmicpkgs/react-twitter-widgets";
//
import { registerAll } from "@plasmicpkgs/commerce";
import { registerAll as registerShopifyProvider } from "@plasmicpkgs/commerce-shopify";
import { registerAll as registerSwellProvider } from "@plasmicpkgs/commerce-swell";
// import "@plasmicpkgs/framer-motion";
// import "@plasmicpkgs/lottie-react";
// import { registerLottieWrapper } from "@plasmicpkgs/lottie-react";
// import "@plasmicpkgs/plasmic-basic-components";
// import {
//   registerDataProvider,
//   registerDynamicImage,
//   registerDynamicRepeater,
//   registerDynamicText,
// } from "@plasmicpkgs/plasmic-basic-components";
// import "@plasmicpkgs/plasmic-query";
// import { registerDataFetcher } from "@plasmicpkgs/plasmic-query";
import {
  registerProductCollection,
  registerProductImage,
  registerProductPrice,
  registerProductTitle,
  registerShopifyCredentialsProvider,
  registerShopifyProduct,
} from "@plasmicpkgs/plasmic-shopify";

export const PLASMIC = initPlasmicLoader({
  projects: [
    {
      id: "cgHPK27WAUJzZiS37DyptC",
      token:
        "WJmfTQjq5BBrXFRqSUz85QrzlPxpVBY6ZkSNj2DOikzCgNpStyme1DrEWI11f5hDJv3mjv9n8rvcmn3TfQA",
    },
  ],

  // By default Plasmic will use the last published version of your project.
  // For development, you can set preview to true, which will use the unpublished
  // project, allowing you to see your designs without publishing.  Please
  // only use this for development, as this is significantly slower.
  preview: false,
});
//
// // You can register any code components that you want to use here; see
// // https://docs.plasmic.app/learn/code-components-ref/
// // And configure your Plasmic project to use the host url pointing at
// // the /plasmic-host page of your nextjs app (for example,
// // http://localhost:3000/plasmic-host).  See
// // https://docs.plasmic.app/learn/app-hosting/#set-a-plasmic-project-to-use-your-app-host
//
// // PLASMIC.registerComponent(...);
//
// registerParallaxProvider(PLASMIC);
// registerParallaxWrapper(PLASMIC);
//
// registerLottieWrapper(PLASMIC);
//
// registerTimelineWrapper(PLASMIC);
// registerTweetWrapper(PLASMIC);
// registerFollowWrapper(PLASMIC);
//
// registerDataFetcher(PLASMIC);
//
// registerDynamicText(PLASMIC);
// registerDynamicImage(PLASMIC);
// registerDataProvider(PLASMIC);
// registerDynamicRepeater(PLASMIC);
//
// // registerNavProvider(PLASMIC);
// // registerNavMenu(PLASMIC);
// // registerNavToggle(PLASMIC);

registerShopifyCredentialsProvider(PLASMIC);
registerShopifyProduct(PLASMIC);
registerProductCollection(PLASMIC);
registerProductTitle(PLASMIC);
registerProductImage(PLASMIC);
registerProductPrice(PLASMIC);

registerAll(PLASMIC);
registerShopifyProvider(PLASMIC);
registerSwellProvider(PLASMIC);
