set -e
p=$1
yarn gen-meta

plasmic='~/plasmic/public-packages/@plasmicapp/cli/dist/index.js'
#plasmic='plasmic'

#yarn bundle-antd
#eval $plasmic upload-bundle --bundleJsFile build/static/js/*.js --metaJsonFile /tmp/antd-meta.json --project $p --bundleName antd --cssFiles node_modules/antd/dist/antd.min.css --genCssPaths antd/dist/antd.min.css --pkgVersion 4.3.5

#yarn bundle-strap
#eval $plasmic upload-bundle --bundleJsFile build/static/js/*.js --metaJsonFile /tmp/strap-meta.json --project $p --bundleName strap --genModulePath react-bootstrap --pkgVersion 1.2.2

# yarn bundle-material
# eval $plasmic upload-bundle --bundleJsFile build/static/js/*.js --metaJsonFile /tmp/material-meta.json --project $p --bundleName material --genModulePath @material-ui/core --pkgVersion 4.11.0 --extraPropMetaJsonFile tools/material-extra.json --themeProviderWrapper withMaterialTheme --themeModuleFile src/component-imports/MaterialThemeModule.tsx

# yarn bundle-material-icons
# eval $plasmic upload-bundle --bundleJsFile build/static/js/*.js --metaJsonFile /tmp/material-icons-meta.json --project $p --bundleName material-icons --genModulePath @material-ui/icons --pkgVersion 4.9.1

yarn bundle-fetch
eval $plasmic upload-bundle --bundleJsFile build/static/js/*.js --metaJsonFile /tmp/fetch-meta.json --project $p --bundleName fetch --genModulePath react-fetch-component --pkgVersion 7.0.1
