#%%
import pandas as pd
import sqlalchemy as sa
import psycopg2 as pg
#%%
eng=sa.create_engine('postgresql://segment@localhost:5433/segment')
#%%
df=pd.read_sql('select received_at, user_id from app.tracks order by user_id, received_at', con=eng)
#%%
# Find out how much time between "sessions" - we simply only consider gaps of at least 12 hours to be different sessions

def agg(grp):
    diff = grp.received_at.diff()[1:]
    return diff[diff > pd.Timedelta('0.5 days')]

diff = df.groupby('user_id').apply(agg)
#%%
diff.describe()
#%%
[diff.quantile(q=q) for q in [.8,.9,.95,.99]]
#%%
